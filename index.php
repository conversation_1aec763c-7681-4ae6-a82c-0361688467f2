<!DOCTYPE html>
<html lang="ar">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>نظام إدارة العقارات - رافعة المدينة</title>
    <link href="https://fonts.googleapis.com/css2?family=Tajawal:wght@300;400;500;700&display=swap" rel="stylesheet">
    <style>
        * { margin: 0; padding: 0; box-sizing: border-box; }
        body { font-family: 'Tajawal', sans-serif; direction: rtl; background: #f5f5f5; color: #333; }
        .header { background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; padding: 2rem; text-align: center; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }
        .header h1 { font-size: 2.5rem; margin-bottom: 0.5rem; }
        .header p { font-size: 1.2rem; opacity: 0.9; }
        .nav-tabs { background: white; padding: 1rem; box-shadow: 0 2px 5px rgba(0,0,0,0.1); display: flex; gap: 1rem; overflow-x: auto; }
        .nav-tab { padding: 0.8rem 1.5rem; background: #f8f9fa; border: none; border-radius: 8px; cursor: pointer; transition: all 0.3s; white-space: nowrap; font-family: inherit; font-size: 1rem; }
        .nav-tab.active { background: #667eea; color: white; }
        .nav-tab:hover { background: #5a67d8; color: white; }
        .content { padding: 2rem; max-width: 1200px; margin: 0 auto; }
        .page { display: none; animation: fadeIn 0.3s ease-in; }
        .page.active { display: block; }
        @keyframes fadeIn { from { opacity: 0; transform: translateY(20px); } to { opacity: 1; transform: translateY(0); } }
        .stats-grid { display: grid; grid-template-columns: repeat(auto-fit, minmax(250px, 1fr)); gap: 1.5rem; margin-bottom: 2rem; }
        .stat-card { background: white; padding: 1.5rem; border-radius: 12px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); text-align: center; }
        .stat-card h3 { color: #667eea; font-size: 2rem; margin-bottom: 0.5rem; }
        .stat-card p { color: #666; font-size: 1rem; }
        .form-container { background: white; padding: 2rem; border-radius: 12px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); margin-bottom: 2rem; }
        .form-grid { display: grid; grid-template-columns: repeat(auto-fit, minmax(250px, 1fr)); gap: 1rem; margin-bottom: 1.5rem; }
        .form-group { display: flex; flex-direction: column; }
        .form-group label { margin-bottom: 0.5rem; font-weight: 500; color: #333; }
        .form-group input, .form-group select, .form-group textarea { padding: 0.8rem; border: 2px solid #e2e8f0; border-radius: 8px; font-family: inherit; font-size: 1rem; transition: border-color 0.3s; }
        .form-group input:focus, .form-group select:focus, .form-group textarea:focus { outline: none; border-color: #667eea; }
        .btn { padding: 0.8rem 1.5rem; border: none; border-radius: 8px; cursor: pointer; font-family: inherit; font-size: 1rem; transition: all 0.3s; }
        .btn-primary { background: #667eea; color: white; }
        .btn-primary:hover { background: #5a67d8; }
        .btn-danger { background: #e53e3e; color: white; }
        .btn-danger:hover { background: #c53030; }
        .btn-secondary { background: #718096; color: white; }
        .btn-secondary:hover { background: #4a5568; }
        .table-container { background: white; border-radius: 12px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); overflow: hidden; }
        .table { width: 100%; border-collapse: collapse; }
        .table th, .table td { padding: 1rem; text-align: right; border-bottom: 1px solid #e2e8f0; }
        .table th { background: #f8f9fa; font-weight: 600; color: #4a5568; }
        .table tr:hover { background: #f8f9fa; }
        .badge { padding: 0.3rem 0.8rem; border-radius: 20px; font-size: 0.8rem; font-weight: 500; }
        .badge-success { background: #c6f6d5; color: #22543d; }
        .badge-warning { background: #fef5e7; color: #744210; }
        .badge-danger { background: #fed7d7; color: #742a2a; }
        .welcome-grid { display: grid; grid-template-columns: repeat(auto-fit, minmax(300px, 1fr)); gap: 2rem; margin-top: 2rem; }
        .welcome-card { background: white; padding: 2rem; border-radius: 15px; box-shadow: 0 5px 20px rgba(0,0,0,0.1); text-align: center; cursor: pointer; transition: transform 0.3s ease; }
        .welcome-card:hover { transform: translateY(-5px); }
        .welcome-card-icon { width: 60px; height: 60px; margin: 0 auto 1rem; border-radius: 50%; display: flex; align-items: center; justify-content: center; font-size: 1.5rem; }
        .alert { padding: 1rem; border-radius: 8px; margin-bottom: 1rem; }
        .alert-success { background: #c6f6d5; color: #22543d; border: 1px solid #9ae6b4; }
        .alert-info { background: #bee3f8; color: #2a4365; border: 1px solid #90cdf4; }
        .alert-error { background: #fed7d7; color: #742a2a; border: 1px solid #feb2b2; }
        .loading { display: none; text-align: center; padding: 2rem; }
        .loading.show { display: block; }
        .server-status { position: fixed; top: 10px; left: 10px; padding: 0.5rem 1rem; border-radius: 20px; font-size: 0.8rem; z-index: 1000; }
        .server-status.online { background: #c6f6d5; color: #22543d; }
        .server-status.offline { background: #fed7d7; color: #742a2a; }
        @media (max-width: 768px) { .header h1 { font-size: 2rem; } .nav-tabs { padding: 0.5rem; } .content { padding: 1rem; } .form-grid { grid-template-columns: 1fr; } }
    </style>
</head>
<body>
    <!-- Server Status -->
    <div id="server-status" class="server-status offline">🔴 غير متصل</div>

    <div class="header">
        <h1>🏢 نظام إدارة العقارات</h1>
        <p>شركة رافعة المدينة للتطوير العقاري - نسخة السيرفر</p>
    </div>

    <div class="nav-tabs">
        <button class="nav-tab active" onclick="showPage('dashboard')">🏠 الرئيسية</button>
        <button class="nav-tab" onclick="showPage('properties')">🏢 العقارات</button>
        <button class="nav-tab" onclick="showPage('users')">👥 المستخدمين</button>
        <button class="nav-tab" onclick="showPage('invoices')">📄 المستخلصات</button>
        <button class="nav-tab" onclick="showPage('suppliers')">🏭 الموردين</button>
        <button class="nav-tab" onclick="showPage('procurement')">🛒 المشتريات</button>
        <button class="nav-tab" onclick="showPage('settings')">⚙️ الإعدادات</button>
    </div>

    <div class="content">
        <!-- Loading -->
        <div id="loading" class="loading">
            <p>⏳ جاري تحميل البيانات...</p>
        </div>

        <!-- Dashboard -->
        <div id="dashboard" class="page active">
            <div class="alert alert-success">
                <strong>مرحباً بك!</strong> نظام إدارة العقارات يعمل مع السيرفر. جميع البيانات محفوظة في ملفات على السيرفر.
            </div>
            <div class="stats-grid">
                <div class="stat-card"><h3 id="total-properties">0</h3><p>إجمالي العقارات</p></div>
                <div class="stat-card"><h3 id="total-users">0</h3><p>المستخدمين</p></div>
                <div class="stat-card"><h3 id="total-invoices">0</h3><p>المستخلصات</p></div>
                <div class="stat-card"><h3 id="total-suppliers">0</h3><p>الموردين</p></div>
            </div>
            <div class="welcome-grid">
                <div class="welcome-card" onclick="showPage('properties')">
                    <div class="welcome-card-icon" style="background: linear-gradient(135deg, #667eea, #764ba2); color: white;">🏢</div>
                    <h3>إدارة العقارات</h3><p>إضافة وإدارة العقارات والوحدات السكنية والتجارية</p>
                </div>
                <div class="welcome-card" onclick="showPage('users')">
                    <div class="welcome-card-icon" style="background: linear-gradient(135deg, #4facfe, #00f2fe); color: white;">👥</div>
                    <h3>إدارة المستخدمين</h3><p>إضافة وإدارة حسابات المستخدمين والصلاحيات</p>
                </div>
                <div class="welcome-card" onclick="showPage('invoices')">
                    <div class="welcome-card-icon" style="background: linear-gradient(135deg, #fa709a, #fee140); color: white;">📄</div>
                    <h3>المستخلصات</h3><p>إدارة المستخلصات المالية والفواتير</p>
                </div>
            </div>
        </div>

        <!-- Properties -->
        <div id="properties" class="page">
            <h2>إدارة العقارات</h2>
            <div class="form-container">
                <h3>إضافة عقار جديد</h3>
                <form id="property-form">
                    <div class="form-grid">
                        <div class="form-group"><label>عنوان العقار *</label><input type="text" id="property-title" required></div>
                        <div class="form-group"><label>نوع العقار</label><select id="property-type"><option value="شقة">شقة</option><option value="فيلا">فيلا</option><option value="محل تجاري">محل تجاري</option><option value="مكتب">مكتب</option><option value="أرض">أرض</option></select></div>
                        <div class="form-group"><label>الموقع</label><input type="text" id="property-location"></div>
                        <div class="form-group"><label>المساحة (م²)</label><input type="number" id="property-area"></div>
                        <div class="form-group"><label>السعر (ريال)</label><input type="number" id="property-price"></div>
                        <div class="form-group"><label>الحالة</label><select id="property-status"><option value="متاح">متاح</option><option value="محجوز">محجوز</option><option value="مباع">مباع</option></select></div>
                    </div>
                    <div class="form-group"><label>الوصف</label><textarea id="property-description" rows="3"></textarea></div>
                    <button type="submit" class="btn btn-primary">إضافة العقار</button>
                </form>
            </div>
            <div class="table-container">
                <table class="table">
                    <thead><tr><th>العنوان</th><th>النوع</th><th>الموقع</th><th>المساحة</th><th>السعر</th><th>الحالة</th><th>الإجراءات</th></tr></thead>
                    <tbody id="properties-table"></tbody>
                </table>
            </div>
        </div>

        <!-- Users -->
        <div id="users" class="page">
            <h2>إدارة المستخدمين</h2>
            <div class="form-container">
                <h3>إضافة مستخدم جديد</h3>
                <form id="user-form">
                    <div class="form-grid">
                        <div class="form-group"><label>الاسم *</label><input type="text" id="user-name" required></div>
                        <div class="form-group"><label>البريد الإلكتروني *</label><input type="email" id="user-email" required></div>
                        <div class="form-group"><label>رقم الهاتف</label><input type="tel" id="user-phone"></div>
                        <div class="form-group"><label>الدور الوظيفي</label><select id="user-role"><option value="مدير">مدير</option><option value="موظف مبيعات">موظف مبيعات</option><option value="محاسب">محاسب</option><option value="مشرف">مشرف</option></select></div>
                        <div class="form-group"><label>القسم</label><input type="text" id="user-department"></div>
                        <div class="form-group"><label>الحالة</label><select id="user-status"><option value="نشط">نشط</option><option value="غير نشط">غير نشط</option></select></div>
                    </div>
                    <button type="submit" class="btn btn-primary">إضافة المستخدم</button>
                </form>
            </div>
            <div class="table-container">
                <table class="table">
                    <thead><tr><th>الاسم</th><th>البريد الإلكتروني</th><th>الهاتف</th><th>الدور</th><th>القسم</th><th>الحالة</th><th>الإجراءات</th></tr></thead>
                    <tbody id="users-table"></tbody>
                </table>
            </div>
        </div>

        <!-- Invoices -->
        <div id="invoices" class="page">
            <h2>إدارة المستخلصات</h2>
            <div class="form-container">
                <h3>إضافة مستخلص جديد</h3>
                <form id="invoice-form">
                    <div class="form-grid">
                        <div class="form-group"><label>اسم العميل *</label><input type="text" id="invoice-customer" required></div>
                        <div class="form-group"><label>هاتف العميل</label><input type="tel" id="invoice-phone"></div>
                        <div class="form-group"><label>العقار</label><input type="text" id="invoice-property"></div>
                        <div class="form-group"><label>المبلغ (ريال) *</label><input type="number" id="invoice-amount" required></div>
                        <div class="form-group"><label>تاريخ الاستحقاق</label><input type="date" id="invoice-due-date"></div>
                        <div class="form-group"><label>الحالة</label><select id="invoice-status"><option value="معلق">معلق</option><option value="مدفوع">مدفوع</option><option value="مرفوض">مرفوض</option></select></div>
                    </div>
                    <button type="submit" class="btn btn-primary">إضافة المستخلص</button>
                </form>
            </div>
            <div class="table-container">
                <table class="table">
                    <thead><tr><th>رقم المستخلص</th><th>العميل</th><th>العقار</th><th>المبلغ</th><th>تاريخ الاستحقاق</th><th>الحالة</th><th>الإجراءات</th></tr></thead>
                    <tbody id="invoices-table"></tbody>
                </table>
            </div>
        </div>

        <!-- Suppliers -->
        <div id="suppliers" class="page">
            <h2>إدارة الموردين</h2>
            <div class="form-container">
                <h3>إضافة مورد جديد</h3>
                <form id="supplier-form">
                    <div class="form-grid">
                        <div class="form-group"><label>اسم المورد *</label><input type="text" id="supplier-name" required></div>
                        <div class="form-group"><label>البريد الإلكتروني</label><input type="email" id="supplier-email"></div>
                        <div class="form-group"><label>رقم الهاتف</label><input type="tel" id="supplier-phone"></div>
                        <div class="form-group"><label>العنوان</label><input type="text" id="supplier-address"></div>
                        <div class="form-group"><label>الفئة</label><select id="supplier-category"><option value="مواد البناء">مواد البناء</option><option value="كهرباء">كهرباء</option><option value="سباكة">سباكة</option><option value="دهانات">دهانات</option><option value="رخام وسيراميك">رخام وسيراميك</option><option value="مقاولات">مقاولات</option></select></div>
                        <div class="form-group"><label>الحالة</label><select id="supplier-status"><option value="نشط">نشط</option><option value="غير نشط">غير نشط</option></select></div>
                    </div>
                    <button type="submit" class="btn btn-primary">إضافة المورد</button>
                </form>
            </div>
            <div class="table-container">
                <table class="table">
                    <thead><tr><th>الاسم</th><th>الفئة</th><th>الهاتف</th><th>البريد الإلكتروني</th><th>الحالة</th><th>الإجراءات</th></tr></thead>
                    <tbody id="suppliers-table"></tbody>
                </table>
            </div>
        </div>

        <!-- Procurement -->
        <div id="procurement" class="page">
            <h2>إدارة المشتريات</h2>
            <div class="form-container">
                <h3>إضافة عنصر جديد</h3>
                <form id="procurement-form">
                    <div class="form-grid">
                        <div class="form-group"><label>اسم العنصر *</label><input type="text" id="procurement-item" required></div>
                        <div class="form-group"><label>الفئة</label><select id="procurement-category"><option value="مواد البناء">مواد البناء</option><option value="كهرباء">كهرباء</option><option value="سباكة">سباكة</option><option value="دهانات">دهانات</option><option value="أدوات">أدوات</option><option value="أخرى">أخرى</option></select></div>
                        <div class="form-group"><label>المورد</label><input type="text" id="procurement-supplier"></div>
                        <div class="form-group"><label>الكمية *</label><input type="number" id="procurement-quantity" required></div>
                        <div class="form-group"><label>سعر الوحدة</label><input type="number" step="0.01" id="procurement-price"></div>
                        <div class="form-group"><label>الحالة</label><select id="procurement-status"><option value="معلق">معلق</option><option value="قيد التنفيذ">قيد التنفيذ</option><option value="تم التسليم">تم التسليم</option></select></div>
                    </div>
                    <button type="submit" class="btn btn-primary">إضافة العنصر</button>
                </form>
            </div>
            <div class="table-container">
                <table class="table">
                    <thead><tr><th>العنصر</th><th>الفئة</th><th>المورد</th><th>الكمية</th><th>السعر الإجمالي</th><th>الحالة</th><th>الإجراءات</th></tr></thead>
                    <tbody id="procurement-table"></tbody>
                </table>
            </div>
        </div>

        <!-- Settings -->
        <div id="settings" class="page">
            <h2>الإعدادات</h2>
            <div class="form-container">
                <h3>إدارة البيانات</h3>
                <div class="form-grid">
                    <div class="form-group"><label>نسخة احتياطية</label><button type="button" class="btn btn-secondary" onclick="createBackup()">إنشاء نسخة احتياطية</button></div>
                    <div class="form-group"><label>استيراد البيانات</label><input type="file" id="import-file" accept=".json"><button type="button" class="btn btn-secondary" onclick="importData()">استيراد البيانات</button></div>
                    <div class="form-group"><label>مسح جميع البيانات</label><button type="button" class="btn btn-danger" onclick="clearAllData()">مسح جميع البيانات</button></div>
                </div>
            </div>
            <div class="form-container">
                <h3>معلومات النظام</h3>
                <div class="alert alert-info">
                    <strong>إصدار النظام:</strong> 2.1.0 (نسخة السيرفر)<br>
                    <strong>تاريخ آخر تحديث:</strong> <span id="last-update"></span><br>
                    <strong>حالة السيرفر:</strong> <span id="server-info">🟢 متصل</span><br>
                    <strong>عدد العقارات:</strong> <span id="settings-properties">0</span><br>
                    <strong>عدد المستخدمين:</strong> <span id="settings-users">0</span><br>
                    <strong>عدد المستخلصات:</strong> <span id="settings-invoices">0</span><br>
                    <strong>عدد الموردين:</strong> <span id="settings-suppliers">0</span><br>
                    <strong>عدد المشتريات:</strong> <span id="settings-procurement">0</span>
                </div>
            </div>
        </div>
    </div>

    <script>
        // API Configuration
        const API_BASE = './api.php';
        let isOnline = false;

        // API Helper Functions
        async function apiCall(action, type = '', data = null, id = null) {
            showLoading(true);
            try {
                let url = `${API_BASE}?action=${action}`;
                if (type) url += `&type=${type}`;
                if (id) url += `&id=${id}`;

                const options = {
                    method: data ? 'POST' : 'GET',
                    headers: {
                        'Content-Type': 'application/json',
                    }
                };

                if (data) {
                    options.body = JSON.stringify(data);
                }

                const response = await fetch(url, options);
                const result = await response.json();

                if (!response.ok) {
                    throw new Error(result.error || 'خطأ في الخادم');
                }

                updateServerStatus(true);
                return result;
            } catch (error) {
                updateServerStatus(false);
                showNotification('خطأ في الاتصال: ' + error.message, 'error');
                throw error;
            } finally {
                showLoading(false);
            }
        }

        // Server Status
        function updateServerStatus(online) {
            isOnline = online;
            const statusEl = document.getElementById('server-status');
            const serverInfoEl = document.getElementById('server-info');

            if (online) {
                statusEl.className = 'server-status online';
                statusEl.textContent = '🟢 متصل';
                if (serverInfoEl) serverInfoEl.textContent = '🟢 متصل';
            } else {
                statusEl.className = 'server-status offline';
                statusEl.textContent = '🔴 غير متصل';
                if (serverInfoEl) serverInfoEl.textContent = '🔴 غير متصل';
            }
        }

        // Loading
        function showLoading(show) {
            const loadingEl = document.getElementById('loading');
            if (show) {
                loadingEl.classList.add('show');
            } else {
                loadingEl.classList.remove('show');
            }
        }

        // Data Storage
        let properties = [];
        let users = [];
        let invoices = [];
        let suppliers = [];
        let procurement = [];

        // Navigation
        function showPage(pageId) {
            document.querySelectorAll('.page').forEach(page => page.classList.remove('active'));
            document.querySelectorAll('.nav-tab').forEach(tab => tab.classList.remove('active'));
            document.getElementById(pageId).classList.add('active');
            event.target.classList.add('active');
            loadPageData(pageId);
        }

        async function loadPageData(pageId) {
            try {
                switch(pageId) {
                    case 'dashboard': await loadDashboard(); break;
                    case 'properties': await loadProperties(); break;
                    case 'users': await loadUsers(); break;
                    case 'invoices': await loadInvoices(); break;
                    case 'suppliers': await loadSuppliers(); break;
                    case 'procurement': await loadProcurement(); break;
                    case 'settings': await loadSettings(); break;
                }
            } catch (error) {
                console.error('Error loading page data:', error);
            }
        }

        // Dashboard
        async function loadDashboard() {
            try {
                const stats = await apiCall('stats');
                document.getElementById('total-properties').textContent = stats.properties || 0;
                document.getElementById('total-users').textContent = stats.users || 0;
                document.getElementById('total-invoices').textContent = stats.invoices || 0;
                document.getElementById('total-suppliers').textContent = stats.suppliers || 0;
            } catch (error) {
                console.error('Error loading dashboard:', error);
            }
        }

        // Properties
        async function loadProperties() {
            try {
                properties = await apiCall('get', 'properties');
                const tbody = document.getElementById('properties-table');
                tbody.innerHTML = properties.map(property => `
                    <tr>
                        <td>${property.title}</td>
                        <td>${property.type}</td>
                        <td>${property.location || 'غير محدد'}</td>
                        <td>${property.area || 'غير محدد'} م²</td>
                        <td>${property.price || 'غير محدد'} ريال</td>
                        <td><span class="badge badge-${property.status === 'متاح' ? 'success' : property.status === 'محجوز' ? 'warning' : 'danger'}">${property.status}</span></td>
                        <td><button class="btn btn-danger" onclick="deleteProperty('${property.id}')">حذف</button></td>
                    </tr>
                `).join('');
            } catch (error) {
                console.error('Error loading properties:', error);
            }
        }

        document.getElementById('property-form').addEventListener('submit', async function(e) {
            e.preventDefault();
            try {
                const property = {
                    title: document.getElementById('property-title').value,
                    type: document.getElementById('property-type').value,
                    location: document.getElementById('property-location').value,
                    area: document.getElementById('property-area').value,
                    price: document.getElementById('property-price').value,
                    status: document.getElementById('property-status').value,
                    description: document.getElementById('property-description').value
                };

                const result = await apiCall('add', 'properties', property);
                this.reset();
                await loadProperties();
                showNotification(result.message || 'تم إضافة العقار بنجاح');
            } catch (error) {
                console.error('Error adding property:', error);
            }
        });

        async function deleteProperty(id) {
            if (confirm('هل أنت متأكد من حذف هذا العقار؟')) {
                try {
                    const result = await apiCall('delete', 'properties', null, id);
                    await loadProperties();
                    showNotification(result.message || 'تم حذف العقار بنجاح');
                } catch (error) {
                    console.error('Error deleting property:', error);
                }
            }
        }

        // Users
        async function loadUsers() {
            try {
                users = await apiCall('get', 'users');
                const tbody = document.getElementById('users-table');
                tbody.innerHTML = users.map(user => `
                    <tr>
                        <td>${user.name}</td>
                        <td>${user.email}</td>
                        <td>${user.phone || 'غير محدد'}</td>
                        <td>${user.role}</td>
                        <td>${user.department || 'غير محدد'}</td>
                        <td><span class="badge badge-${user.status === 'نشط' ? 'success' : 'danger'}">${user.status}</span></td>
                        <td><button class="btn btn-danger" onclick="deleteUser('${user.id}')">حذف</button></td>
                    </tr>
                `).join('');
            } catch (error) {
                console.error('Error loading users:', error);
            }
        }

        document.getElementById('user-form').addEventListener('submit', async function(e) {
            e.preventDefault();
            try {
                const user = {
                    name: document.getElementById('user-name').value,
                    email: document.getElementById('user-email').value,
                    phone: document.getElementById('user-phone').value,
                    role: document.getElementById('user-role').value,
                    department: document.getElementById('user-department').value,
                    status: document.getElementById('user-status').value
                };

                const result = await apiCall('add', 'users', user);
                this.reset();
                await loadUsers();
                showNotification(result.message || 'تم إضافة المستخدم بنجاح');
            } catch (error) {
                console.error('Error adding user:', error);
            }
        });

        async function deleteUser(id) {
            if (confirm('هل أنت متأكد من حذف هذا المستخدم؟')) {
                try {
                    const result = await apiCall('delete', 'users', null, id);
                    await loadUsers();
                    showNotification(result.message || 'تم حذف المستخدم بنجاح');
                } catch (error) {
                    console.error('Error deleting user:', error);
                }
            }
        }

        // Invoices
        async function loadInvoices() {
            try {
                invoices = await apiCall('get', 'invoices');
                const tbody = document.getElementById('invoices-table');
                tbody.innerHTML = invoices.map(invoice => `
                    <tr>
                        <td>#${invoice.id}</td>
                        <td>${invoice.customer}</td>
                        <td>${invoice.property || 'غير محدد'}</td>
                        <td>${invoice.amount} ريال</td>
                        <td>${invoice.dueDate || 'غير محدد'}</td>
                        <td><span class="badge badge-${invoice.status === 'مدفوع' ? 'success' : invoice.status === 'معلق' ? 'warning' : 'danger'}">${invoice.status}</span></td>
                        <td><button class="btn btn-danger" onclick="deleteInvoice('${invoice.id}')">حذف</button></td>
                    </tr>
                `).join('');
            } catch (error) {
                console.error('Error loading invoices:', error);
            }
        }

        document.getElementById('invoice-form').addEventListener('submit', async function(e) {
            e.preventDefault();
            try {
                const invoice = {
                    customer: document.getElementById('invoice-customer').value,
                    phone: document.getElementById('invoice-phone').value,
                    property: document.getElementById('invoice-property').value,
                    amount: document.getElementById('invoice-amount').value,
                    dueDate: document.getElementById('invoice-due-date').value,
                    status: document.getElementById('invoice-status').value
                };

                const result = await apiCall('add', 'invoices', invoice);
                this.reset();
                await loadInvoices();
                showNotification(result.message || 'تم إضافة المستخلص بنجاح');
            } catch (error) {
                console.error('Error adding invoice:', error);
            }
        });

        async function deleteInvoice(id) {
            if (confirm('هل أنت متأكد من حذف هذا المستخلص؟')) {
                try {
                    const result = await apiCall('delete', 'invoices', null, id);
                    await loadInvoices();
                    showNotification(result.message || 'تم حذف المستخلص بنجاح');
                } catch (error) {
                    console.error('Error deleting invoice:', error);
                }
            }
        }

        function showNotification(message, type = 'success') {
            const notification = document.createElement('div');
            notification.className = `alert alert-${type}`;
            notification.textContent = message;
            notification.style.position = 'fixed';
            notification.style.top = '20px';
            notification.style.right = '20px';
            notification.style.zIndex = '1000';
            notification.style.minWidth = '300px';
            document.body.appendChild(notification);
            setTimeout(() => notification.remove(), 3000);
        }

        // Initialize
        document.addEventListener('DOMContentLoaded', async function() {
            await loadDashboard();
            showNotification('مرحباً بك في نظام إدارة العقارات - نسخة السيرفر!');
        });
    </script>
</body>
</html>
