
import React, { useState } from "react";
import MainLayout from "@/components/layout/MainLayout";
import { Button } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import {
  Table,
  TableBody,
  TableCaption,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { Badge } from "@/components/ui/badge";
import { Search, Plus, FileText, Trash2, Edit, Eye } from "lucide-react";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";

const suppliersData = [
  {
    id: "SUP-001",
    name: "شركة المدينة للمواد الإنشائية",
    contact: "محمد أحمد",
    phone: "0555123456",
    email: "<EMAIL>",
    category: "مواد بناء",
    status: "نشط",
  },
  {
    id: "SUP-002",
    name: "مؤسسة الأندلس للمقاولات",
    contact: "خالد سعيد",
    phone: "0567891234",
    email: "<EMAIL>",
    category: "مقاولات",
    status: "نشط",
  },
  {
    id: "SUP-003",
    name: "شركة التقنية للأنظمة الكهربائية",
    contact: "سامي عبدالله",
    phone: "0512345678",
    email: "<EMAIL>",
    category: "كهرباء",
    status: "نشط",
  },
  {
    id: "SUP-004",
    name: "مؤسسة الجزيرة للسباكة",
    contact: "فهد عبدالرحمن",
    phone: "0599123456",
    email: "<EMAIL>",
    category: "سباكة",
    status: "غير نشط",
  },
  {
    id: "SUP-005",
    name: "شركة النور للدهانات",
    contact: "أحمد محمد",
    phone: "0545678912",
    email: "<EMAIL>",
    category: "دهانات",
    status: "نشط",
  },
  {
    id: "SUP-006",
    name: "مؤسسة الفارس للرخام والسيراميك",
    contact: "عبدالله سعد",
    phone: "0523456781",
    email: "<EMAIL>",
    category: "رخام وسيراميك",
    status: "غير نشط",
  },
];

const categoryCount = {
  "مواد بناء": 2,
  "مقاولات": 1,
  "كهرباء": 1,
  "سباكة": 1,
  "دهانات": 1,
  "رخام وسيراميك": 1,
};

const Suppliers: React.FC = () => {
  const [searchQuery, setSearchQuery] = useState("");
  
  const filteredSuppliers = suppliersData.filter(supplier => 
    supplier.name.includes(searchQuery) || 
    supplier.id.includes(searchQuery) ||
    supplier.category.includes(searchQuery)
  );

  return (
    <MainLayout>
      <div className="space-y-6">
        <div className="flex justify-between items-center">
          <h1 className="text-2xl font-bold">الموردين</h1>
          <div className="flex gap-3">
            <Button variant="outline" className="gap-2">
              <FileText size={18} />
              تصدير القائمة
            </Button>
            <Button className="gap-2">
              <Plus size={18} />
              إضافة مورد جديد
            </Button>
          </div>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
          <Card>
            <CardHeader className="pb-2">
              <CardTitle className="text-sm font-medium text-muted-foreground">
                إجمالي الموردين
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{suppliersData.length}</div>
            </CardContent>
          </Card>
          <Card>
            <CardHeader className="pb-2">
              <CardTitle className="text-sm font-medium text-muted-foreground">
                الموردين النشطين
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">
                {suppliersData.filter(s => s.status === "نشط").length}
              </div>
              <p className="text-xs text-muted-foreground mt-1">
                {((suppliersData.filter(s => s.status === "نشط").length / suppliersData.length) * 100).toFixed(0)}% من إجمالي الموردين
              </p>
            </CardContent>
          </Card>
          <Card>
            <CardHeader className="pb-2">
              <CardTitle className="text-sm font-medium text-muted-foreground">
                الموردين غير النشطين
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">
                {suppliersData.filter(s => s.status === "غير نشط").length}
              </div>
              <p className="text-xs text-muted-foreground mt-1">
                {((suppliersData.filter(s => s.status === "غير نشط").length / suppliersData.length) * 100).toFixed(0)}% من إجمالي الموردين
              </p>
            </CardContent>
          </Card>
          <Card>
            <CardHeader className="pb-2">
              <CardTitle className="text-sm font-medium text-muted-foreground">
                الفئات
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{Object.keys(categoryCount).length}</div>
              <p className="text-xs text-muted-foreground mt-1">فئة مختلفة من الموردين</p>
            </CardContent>
          </Card>
        </div>

        <Card>
          <CardHeader className="pb-2">
            <div className="flex flex-col gap-4 md:flex-row md:items-center md:justify-between">
              <CardTitle>قائمة الموردين</CardTitle>
              <div className="relative">
                <Search className="absolute right-3 top-1/2 h-4 w-4 -translate-y-1/2 text-muted-foreground" />
                <Input
                  placeholder="بحث في الموردين..."
                  className="pr-9 w-full md:w-[300px]"
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                />
              </div>
            </div>
          </CardHeader>
          <CardContent>
            <Table>
              <TableCaption>قائمة بجميع الموردين المسجلين في النظام</TableCaption>
              <TableHeader>
                <TableRow>
                  <TableHead>رقم المورد</TableHead>
                  <TableHead>اسم المورد</TableHead>
                  <TableHead>جهة الاتصال</TableHead>
                  <TableHead>رقم الهاتف</TableHead>
                  <TableHead>الفئة</TableHead>
                  <TableHead>الحالة</TableHead>
                  <TableHead className="text-left">إجراءات</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {filteredSuppliers.map((supplier) => (
                  <TableRow key={supplier.id}>
                    <TableCell>{supplier.id}</TableCell>
                    <TableCell>{supplier.name}</TableCell>
                    <TableCell>{supplier.contact}</TableCell>
                    <TableCell>{supplier.phone}</TableCell>
                    <TableCell>{supplier.category}</TableCell>
                    <TableCell>
                      <Badge
                        variant={supplier.status === "نشط" ? "default" : "outline"}
                      >
                        {supplier.status}
                      </Badge>
                    </TableCell>
                    <TableCell>
                      <DropdownMenu>
                        <DropdownMenuTrigger asChild>
                          <Button variant="ghost" size="icon">
                            <svg
                              xmlns="http://www.w3.org/2000/svg"
                              width="16"
                              height="16"
                              viewBox="0 0 24 24"
                              fill="none"
                              stroke="currentColor"
                              strokeWidth="2"
                              strokeLinecap="round"
                              strokeLinejoin="round"
                            >
                              <circle cx="12" cy="12" r="1" />
                              <circle cx="19" cy="12" r="1" />
                              <circle cx="5" cy="12" r="1" />
                            </svg>
                          </Button>
                        </DropdownMenuTrigger>
                        <DropdownMenuContent align="end">
                          <DropdownMenuItem>
                            <Eye className="w-4 h-4 ml-2" />
                            عرض التفاصيل
                          </DropdownMenuItem>
                          <DropdownMenuItem>
                            <Edit className="w-4 h-4 ml-2" />
                            تعديل
                          </DropdownMenuItem>
                          <DropdownMenuSeparator />
                          <DropdownMenuItem className="text-destructive">
                            <Trash2 className="w-4 h-4 ml-2" />
                            حذف
                          </DropdownMenuItem>
                        </DropdownMenuContent>
                      </DropdownMenu>
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          </CardContent>
        </Card>
      </div>
    </MainLayout>
  );
};

export default Suppliers;
