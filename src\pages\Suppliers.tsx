import React, { useState, useEffect } from "react";
import MainLayout from "@/components/layout/MainLayout";
import { But<PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Badge } from "@/components/ui/badge";
import { Label } from "@/components/ui/label";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import {
  Dialog,
  DialogContent,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog";
import { Plus, Edit, Trash2, Building, Save, X } from "lucide-react";
import { useToast } from "@/components/ui/use-toast";
import { DataService, Supplier } from "@/lib/storage";

const Suppliers = () => {
  const { toast } = useToast();
  const [suppliers, setSuppliers] = useState<Supplier[]>([]);
  const [isAddDialogOpen, setIsAddDialogOpen] = useState(false);
  const [isEditDialogOpen, setIsEditDialogOpen] = useState(false);
  const [supplierToEdit, setSupplierToEdit] = useState<Supplier | null>(null);
  const [isLoading, setIsLoading] = useState(true);

  // بيانات النموذج
  const [formData, setFormData] = useState({
    name: "",
    email: "",
    phone: "",
    address: "",
    category: "",
    status: "نشط"
  });

  // تحميل البيانات عند بدء التطبيق
  useEffect(() => {
    loadSuppliers();
  }, []);

  const loadSuppliers = () => {
    try {
      setIsLoading(true);
      console.log('🔄 جاري تحميل الموردين...');
      const loadedSuppliers = DataService.getSuppliers();
      setSuppliers(loadedSuppliers);
      console.log('✅ تم تحميل الموردين بنجاح:', loadedSuppliers);
    } catch (error) {
      console.error('❌ خطأ في تحميل الموردين:', error);
      toast({
        title: "خطأ في التحميل",
        description: "حدث خطأ أثناء تحميل بيانات الموردين",
        variant: "destructive",
      });
    } finally {
      setIsLoading(false);
    }
  };

  // إضافة مورد جديد
  const handleAddSupplier = () => {
    console.log('🔄 محاولة إضافة مورد:', formData);

    if (!formData.name.trim() || !formData.category.trim()) {
      toast({
        title: "خطأ في البيانات",
        description: "يرجى ملء اسم المورد والفئة على الأقل",
        variant: "destructive",
      });
      return;
    }

    try {
      const newSupplier = DataService.addSupplier({
        name: formData.name.trim(),
        email: formData.email.trim(),
        phone: formData.phone.trim(),
        address: formData.address.trim(),
        category: formData.category.trim(),
        status: formData.status
      });

      console.log('✅ تم إضافة المورد:', newSupplier);

      // إعادة تحميل البيانات
      loadSuppliers();

      // إعادة تعيين النموذج
      setFormData({
        name: "",
        email: "",
        phone: "",
        address: "",
        category: "",
        status: "نشط"
      });
      setIsAddDialogOpen(false);

      toast({
        title: "تم بنجاح",
        description: `تم إضافة المورد ${newSupplier.name} بنجاح`,
      });
    } catch (error) {
      console.error('❌ خطأ في إضافة المورد:', error);
      toast({
        title: "خطأ في الإضافة",
        description: "حدث خطأ أثناء إضافة المورد",
        variant: "destructive",
      });
    }
  };

  // تعديل مورد
  const handleEditSupplier = (supplier: Supplier) => {
    console.log('🔄 فتح نافذة تعديل المورد:', supplier);
    setSupplierToEdit(supplier);
    setFormData({
      name: supplier.name,
      email: supplier.email,
      phone: supplier.phone,
      address: supplier.address,
      category: supplier.category,
      status: supplier.status
    });
    setIsEditDialogOpen(true);
  };

  // حفظ التعديل
  const handleSaveEdit = () => {
    if (!supplierToEdit) return;

    console.log('🔄 محاولة حفظ تعديل المورد:', formData);

    if (!formData.name.trim() || !formData.category.trim()) {
      toast({
        title: "خطأ في البيانات",
        description: "يرجى ملء اسم المورد والفئة على الأقل",
        variant: "destructive",
      });
      return;
    }

    try {
      const updatedSupplier: Supplier = {
        ...supplierToEdit,
        name: formData.name.trim(),
        email: formData.email.trim(),
        phone: formData.phone.trim(),
        address: formData.address.trim(),
        category: formData.category.trim(),
        status: formData.status
      };

      DataService.updateSupplier(updatedSupplier);
      console.log('✅ تم تعديل المورد:', updatedSupplier);

      // إعادة تحميل البيانات
      loadSuppliers();

      setIsEditDialogOpen(false);
      setSupplierToEdit(null);
      setFormData({
        name: "",
        email: "",
        phone: "",
        address: "",
        category: "",
        status: "نشط"
      });

      toast({
        title: "تم التعديل",
        description: `تم تعديل بيانات المورد ${updatedSupplier.name} بنجاح`,
      });
    } catch (error) {
      console.error('❌ خطأ في تعديل المورد:', error);
      toast({
        title: "خطأ في التعديل",
        description: "حدث خطأ أثناء تعديل المورد",
        variant: "destructive",
      });
    }
  };

  // حذف مورد
  const handleDeleteSupplier = (supplierId: number, supplierName: string) => {
    console.log('🔄 محاولة حذف المورد:', supplierId, supplierName);

    if (confirm(`هل أنت متأكد من حذف المورد "${supplierName}"؟`)) {
      try {
        DataService.deleteSupplier(supplierId);
        console.log('✅ تم حذف المورد:', supplierId);

        // إعادة تحميل البيانات
        loadSuppliers();

        toast({
          title: "تم الحذف",
          description: `تم حذف المورد ${supplierName} بنجاح`,
        });
      } catch (error) {
        console.error('❌ خطأ في حذف المورد:', error);
        toast({
          title: "خطأ في الحذف",
          description: "حدث خطأ أثناء حذف المورد",
          variant: "destructive",
        });
      }
    }
  };

  // إحصائيات
  const activeSuppliers = suppliers.filter(s => s.status === "نشط").length;
  const inactiveSuppliers = suppliers.filter(s => s.status === "غير نشط").length;
  const categories = [...new Set(suppliers.map(s => s.category))].length;

  if (isLoading) {
    return (
      <MainLayout>
        <div className="flex items-center justify-center h-64">
          <div className="text-center">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto mb-4"></div>
            <p>جاري تحميل البيانات...</p>
          </div>
        </div>
      </MainLayout>
    );
  }

  return (
    <MainLayout>
      <div className="space-y-6">
        {/* العنوان وزر الإضافة */}
        <div className="flex justify-between items-center">
          <h1 className="text-2xl font-bold">إدارة الموردين</h1>
          <Dialog open={isAddDialogOpen} onOpenChange={setIsAddDialogOpen}>
            <DialogTrigger asChild>
              <Button onClick={() => {
                console.log('🔄 فتح نافذة إضافة مورد');
                setFormData({
                  name: "",
                  email: "",
                  phone: "",
                  address: "",
                  category: "",
                  status: "نشط"
                });
              }}>
                <Plus className="mr-2 h-4 w-4" />
                إضافة مورد
              </Button>
            </DialogTrigger>
            <DialogContent className="max-w-md">
              <DialogHeader>
                <DialogTitle>إضافة مورد جديد</DialogTitle>
              </DialogHeader>
              <div className="space-y-4 max-h-96 overflow-y-auto">
                <div>
                  <Label htmlFor="name">اسم المورد *</Label>
                  <Input
                    id="name"
                    value={formData.name}
                    onChange={(e) => {
                      console.log('📝 تغيير اسم المورد:', e.target.value);
                      setFormData({...formData, name: e.target.value});
                    }}
                    placeholder="أدخل اسم المورد"
                  />
                </div>
                <div>
                  <Label htmlFor="email">البريد الإلكتروني</Label>
                  <Input
                    id="email"
                    type="email"
                    value={formData.email}
                    onChange={(e) => {
                      console.log('📝 تغيير البريد الإلكتروني:', e.target.value);
                      setFormData({...formData, email: e.target.value});
                    }}
                    placeholder="<EMAIL>"
                  />
                </div>
                <div>
                  <Label htmlFor="phone">رقم الهاتف</Label>
                  <Input
                    id="phone"
                    value={formData.phone}
                    onChange={(e) => {
                      console.log('📝 تغيير رقم الهاتف:', e.target.value);
                      setFormData({...formData, phone: e.target.value});
                    }}
                    placeholder="05xxxxxxxx"
                  />
                </div>
                <div>
                  <Label htmlFor="address">العنوان</Label>
                  <Input
                    id="address"
                    value={formData.address}
                    onChange={(e) => {
                      console.log('📝 تغيير العنوان:', e.target.value);
                      setFormData({...formData, address: e.target.value});
                    }}
                    placeholder="المدينة - الحي"
                  />
                </div>
                <div>
                  <Label htmlFor="category">الفئة *</Label>
                  <select
                    id="category"
                    value={formData.category}
                    onChange={(e) => {
                      console.log('📝 تغيير الفئة:', e.target.value);
                      setFormData({...formData, category: e.target.value});
                    }}
                    className="w-full p-2 border rounded-md"
                  >
                    <option value="">اختر الفئة</option>
                    <option value="مواد البناء">مواد البناء</option>
                    <option value="كهرباء">كهرباء</option>
                    <option value="سباكة">سباكة</option>
                    <option value="دهانات">دهانات</option>
                    <option value="رخام وسيراميك">رخام وسيراميك</option>
                    <option value="مقاولات">مقاولات</option>
                  </select>
                </div>
                <div>
                  <Label htmlFor="status">الحالة</Label>
                  <select
                    id="status"
                    value={formData.status}
                    onChange={(e) => {
                      console.log('📝 تغيير الحالة:', e.target.value);
                      setFormData({...formData, status: e.target.value});
                    }}
                    className="w-full p-2 border rounded-md"
                  >
                    <option value="نشط">نشط</option>
                    <option value="غير نشط">غير نشط</option>
                  </select>
                </div>
              </div>
              <DialogFooter>
                <Button variant="outline" onClick={() => setIsAddDialogOpen(false)}>
                  <X className="mr-2 h-4 w-4" />
                  إلغاء
                </Button>
                <Button onClick={handleAddSupplier}>
                  <Save className="mr-2 h-4 w-4" />
                  إضافة
                </Button>
              </DialogFooter>
            </DialogContent>
          </Dialog>
        </div>

        {/* الإحصائيات */}
        <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
          <Card>
            <CardHeader>
              <CardTitle className="text-sm">إجمالي الموردين</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{suppliers.length}</div>
            </CardContent>
          </Card>
          <Card>
            <CardHeader>
              <CardTitle className="text-sm">نشط</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold text-green-600">{activeSuppliers}</div>
            </CardContent>
          </Card>
          <Card>
            <CardHeader>
              <CardTitle className="text-sm">غير نشط</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold text-red-600">{inactiveSuppliers}</div>
            </CardContent>
          </Card>
          <Card>
            <CardHeader>
              <CardTitle className="text-sm">الفئات</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold text-blue-600">{categories}</div>
            </CardContent>
          </Card>
        </div>

        {/* جدول الموردين */}
        <Card>
          <CardHeader>
            <CardTitle>قائمة الموردين ({suppliers.length})</CardTitle>
          </CardHeader>
          <CardContent>
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>المعرف</TableHead>
                  <TableHead>الاسم</TableHead>
                  <TableHead>الفئة</TableHead>
                  <TableHead>الهاتف</TableHead>
                  <TableHead>البريد الإلكتروني</TableHead>
                  <TableHead>الحالة</TableHead>
                  <TableHead>الإجراءات</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {suppliers.length === 0 ? (
                  <TableRow>
                    <TableCell colSpan={7} className="text-center py-8">
                      لا توجد بيانات
                    </TableCell>
                  </TableRow>
                ) : (
                  suppliers.map((supplier) => (
                    <TableRow key={supplier.id}>
                      <TableCell>#{supplier.id}</TableCell>
                      <TableCell>
                        <div className="flex items-center gap-2">
                          <Building className="h-4 w-4" />
                          <div>
                            <div className="font-medium">{supplier.name}</div>
                            {supplier.address && (
                              <div className="text-sm text-muted-foreground">{supplier.address}</div>
                            )}
                          </div>
                        </div>
                      </TableCell>
                      <TableCell>{supplier.category}</TableCell>
                      <TableCell>{supplier.phone || "غير محدد"}</TableCell>
                      <TableCell>{supplier.email || "غير محدد"}</TableCell>
                      <TableCell>
                        <Badge variant={supplier.status === "نشط" ? "default" : "secondary"}>
                          {supplier.status}
                        </Badge>
                      </TableCell>
                      <TableCell>
                        <div className="flex gap-2">
                          <Button
                            variant="ghost"
                            size="sm"
                            onClick={() => handleEditSupplier(supplier)}
                          >
                            <Edit className="h-4 w-4" />
                          </Button>
                          <Button
                            variant="ghost"
                            size="sm"
                            onClick={() => handleDeleteSupplier(supplier.id, supplier.name)}
                          >
                            <Trash2 className="h-4 w-4" />
                          </Button>
                        </div>
                      </TableCell>
                    </TableRow>
                  ))
                )}
              </TableBody>
            </Table>
          </CardContent>
        </Card>

        {/* نافذة التعديل */}
        <Dialog open={isEditDialogOpen} onOpenChange={setIsEditDialogOpen}>
          <DialogContent className="max-w-md">
            <DialogHeader>
              <DialogTitle>تعديل المورد</DialogTitle>
            </DialogHeader>
            <div className="space-y-4 max-h-96 overflow-y-auto">
              <div>
                <Label htmlFor="edit-name">اسم المورد *</Label>
                <Input
                  id="edit-name"
                  value={formData.name}
                  onChange={(e) => {
                    console.log('📝 تعديل اسم المورد:', e.target.value);
                    setFormData({...formData, name: e.target.value});
                  }}
                  placeholder="أدخل اسم المورد"
                />
              </div>
              <div>
                <Label htmlFor="edit-email">البريد الإلكتروني</Label>
                <Input
                  id="edit-email"
                  type="email"
                  value={formData.email}
                  onChange={(e) => {
                    console.log('📝 تعديل البريد الإلكتروني:', e.target.value);
                    setFormData({...formData, email: e.target.value});
                  }}
                  placeholder="<EMAIL>"
                />
              </div>
              <div>
                <Label htmlFor="edit-phone">رقم الهاتف</Label>
                <Input
                  id="edit-phone"
                  value={formData.phone}
                  onChange={(e) => {
                    console.log('📝 تعديل رقم الهاتف:', e.target.value);
                    setFormData({...formData, phone: e.target.value});
                  }}
                  placeholder="05xxxxxxxx"
                />
              </div>
              <div>
                <Label htmlFor="edit-address">العنوان</Label>
                <Input
                  id="edit-address"
                  value={formData.address}
                  onChange={(e) => {
                    console.log('📝 تعديل العنوان:', e.target.value);
                    setFormData({...formData, address: e.target.value});
                  }}
                  placeholder="المدينة - الحي"
                />
              </div>
              <div>
                <Label htmlFor="edit-category">الفئة *</Label>
                <select
                  id="edit-category"
                  value={formData.category}
                  onChange={(e) => {
                    console.log('📝 تعديل الفئة:', e.target.value);
                    setFormData({...formData, category: e.target.value});
                  }}
                  className="w-full p-2 border rounded-md"
                >
                  <option value="">اختر الفئة</option>
                  <option value="مواد البناء">مواد البناء</option>
                  <option value="كهرباء">كهرباء</option>
                  <option value="سباكة">سباكة</option>
                  <option value="دهانات">دهانات</option>
                  <option value="رخام وسيراميك">رخام وسيراميك</option>
                  <option value="مقاولات">مقاولات</option>
                </select>
              </div>
              <div>
                <Label htmlFor="edit-status">الحالة</Label>
                <select
                  id="edit-status"
                  value={formData.status}
                  onChange={(e) => {
                    console.log('📝 تعديل الحالة:', e.target.value);
                    setFormData({...formData, status: e.target.value});
                  }}
                  className="w-full p-2 border rounded-md"
                >
                  <option value="نشط">نشط</option>
                  <option value="غير نشط">غير نشط</option>
                </select>
              </div>
            </div>
            <DialogFooter>
              <Button variant="outline" onClick={() => {
                setIsEditDialogOpen(false);
                setSupplierToEdit(null);
              }}>
                <X className="mr-2 h-4 w-4" />
                إلغاء
              </Button>
              <Button onClick={handleSaveEdit}>
                <Save className="mr-2 h-4 w-4" />
                حفظ التغييرات
              </Button>
            </DialogFooter>
          </DialogContent>
        </Dialog>
      </div>
    </MainLayout>
  );
};

export default Suppliers;
