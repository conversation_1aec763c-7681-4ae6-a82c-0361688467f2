
import React, { useState } from "react";
import MainLayout from "@/components/layout/MainLayout";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { FileText, Plus, DollarSign, CheckCircle, AlertCircle, Clock } from "lucide-react";
import { Ta<PERSON>, Ta<PERSON>Content, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Input } from "@/components/ui/input";
import { Badge } from "@/components/ui/badge";
import {
  Table,
  TableBody,
  TableCaption,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog";
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { useForm } from "react-hook-form";
import { useToast } from "@/components/ui/use-toast";

// بيانات المستخلصات النموذجية
const invoicesData = [
  {
    id: 1001,
    contractor: "شركة البناء المتحدة",
    project: "مشروع فلل الواحة",
    amount: 125000,
    date: "2024-06-01",
    status: "مدفوع",
  },
  {
    id: 1002,
    contractor: "مؤسسة الإعمار الحديثة",
    project: "مجمع الفردوس السكني",
    amount: 87500,
    date: "2024-05-25",
    status: "معلق",
  },
  {
    id: 1003,
    contractor: "شركة التطوير العقاري",
    project: "برج الأفق",
    amount: 210000,
    date: "2024-05-15",
    status: "مدفوع",
  },
  {
    id: 1004,
    contractor: "مؤسسة الإنشاءات المتكاملة",
    project: "مشروع فلل الربيع",
    amount: 95000,
    date: "2024-06-05",
    status: "مرفوض",
  },
  {
    id: 1005,
    contractor: "شركة البناء المتحدة",
    project: "مشروع شقق الروضة",
    amount: 150000,
    date: "2024-05-20",
    status: "معلق",
  },
];

const Invoices: React.FC = () => {
  const [activeTab, setActiveTab] = useState("all");
  const [searchQuery, setSearchQuery] = useState("");
  const { toast } = useToast();

  // نموذج إضافة مستخلص جديد
  const form = useForm({
    defaultValues: {
      contractor: "",
      project: "",
      amount: "",
      date: new Date().toISOString().split('T')[0],
      status: "معلق",
    },
  });

  const onSubmit = (data: any) => {
    // هنا يمكن إضافة المستخلص إلى قاعدة البيانات
    toast({
      title: "تمت الإضافة بنجاح",
      description: `تم إضافة المستخلص بقيمة ${data.amount} ريال بنجاح.`,
    });
    console.log(data);
  };

  // تصفية المستخلصات حسب الفلاتر
  const filteredInvoices = invoicesData
    .filter((invoice) => {
      if (activeTab === "all") return true;
      if (activeTab === "paid") return invoice.status === "مدفوع";
      if (activeTab === "pending") return invoice.status === "معلق";
      if (activeTab === "rejected") return invoice.status === "مرفوض";
      return true;
    })
    .filter((invoice) => {
      if (!searchQuery) return true;
      return (
        invoice.contractor.includes(searchQuery) ||
        invoice.project.includes(searchQuery) ||
        invoice.id.toString().includes(searchQuery)
      );
    });

  const totalAmount = invoicesData.reduce((sum, invoice) => sum + invoice.amount, 0);
  const paidAmount = invoicesData
    .filter((invoice) => invoice.status === "مدفوع")
    .reduce((sum, invoice) => sum + invoice.amount, 0);
  const pendingAmount = invoicesData
    .filter((invoice) => invoice.status === "معلق")
    .reduce((sum, invoice) => sum + invoice.amount, 0);

  return (
    <MainLayout>
      <div className="space-y-6">
        <div className="flex justify-between items-center">
          <h1 className="text-2xl font-bold">إدارة المستخلصات</h1>
          <Dialog>
            <DialogTrigger asChild>
              <Button className="gap-2">
                <Plus size={18} />
                إضافة مستخلص
              </Button>
            </DialogTrigger>
            <DialogContent className="sm:max-w-[425px]">
              <DialogHeader>
                <DialogTitle>إضافة مستخلص جديد</DialogTitle>
                <DialogDescription>
                  قم بإدخال بيانات المستخلص الجديد. اضغط على "إضافة" عند الانتهاء.
                </DialogDescription>
              </DialogHeader>
              <Form {...form}>
                <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-4">
                  <FormField
                    control={form.control}
                    name="contractor"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>المقاول</FormLabel>
                        <FormControl>
                          <Input placeholder="اسم المقاول" {...field} />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                  <FormField
                    control={form.control}
                    name="project"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>المشروع</FormLabel>
                        <FormControl>
                          <Input placeholder="اسم المشروع" {...field} />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                  <FormField
                    control={form.control}
                    name="amount"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>المبلغ (ريال)</FormLabel>
                        <FormControl>
                          <Input type="number" placeholder="0" {...field} />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                  <FormField
                    control={form.control}
                    name="date"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>التاريخ</FormLabel>
                        <FormControl>
                          <Input type="date" {...field} />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                  <FormField
                    control={form.control}
                    name="status"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>الحالة</FormLabel>
                        <Select
                          onValueChange={field.onChange}
                          defaultValue={field.value}
                        >
                          <FormControl>
                            <SelectTrigger>
                              <SelectValue placeholder="اختر الحالة" />
                            </SelectTrigger>
                          </FormControl>
                          <SelectContent>
                            <SelectItem value="مدفوع">مدفوع</SelectItem>
                            <SelectItem value="معلق">معلق</SelectItem>
                            <SelectItem value="مرفوض">مرفوض</SelectItem>
                          </SelectContent>
                        </Select>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                  <DialogFooter>
                    <Button type="submit">إضافة</Button>
                  </DialogFooter>
                </form>
              </Form>
            </DialogContent>
          </Dialog>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          <Card>
            <CardHeader className="pb-2">
              <CardTitle className="text-sm font-medium text-muted-foreground">
                إجمالي المستخلصات
              </CardTitle>
            </CardHeader>
            <CardContent className="flex items-center gap-4">
              <div className="w-10 h-10 rounded-full bg-primary/10 flex items-center justify-center">
                <FileText size={20} className="text-primary" />
              </div>
              <div>
                <div className="text-2xl font-bold">{invoicesData.length}</div>
                <div className="text-sm text-muted-foreground">{totalAmount.toLocaleString()} ريال</div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="pb-2">
              <CardTitle className="text-sm font-medium text-muted-foreground">
                المستخلصات المدفوعة
              </CardTitle>
            </CardHeader>
            <CardContent className="flex items-center gap-4">
              <div className="w-10 h-10 rounded-full bg-green-500/10 flex items-center justify-center">
                <CheckCircle size={20} className="text-green-500" />
              </div>
              <div>
                <div className="text-2xl font-bold">
                  {invoicesData.filter(i => i.status === "مدفوع").length}
                </div>
                <div className="text-sm text-muted-foreground">{paidAmount.toLocaleString()} ريال</div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="pb-2">
              <CardTitle className="text-sm font-medium text-muted-foreground">
                المستخلصات المعلقة
              </CardTitle>
            </CardHeader>
            <CardContent className="flex items-center gap-4">
              <div className="w-10 h-10 rounded-full bg-orange-500/10 flex items-center justify-center">
                <Clock size={20} className="text-orange-500" />
              </div>
              <div>
                <div className="text-2xl font-bold">
                  {invoicesData.filter(i => i.status === "معلق").length}
                </div>
                <div className="text-sm text-muted-foreground">{pendingAmount.toLocaleString()} ريال</div>
              </div>
            </CardContent>
          </Card>
        </div>

        <Card>
          <CardHeader>
            <div className="flex flex-col gap-4 md:flex-row md:items-center md:justify-between">
              <CardTitle>قائمة المستخلصات</CardTitle>
              <div className="relative w-full md:w-64">
                <Input
                  placeholder="بحث في المستخلصات..."
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                />
              </div>
            </div>
          </CardHeader>
          <CardContent>
            <Tabs defaultValue="all" onValueChange={setActiveTab} value={activeTab}>
              <TabsList className="grid w-full grid-cols-4">
                <TabsTrigger value="all">الكل</TabsTrigger>
                <TabsTrigger value="paid">مدفوع</TabsTrigger>
                <TabsTrigger value="pending">معلق</TabsTrigger>
                <TabsTrigger value="rejected">مرفوض</TabsTrigger>
              </TabsList>
              <TabsContent value="all" className="pt-4">
                <InvoicesTable invoices={filteredInvoices} />
              </TabsContent>
              <TabsContent value="paid" className="pt-4">
                <InvoicesTable invoices={filteredInvoices} />
              </TabsContent>
              <TabsContent value="pending" className="pt-4">
                <InvoicesTable invoices={filteredInvoices} />
              </TabsContent>
              <TabsContent value="rejected" className="pt-4">
                <InvoicesTable invoices={filteredInvoices} />
              </TabsContent>
            </Tabs>
          </CardContent>
        </Card>
      </div>
    </MainLayout>
  );
};

interface InvoicesTableProps {
  invoices: {
    id: number;
    contractor: string;
    project: string;
    amount: number;
    date: string;
    status: string;
  }[];
}

const InvoicesTable: React.FC<InvoicesTableProps> = ({ invoices }) => {
  return (
    <Table>
      <TableCaption>قائمة المستخلصات والدفعات</TableCaption>
      <TableHeader>
        <TableRow>
          <TableHead className="w-[100px]">رقم المستخلص</TableHead>
          <TableHead>المقاول</TableHead>
          <TableHead>المشروع</TableHead>
          <TableHead>المبلغ</TableHead>
          <TableHead>التاريخ</TableHead>
          <TableHead>الحالة</TableHead>
          <TableHead className="text-left">الإجراءات</TableHead>
        </TableRow>
      </TableHeader>
      <TableBody>
        {invoices.map((invoice) => (
          <TableRow key={invoice.id} className="cursor-pointer hover:bg-muted/50">
            <TableCell className="font-medium">#{invoice.id}</TableCell>
            <TableCell>{invoice.contractor}</TableCell>
            <TableCell>{invoice.project}</TableCell>
            <TableCell>{invoice.amount.toLocaleString()} ريال</TableCell>
            <TableCell>{new Date(invoice.date).toLocaleDateString("ar-SA")}</TableCell>
            <TableCell>
              <Badge
                variant={
                  invoice.status === "مدفوع" ? "default" :
                  invoice.status === "معلق" ? "secondary" :
                  "destructive"
                }
              >
                {invoice.status}
              </Badge>
            </TableCell>
            <TableCell>
              <div className="flex items-center gap-2 justify-end">
                <Button variant="ghost" size="icon">
                  <FileText size={16} />
                </Button>
                <Button variant="ghost" size="icon">
                  <DollarSign size={16} />
                </Button>
              </div>
            </TableCell>
          </TableRow>
        ))}
      </TableBody>
    </Table>
  );
};

export default Invoices;
