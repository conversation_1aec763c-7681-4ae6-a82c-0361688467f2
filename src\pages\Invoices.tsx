import React, { useState, useEffect } from "react";
import MainLayout from "@/components/layout/MainLayout";
import { But<PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Badge } from "@/components/ui/badge";
import { Label } from "@/components/ui/label";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import {
  Dialog,
  DialogContent,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog";
import { Plus, Edit, Trash2, FileText, Save, X } from "lucide-react";
import { useToast } from "@/components/ui/use-toast";
import { DataService, Invoice } from "@/lib/storage";

const Invoices = () => {
  const { toast } = useToast();
  const [invoices, setInvoices] = useState<Invoice[]>([]);
  const [isAddDialogOpen, setIsAddDialogOpen] = useState(false);
  const [isEditDialogOpen, setIsEditDialogOpen] = useState(false);
  const [invoiceToEdit, setInvoiceToEdit] = useState<Invoice | null>(null);
  const [isLoading, setIsLoading] = useState(true);

  // بيانات النموذج
  const [formData, setFormData] = useState({
    customerName: "",
    customerPhone: "",
    customerEmail: "",
    propertyTitle: "",
    amount: "",
    status: "معلق",
    date: new Date().toISOString().split('T')[0],
    dueDate: ""
  });

  // تحميل البيانات عند بدء التطبيق
  useEffect(() => {
    loadInvoices();
  }, []);

  const loadInvoices = () => {
    try {
      setIsLoading(true);
      console.log('🔄 جاري تحميل المستخلصات...');
      const loadedInvoices = DataService.getInvoices();
      setInvoices(loadedInvoices);
      console.log('✅ تم تحميل المستخلصات بنجاح:', loadedInvoices);
    } catch (error) {
      console.error('❌ خطأ في تحميل المستخلصات:', error);
      toast({
        title: "خطأ في التحميل",
        description: "حدث خطأ أثناء تحميل بيانات المستخلصات",
        variant: "destructive",
      });
    } finally {
      setIsLoading(false);
    }
  };

  // إضافة مستخلص جديد
  const handleAddInvoice = () => {
    console.log('🔄 محاولة إضافة مستخلص:', formData);

    if (!formData.customerName.trim() || !formData.amount.trim()) {
      toast({
        title: "خطأ في البيانات",
        description: "يرجى ملء اسم العميل والمبلغ على الأقل",
        variant: "destructive",
      });
      return;
    }

    try {
      const newInvoiceId = DataService.generateInvoiceId();
      const dueDate = formData.dueDate || new Date(Date.now() + 30 * 24 * 60 * 60 * 1000).toISOString().split('T')[0];

      const newInvoice = DataService.addInvoice({
        id: newInvoiceId,
        customerName: formData.customerName.trim(),
        customerPhone: formData.customerPhone.trim(),
        customerEmail: formData.customerEmail.trim(),
        propertyId: "",
        propertyTitle: formData.propertyTitle.trim(),
        amount: formData.amount.trim(),
        status: formData.status,
        date: formData.date,
        dueDate: dueDate
      });

      console.log('✅ تم إضافة المستخلص:', newInvoice);

      // إعادة تحميل البيانات
      loadInvoices();

      // إعادة تعيين النموذج
      setFormData({
        customerName: "",
        customerPhone: "",
        customerEmail: "",
        propertyTitle: "",
        amount: "",
        status: "معلق",
        date: new Date().toISOString().split('T')[0],
        dueDate: ""
      });
      setIsAddDialogOpen(false);

      toast({
        title: "تم بنجاح",
        description: `تم إضافة المستخلص ${newInvoice.id} بنجاح`,
      });
    } catch (error) {
      console.error('❌ خطأ في إضافة المستخلص:', error);
      toast({
        title: "خطأ في الإضافة",
        description: "حدث خطأ أثناء إضافة المستخلص",
        variant: "destructive",
      });
    }
  };

  // تعديل مستخلص
  const handleEditInvoice = (invoice: Invoice) => {
    console.log('🔄 فتح نافذة تعديل المستخلص:', invoice);
    setInvoiceToEdit(invoice);
    setFormData({
      customerName: invoice.customerName,
      customerPhone: invoice.customerPhone,
      customerEmail: invoice.customerEmail,
      propertyTitle: invoice.propertyTitle,
      amount: invoice.amount,
      status: invoice.status,
      date: invoice.date,
      dueDate: invoice.dueDate
    });
    setIsEditDialogOpen(true);
  };

  // حفظ التعديل
  const handleSaveEdit = () => {
    if (!invoiceToEdit) return;

    console.log('🔄 محاولة حفظ تعديل المستخلص:', formData);

    if (!formData.customerName.trim() || !formData.amount.trim()) {
      toast({
        title: "خطأ في البيانات",
        description: "يرجى ملء اسم العميل والمبلغ على الأقل",
        variant: "destructive",
      });
      return;
    }

    try {
      const updatedInvoice: Invoice = {
        ...invoiceToEdit,
        customerName: formData.customerName.trim(),
        customerPhone: formData.customerPhone.trim(),
        customerEmail: formData.customerEmail.trim(),
        propertyTitle: formData.propertyTitle.trim(),
        amount: formData.amount.trim(),
        status: formData.status,
        date: formData.date,
        dueDate: formData.dueDate
      };

      DataService.updateInvoice(updatedInvoice);
      console.log('✅ تم تعديل المستخلص:', updatedInvoice);

      // إعادة تحميل البيانات
      loadInvoices();

      setIsEditDialogOpen(false);
      setInvoiceToEdit(null);
      setFormData({
        customerName: "",
        customerPhone: "",
        customerEmail: "",
        propertyTitle: "",
        amount: "",
        status: "معلق",
        date: new Date().toISOString().split('T')[0],
        dueDate: ""
      });

      toast({
        title: "تم التعديل",
        description: `تم تعديل بيانات المستخلص ${updatedInvoice.id} بنجاح`,
      });
    } catch (error) {
      console.error('❌ خطأ في تعديل المستخلص:', error);
      toast({
        title: "خطأ في التعديل",
        description: "حدث خطأ أثناء تعديل المستخلص",
        variant: "destructive",
      });
    }
  };

  // حذف مستخلص
  const handleDeleteInvoice = (invoiceId: string, customerName: string) => {
    console.log('🔄 محاولة حذف المستخلص:', invoiceId, customerName);

    if (confirm(`هل أنت متأكد من حذف المستخلص "${invoiceId}" للعميل "${customerName}"؟`)) {
      try {
        DataService.deleteInvoice(invoiceId);
        console.log('✅ تم حذف المستخلص:', invoiceId);

        // إعادة تحميل البيانات
        loadInvoices();

        toast({
          title: "تم الحذف",
          description: `تم حذف المستخلص ${invoiceId} بنجاح`,
        });
      } catch (error) {
        console.error('❌ خطأ في حذف المستخلص:', error);
        toast({
          title: "خطأ في الحذف",
          description: "حدث خطأ أثناء حذف المستخلص",
          variant: "destructive",
        });
      }
    }
  };

  // إحصائيات
  const paidInvoices = invoices.filter(i => i.status === "مدفوع").length;
  const pendingInvoices = invoices.filter(i => i.status === "معلق").length;
  const rejectedInvoices = invoices.filter(i => i.status === "مرفوض").length;

  const totalAmount = invoices.reduce((sum, invoice) => {
    const amount = parseFloat(invoice.amount.replace(/,/g, '')) || 0;
    return sum + amount;
  }, 0);

  if (isLoading) {
    return (
      <MainLayout>
        <div className="flex items-center justify-center h-64">
          <div className="text-center">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto mb-4"></div>
            <p>جاري تحميل البيانات...</p>
          </div>
        </div>
      </MainLayout>
    );
  }

  return (
    <MainLayout>
      <div className="space-y-6">
        {/* العنوان وزر الإضافة */}
        <div className="flex justify-between items-center">
          <h1 className="text-2xl font-bold">إدارة المستخلصات</h1>
          <Dialog open={isAddDialogOpen} onOpenChange={setIsAddDialogOpen}>
            <DialogTrigger asChild>
              <Button onClick={() => {
                console.log('🔄 فتح نافذة إضافة مستخلص');
                setFormData({
                  customerName: "",
                  customerPhone: "",
                  customerEmail: "",
                  propertyTitle: "",
                  amount: "",
                  status: "معلق",
                  date: new Date().toISOString().split('T')[0],
                  dueDate: ""
                });
              }}>
                <Plus className="mr-2 h-4 w-4" />
                إضافة مستخلص
              </Button>
            </DialogTrigger>
            <DialogContent className="max-w-md">
              <DialogHeader>
                <DialogTitle>إضافة مستخلص جديد</DialogTitle>
              </DialogHeader>
              <div className="space-y-4 max-h-96 overflow-y-auto">
                <div>
                  <Label htmlFor="customerName">اسم العميل *</Label>
                  <Input
                    id="customerName"
                    value={formData.customerName}
                    onChange={(e) => {
                      console.log('📝 تغيير اسم العميل:', e.target.value);
                      setFormData({...formData, customerName: e.target.value});
                    }}
                    placeholder="أدخل اسم العميل"
                  />
                </div>
                <div>
                  <Label htmlFor="customerPhone">رقم الهاتف</Label>
                  <Input
                    id="customerPhone"
                    value={formData.customerPhone}
                    onChange={(e) => {
                      console.log('📝 تغيير رقم الهاتف:', e.target.value);
                      setFormData({...formData, customerPhone: e.target.value});
                    }}
                    placeholder="05xxxxxxxx"
                  />
                </div>
                <div>
                  <Label htmlFor="customerEmail">البريد الإلكتروني</Label>
                  <Input
                    id="customerEmail"
                    type="email"
                    value={formData.customerEmail}
                    onChange={(e) => {
                      console.log('📝 تغيير البريد الإلكتروني:', e.target.value);
                      setFormData({...formData, customerEmail: e.target.value});
                    }}
                    placeholder="<EMAIL>"
                  />
                </div>
                <div>
                  <Label htmlFor="propertyTitle">العقار</Label>
                  <Input
                    id="propertyTitle"
                    value={formData.propertyTitle}
                    onChange={(e) => {
                      console.log('📝 تغيير العقار:', e.target.value);
                      setFormData({...formData, propertyTitle: e.target.value});
                    }}
                    placeholder="عنوان العقار"
                  />
                </div>
                <div>
                  <Label htmlFor="amount">المبلغ (ريال) *</Label>
                  <Input
                    id="amount"
                    value={formData.amount}
                    onChange={(e) => {
                      console.log('📝 تغيير المبلغ:', e.target.value);
                      setFormData({...formData, amount: e.target.value});
                    }}
                    placeholder="0"
                  />
                </div>
                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <Label htmlFor="date">تاريخ الإصدار</Label>
                    <Input
                      id="date"
                      type="date"
                      value={formData.date}
                      onChange={(e) => {
                        console.log('📝 تغيير التاريخ:', e.target.value);
                        setFormData({...formData, date: e.target.value});
                      }}
                    />
                  </div>
                  <div>
                    <Label htmlFor="dueDate">تاريخ الاستحقاق</Label>
                    <Input
                      id="dueDate"
                      type="date"
                      value={formData.dueDate}
                      onChange={(e) => {
                        console.log('📝 تغيير تاريخ الاستحقاق:', e.target.value);
                        setFormData({...formData, dueDate: e.target.value});
                      }}
                    />
                  </div>
                </div>
                <div>
                  <Label htmlFor="status">الحالة</Label>
                  <select
                    id="status"
                    value={formData.status}
                    onChange={(e) => {
                      console.log('📝 تغيير الحالة:', e.target.value);
                      setFormData({...formData, status: e.target.value});
                    }}
                    className="w-full p-2 border rounded-md"
                  >
                    <option value="معلق">معلق</option>
                    <option value="مدفوع">مدفوع</option>
                    <option value="مرفوض">مرفوض</option>
                  </select>
                </div>
              </div>
              <DialogFooter>
                <Button variant="outline" onClick={() => setIsAddDialogOpen(false)}>
                  <X className="mr-2 h-4 w-4" />
                  إلغاء
                </Button>
                <Button onClick={handleAddInvoice}>
                  <Save className="mr-2 h-4 w-4" />
                  إضافة
                </Button>
              </DialogFooter>
            </DialogContent>
          </Dialog>
        </div>

        {/* الإحصائيات */}
        <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
          <Card>
            <CardHeader>
              <CardTitle className="text-sm">إجمالي المستخلصات</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{invoices.length}</div>
              <p className="text-xs text-muted-foreground">{totalAmount.toLocaleString()} ريال</p>
            </CardContent>
          </Card>
          <Card>
            <CardHeader>
              <CardTitle className="text-sm">مدفوع</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold text-green-600">{paidInvoices}</div>
            </CardContent>
          </Card>
          <Card>
            <CardHeader>
              <CardTitle className="text-sm">معلق</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold text-orange-600">{pendingInvoices}</div>
            </CardContent>
          </Card>
          <Card>
            <CardHeader>
              <CardTitle className="text-sm">مرفوض</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold text-red-600">{rejectedInvoices}</div>
            </CardContent>
          </Card>
        </div>

        {/* جدول المستخلصات */}
        <Card>
          <CardHeader>
            <CardTitle>قائمة المستخلصات ({invoices.length})</CardTitle>
          </CardHeader>
          <CardContent>
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>المعرف</TableHead>
                  <TableHead>العميل</TableHead>
                  <TableHead>العقار</TableHead>
                  <TableHead>المبلغ</TableHead>
                  <TableHead>التاريخ</TableHead>
                  <TableHead>الحالة</TableHead>
                  <TableHead>الإجراءات</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {invoices.length === 0 ? (
                  <TableRow>
                    <TableCell colSpan={7} className="text-center py-8">
                      لا توجد بيانات
                    </TableCell>
                  </TableRow>
                ) : (
                  invoices.map((invoice) => (
                    <TableRow key={invoice.id}>
                      <TableCell>{invoice.id}</TableCell>
                      <TableCell>
                        <div className="flex items-center gap-2">
                          <FileText className="h-4 w-4" />
                          <div>
                            <div className="font-medium">{invoice.customerName}</div>
                            {invoice.customerPhone && (
                              <div className="text-sm text-muted-foreground">{invoice.customerPhone}</div>
                            )}
                          </div>
                        </div>
                      </TableCell>
                      <TableCell>{invoice.propertyTitle || "غير محدد"}</TableCell>
                      <TableCell>{invoice.amount} ريال</TableCell>
                      <TableCell>{new Date(invoice.date).toLocaleDateString("ar-SA")}</TableCell>
                      <TableCell>
                        <Badge
                          variant={
                            invoice.status === "مدفوع"
                              ? "default"
                              : invoice.status === "معلق"
                              ? "secondary"
                              : "destructive"
                          }
                        >
                          {invoice.status}
                        </Badge>
                      </TableCell>
                      <TableCell>
                        <div className="flex gap-2">
                          <Button
                            variant="ghost"
                            size="sm"
                            onClick={() => handleEditInvoice(invoice)}
                          >
                            <Edit className="h-4 w-4" />
                          </Button>
                          <Button
                            variant="ghost"
                            size="sm"
                            onClick={() => handleDeleteInvoice(invoice.id, invoice.customerName)}
                          >
                            <Trash2 className="h-4 w-4" />
                          </Button>
                        </div>
                      </TableCell>
                    </TableRow>
                  ))
                )}
              </TableBody>
            </Table>
          </CardContent>
        </Card>

        {/* نافذة التعديل */}
        <Dialog open={isEditDialogOpen} onOpenChange={setIsEditDialogOpen}>
          <DialogContent className="max-w-md">
            <DialogHeader>
              <DialogTitle>تعديل المستخلص</DialogTitle>
            </DialogHeader>
            <div className="space-y-4 max-h-96 overflow-y-auto">
              <div>
                <Label htmlFor="edit-customerName">اسم العميل *</Label>
                <Input
                  id="edit-customerName"
                  value={formData.customerName}
                  onChange={(e) => {
                    console.log('📝 تعديل اسم العميل:', e.target.value);
                    setFormData({...formData, customerName: e.target.value});
                  }}
                  placeholder="أدخل اسم العميل"
                />
              </div>
              <div>
                <Label htmlFor="edit-customerPhone">رقم الهاتف</Label>
                <Input
                  id="edit-customerPhone"
                  value={formData.customerPhone}
                  onChange={(e) => {
                    console.log('📝 تعديل رقم الهاتف:', e.target.value);
                    setFormData({...formData, customerPhone: e.target.value});
                  }}
                  placeholder="05xxxxxxxx"
                />
              </div>
              <div>
                <Label htmlFor="edit-customerEmail">البريد الإلكتروني</Label>
                <Input
                  id="edit-customerEmail"
                  type="email"
                  value={formData.customerEmail}
                  onChange={(e) => {
                    console.log('📝 تعديل البريد الإلكتروني:', e.target.value);
                    setFormData({...formData, customerEmail: e.target.value});
                  }}
                  placeholder="<EMAIL>"
                />
              </div>
              <div>
                <Label htmlFor="edit-propertyTitle">العقار</Label>
                <Input
                  id="edit-propertyTitle"
                  value={formData.propertyTitle}
                  onChange={(e) => {
                    console.log('📝 تعديل العقار:', e.target.value);
                    setFormData({...formData, propertyTitle: e.target.value});
                  }}
                  placeholder="عنوان العقار"
                />
              </div>
              <div>
                <Label htmlFor="edit-amount">المبلغ (ريال) *</Label>
                <Input
                  id="edit-amount"
                  value={formData.amount}
                  onChange={(e) => {
                    console.log('📝 تعديل المبلغ:', e.target.value);
                    setFormData({...formData, amount: e.target.value});
                  }}
                  placeholder="0"
                />
              </div>
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <Label htmlFor="edit-date">تاريخ الإصدار</Label>
                  <Input
                    id="edit-date"
                    type="date"
                    value={formData.date}
                    onChange={(e) => {
                      console.log('📝 تعديل التاريخ:', e.target.value);
                      setFormData({...formData, date: e.target.value});
                    }}
                  />
                </div>
                <div>
                  <Label htmlFor="edit-dueDate">تاريخ الاستحقاق</Label>
                  <Input
                    id="edit-dueDate"
                    type="date"
                    value={formData.dueDate}
                    onChange={(e) => {
                      console.log('📝 تعديل تاريخ الاستحقاق:', e.target.value);
                      setFormData({...formData, dueDate: e.target.value});
                    }}
                  />
                </div>
              </div>
              <div>
                <Label htmlFor="edit-status">الحالة</Label>
                <select
                  id="edit-status"
                  value={formData.status}
                  onChange={(e) => {
                    console.log('📝 تعديل الحالة:', e.target.value);
                    setFormData({...formData, status: e.target.value});
                  }}
                  className="w-full p-2 border rounded-md"
                >
                  <option value="معلق">معلق</option>
                  <option value="مدفوع">مدفوع</option>
                  <option value="مرفوض">مرفوض</option>
                </select>
              </div>
            </div>
            <DialogFooter>
              <Button variant="outline" onClick={() => {
                setIsEditDialogOpen(false);
                setInvoiceToEdit(null);
              }}>
                <X className="mr-2 h-4 w-4" />
                إلغاء
              </Button>
              <Button onClick={handleSaveEdit}>
                <Save className="mr-2 h-4 w-4" />
                حفظ التغييرات
              </Button>
            </DialogFooter>
          </DialogContent>
        </Dialog>
      </div>
    </MainLayout>
  );
};

export default Invoices;
