import { simpleDatabase, User } from '@/lib/simpleDatabase';

export class SimpleUserService {
  // إضافة مستخدم جديد
  async addUser(userData: Omit<User, 'id' | 'createdAt'>): Promise<number> {
    console.log('SimpleUserService: إضافة مستخدم جديد:', userData);
    return await simpleDatabase.addUser(userData);
  }

  // تحديث مستخدم
  async updateUser(user: User): Promise<void> {
    console.log('SimpleUserService: تحديث مستخدم:', user);
    await simpleDatabase.updateUser(user);
  }

  // حذف مستخدم
  async deleteUser(id: number): Promise<void> {
    console.log('SimpleUserService: حذف مستخدم:', id);
    await simpleDatabase.deleteUser(id);
  }

  // الحصول على مستخدم واحد
  async getUser(id: number): Promise<User | null> {
    return await simpleDatabase.getUser(id);
  }

  // الحصول على جميع المستخدمين
  async getAllUsers(): Promise<User[]> {
    console.log('SimpleUserService: تحميل جميع المستخدمين');
    return await simpleDatabase.getAllUsers();
  }

  // البحث عن مستخدم بالبريد الإلكتروني
  async getUserByEmail(email: string): Promise<User[]> {
    const allUsers = await this.getAllUsers();
    return allUsers.filter(user => user.email === email);
  }

  // البحث عن المستخدمين بالاسم
  async getUsersByName(name: string): Promise<User[]> {
    const allUsers = await this.getAllUsers();
    return allUsers.filter(user => user.name.includes(name));
  }

  // الحصول على المستخدمين النشطين
  async getActiveUsers(): Promise<User[]> {
    const allUsers = await this.getAllUsers();
    return allUsers.filter(user => user.status === 'نشط');
  }

  // الحصول على المستخدمين حسب الدور
  async getUsersByRole(role: string): Promise<User[]> {
    const allUsers = await this.getAllUsers();
    return allUsers.filter(user => user.role === role);
  }

  // الحصول على المستخدمين حسب القسم
  async getUsersByDepartment(department: string): Promise<User[]> {
    const allUsers = await this.getAllUsers();
    return allUsers.filter(user => user.department === department);
  }

  // إحصائيات المستخدمين
  async getUserStats(): Promise<{
    total: number;
    active: number;
    inactive: number;
    byRole: Record<string, number>;
    byDepartment: Record<string, number>;
  }> {
    const users = await this.getAllUsers();
    
    const stats = {
      total: users.length,
      active: users.filter(u => u.status === 'نشط').length,
      inactive: users.filter(u => u.status === 'غير نشط').length,
      byRole: {} as Record<string, number>,
      byDepartment: {} as Record<string, number>,
    };

    // إحصائيات حسب الدور
    users.forEach(user => {
      stats.byRole[user.role] = (stats.byRole[user.role] || 0) + 1;
    });

    // إحصائيات حسب القسم
    users.forEach(user => {
      stats.byDepartment[user.department] = (stats.byDepartment[user.department] || 0) + 1;
    });

    return stats;
  }

  // تهيئة البيانات الأولية (لا حاجة لها مع SimpleDatabase)
  async initializeDefaultUsers(): Promise<void> {
    console.log('SimpleUserService: البيانات الأولية متوفرة بالفعل');
    // البيانات الأولية متوفرة بالفعل في SimpleDatabase
  }
}

export const simpleUserService = new SimpleUserService();
