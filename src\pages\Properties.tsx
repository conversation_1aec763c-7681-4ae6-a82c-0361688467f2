import React, { useState, useEffect } from "react";
import MainLayout from "@/components/layout/MainLayout";
import { But<PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Badge } from "@/components/ui/badge";
import { Label } from "@/components/ui/label";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import {
  Dialog,
  DialogContent,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog";
import { Plus, Edit, Trash2, Home, MapPin, Square, Save, X } from "lucide-react";
import { useToast } from "@/components/ui/use-toast";
import { DataService, Property } from "@/lib/storage";

const Properties = () => {
  const { toast } = useToast();
  const [properties, setProperties] = useState<Property[]>([]);
  const [isAddDialogOpen, setIsAddDialogOpen] = useState(false);
  const [isEditDialogOpen, setIsEditDialogOpen] = useState(false);
  const [propertyToEdit, setPropertyToEdit] = useState<Property | null>(null);
  const [isLoading, setIsLoading] = useState(true);

  // بيانات النموذج
  const [formData, setFormData] = useState({
    id: "",
    title: "",
    type: "",
    location: "",
    area: "",
    price: "",
    bedrooms: "",
    bathrooms: "",
    status: "متاح"
  });

  // تحميل البيانات عند بدء التطبيق
  useEffect(() => {
    loadProperties();
  }, []);

  const loadProperties = () => {
    try {
      setIsLoading(true);
      console.log('🔄 جاري تحميل العقارات...');
      const loadedProperties = DataService.getProperties();
      setProperties(loadedProperties);
      console.log('✅ تم تحميل العقارات بنجاح:', loadedProperties);
    } catch (error) {
      console.error('❌ خطأ في تحميل العقارات:', error);
      toast({
        title: "خطأ في التحميل",
        description: "حدث خطأ أثناء تحميل بيانات العقارات",
        variant: "destructive",
      });
    } finally {
      setIsLoading(false);
    }
  };

  // إضافة عقار جديد
  const handleAddProperty = () => {
    console.log('🔄 محاولة إضافة عقار:', formData);

    if (!formData.title.trim() || !formData.type.trim() || !formData.location.trim()) {
      toast({
        title: "خطأ في البيانات",
        description: "يرجى ملء العنوان والنوع والموقع على الأقل",
        variant: "destructive",
      });
      return;
    }

    try {
      const newPropertyId = DataService.generatePropertyId();
      const newProperty = DataService.addProperty({
        id: newPropertyId,
        title: formData.title.trim(),
        type: formData.type.trim(),
        location: formData.location.trim(),
        area: parseFloat(formData.area) || 0,
        price: formData.price.trim() || "0",
        bedrooms: formData.bedrooms ? parseInt(formData.bedrooms) : null,
        bathrooms: formData.bathrooms ? parseInt(formData.bathrooms) : null,
        status: formData.status
      });

      console.log('✅ تم إضافة العقار:', newProperty);

      // إعادة تحميل البيانات
      loadProperties();

      // إعادة تعيين النموذج
      setFormData({ id: "", title: "", type: "", location: "", area: "", price: "", bedrooms: "", bathrooms: "", status: "متاح" });
      setIsAddDialogOpen(false);

      toast({
        title: "تم بنجاح",
        description: `تم إضافة العقار ${newProperty.title} بنجاح`,
      });
    } catch (error) {
      console.error('❌ خطأ في إضافة العقار:', error);
      toast({
        title: "خطأ في الإضافة",
        description: "حدث خطأ أثناء إضافة العقار",
        variant: "destructive",
      });
    }
  };

  // تعديل عقار
  const handleEditProperty = (property: Property) => {
    console.log('🔄 فتح نافذة تعديل العقار:', property);
    setPropertyToEdit(property);
    setFormData({
      id: property.id,
      title: property.title,
      type: property.type,
      location: property.location,
      area: property.area.toString(),
      price: property.price,
      bedrooms: property.bedrooms?.toString() || "",
      bathrooms: property.bathrooms?.toString() || "",
      status: property.status
    });
    setIsEditDialogOpen(true);
  };

  // حفظ التعديل
  const handleSaveEdit = () => {
    if (!propertyToEdit) return;

    console.log('🔄 محاولة حفظ تعديل العقار:', formData);

    if (!formData.title.trim() || !formData.type.trim() || !formData.location.trim()) {
      toast({
        title: "خطأ في البيانات",
        description: "يرجى ملء العنوان والنوع والموقع على الأقل",
        variant: "destructive",
      });
      return;
    }

    try {
      const updatedProperty: Property = {
        ...propertyToEdit,
        title: formData.title.trim(),
        type: formData.type.trim(),
        location: formData.location.trim(),
        area: parseFloat(formData.area) || 0,
        price: formData.price.trim() || "0",
        bedrooms: formData.bedrooms ? parseInt(formData.bedrooms) : null,
        bathrooms: formData.bathrooms ? parseInt(formData.bathrooms) : null,
        status: formData.status
      };

      DataService.updateProperty(updatedProperty);
      console.log('✅ تم تعديل العقار:', updatedProperty);

      // إعادة تحميل البيانات
      loadProperties();

      setIsEditDialogOpen(false);
      setPropertyToEdit(null);
      setFormData({ id: "", title: "", type: "", location: "", area: "", price: "", bedrooms: "", bathrooms: "", status: "متاح" });

      toast({
        title: "تم التعديل",
        description: `تم تعديل بيانات العقار ${updatedProperty.title} بنجاح`,
      });
    } catch (error) {
      console.error('❌ خطأ في تعديل العقار:', error);
      toast({
        title: "خطأ في التعديل",
        description: "حدث خطأ أثناء تعديل العقار",
        variant: "destructive",
      });
    }
  };

  // حذف عقار
  const handleDeleteProperty = (propertyId: string, propertyTitle: string) => {
    console.log('🔄 محاولة حذف العقار:', propertyId, propertyTitle);

    if (confirm(`هل أنت متأكد من حذف العقار "${propertyTitle}"؟`)) {
      try {
        DataService.deleteProperty(propertyId);
        console.log('✅ تم حذف العقار:', propertyId);

        // إعادة تحميل البيانات
        loadProperties();

        toast({
          title: "تم الحذف",
          description: `تم حذف العقار ${propertyTitle} بنجاح`,
        });
      } catch (error) {
        console.error('❌ خطأ في حذف العقار:', error);
        toast({
          title: "خطأ في الحذف",
          description: "حدث خطأ أثناء حذف العقار",
          variant: "destructive",
        });
      }
    }
  };

  // إحصائيات
  const availableProperties = properties.filter(p => p.status === "متاح").length;
  const soldProperties = properties.filter(p => p.status === "تم البيع").length;
  const pendingProperties = properties.filter(p => p.status === "تحت العرض").length;

  if (isLoading) {
    return (
      <MainLayout>
        <div className="flex items-center justify-center h-64">
          <div className="text-center">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto mb-4"></div>
            <p>جاري تحميل البيانات...</p>
          </div>
        </div>
      </MainLayout>
    );
  }

  return (
    <MainLayout>
      <div className="space-y-6">
        {/* العنوان وزر الإضافة */}
        <div className="flex justify-between items-center">
          <h1 className="text-2xl font-bold">إدارة العقارات</h1>
          <Dialog open={isAddDialogOpen} onOpenChange={setIsAddDialogOpen}>
            <DialogTrigger asChild>
              <Button onClick={() => {
                console.log('🔄 فتح نافذة إضافة عقار');
                setFormData({ id: "", title: "", type: "", location: "", area: "", price: "", bedrooms: "", bathrooms: "", status: "متاح" });
              }}>
                <Plus className="mr-2 h-4 w-4" />
                إضافة عقار
              </Button>
            </DialogTrigger>
            <DialogContent className="max-w-md">
              <DialogHeader>
                <DialogTitle>إضافة عقار جديد</DialogTitle>
              </DialogHeader>
              <div className="space-y-4 max-h-96 overflow-y-auto">
                <div>
                  <Label htmlFor="title">عنوان العقار *</Label>
                  <Input
                    id="title"
                    value={formData.title}
                    onChange={(e) => {
                      console.log('📝 تغيير العنوان:', e.target.value);
                      setFormData({...formData, title: e.target.value});
                    }}
                    placeholder="أدخل عنوان العقار"
                  />
                </div>
                <div>
                  <Label htmlFor="type">نوع العقار *</Label>
                  <select
                    id="type"
                    value={formData.type}
                    onChange={(e) => {
                      console.log('📝 تغيير النوع:', e.target.value);
                      setFormData({...formData, type: e.target.value});
                    }}
                    className="w-full p-2 border rounded-md"
                  >
                    <option value="">اختر النوع</option>
                    <option value="شقة">شقة</option>
                    <option value="فيلا">فيلا</option>
                    <option value="أرض">أرض</option>
                    <option value="محل تجاري">محل تجاري</option>
                    <option value="مكتب">مكتب</option>
                  </select>
                </div>
                <div>
                  <Label htmlFor="location">الموقع *</Label>
                  <Input
                    id="location"
                    value={formData.location}
                    onChange={(e) => {
                      console.log('📝 تغيير الموقع:', e.target.value);
                      setFormData({...formData, location: e.target.value});
                    }}
                    placeholder="المدينة - الحي"
                  />
                </div>
                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <Label htmlFor="area">المساحة (م²)</Label>
                    <Input
                      id="area"
                      type="number"
                      value={formData.area}
                      onChange={(e) => {
                        console.log('📝 تغيير المساحة:', e.target.value);
                        setFormData({...formData, area: e.target.value});
                      }}
                      placeholder="0"
                    />
                  </div>
                  <div>
                    <Label htmlFor="price">السعر (ريال)</Label>
                    <Input
                      id="price"
                      value={formData.price}
                      onChange={(e) => {
                        console.log('📝 تغيير السعر:', e.target.value);
                        setFormData({...formData, price: e.target.value});
                      }}
                      placeholder="0"
                    />
                  </div>
                </div>
                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <Label htmlFor="bedrooms">عدد الغرف</Label>
                    <Input
                      id="bedrooms"
                      type="number"
                      value={formData.bedrooms}
                      onChange={(e) => {
                        console.log('📝 تغيير عدد الغرف:', e.target.value);
                        setFormData({...formData, bedrooms: e.target.value});
                      }}
                      placeholder="0"
                    />
                  </div>
                  <div>
                    <Label htmlFor="bathrooms">دورات المياه</Label>
                    <Input
                      id="bathrooms"
                      type="number"
                      value={formData.bathrooms}
                      onChange={(e) => {
                        console.log('📝 تغيير دورات المياه:', e.target.value);
                        setFormData({...formData, bathrooms: e.target.value});
                      }}
                      placeholder="0"
                    />
                  </div>
                </div>
                <div>
                  <Label htmlFor="status">الحالة</Label>
                  <select
                    id="status"
                    value={formData.status}
                    onChange={(e) => {
                      console.log('📝 تغيير الحالة:', e.target.value);
                      setFormData({...formData, status: e.target.value});
                    }}
                    className="w-full p-2 border rounded-md"
                  >
                    <option value="متاح">متاح</option>
                    <option value="تحت العرض">تحت العرض</option>
                    <option value="تم البيع">تم البيع</option>
                  </select>
                </div>
              </div>
              <DialogFooter>
                <Button variant="outline" onClick={() => setIsAddDialogOpen(false)}>
                  <X className="mr-2 h-4 w-4" />
                  إلغاء
                </Button>
                <Button onClick={handleAddProperty}>
                  <Save className="mr-2 h-4 w-4" />
                  إضافة
                </Button>
              </DialogFooter>
            </DialogContent>
          </Dialog>
        </div>

        {/* الإحصائيات */}
        <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
          <Card>
            <CardHeader>
              <CardTitle className="text-sm">إجمالي العقارات</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{properties.length}</div>
            </CardContent>
          </Card>
          <Card>
            <CardHeader>
              <CardTitle className="text-sm">متاح</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold text-green-600">{availableProperties}</div>
            </CardContent>
          </Card>
          <Card>
            <CardHeader>
              <CardTitle className="text-sm">تم البيع</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold text-blue-600">{soldProperties}</div>
            </CardContent>
          </Card>
          <Card>
            <CardHeader>
              <CardTitle className="text-sm">تحت العرض</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold text-orange-600">{pendingProperties}</div>
            </CardContent>
          </Card>
        </div>

        {/* جدول العقارات */}
        <Card>
          <CardHeader>
            <CardTitle>قائمة العقارات ({properties.length})</CardTitle>
          </CardHeader>
          <CardContent>
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>المعرف</TableHead>
                  <TableHead>العنوان</TableHead>
                  <TableHead>النوع</TableHead>
                  <TableHead>الموقع</TableHead>
                  <TableHead>المساحة</TableHead>
                  <TableHead>السعر</TableHead>
                  <TableHead>الحالة</TableHead>
                  <TableHead>الإجراءات</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {properties.length === 0 ? (
                  <TableRow>
                    <TableCell colSpan={8} className="text-center py-8">
                      لا توجد بيانات
                    </TableCell>
                  </TableRow>
                ) : (
                  properties.map((property) => (
                    <TableRow key={property.id}>
                      <TableCell>{property.id}</TableCell>
                      <TableCell>
                        <div className="flex items-center gap-2">
                          <Home className="h-4 w-4" />
                          {property.title}
                        </div>
                      </TableCell>
                      <TableCell>{property.type}</TableCell>
                      <TableCell>
                        <div className="flex items-center gap-1">
                          <MapPin className="h-3 w-3" />
                          {property.location}
                        </div>
                      </TableCell>
                      <TableCell>
                        <div className="flex items-center gap-1">
                          <Square className="h-3 w-3" />
                          {property.area} م²
                        </div>
                      </TableCell>
                      <TableCell>{property.price} ريال</TableCell>
                      <TableCell>
                        <Badge
                          variant={
                            property.status === "متاح"
                              ? "default"
                              : property.status === "تم البيع"
                              ? "secondary"
                              : "outline"
                          }
                        >
                          {property.status}
                        </Badge>
                      </TableCell>
                      <TableCell>
                        <div className="flex gap-2">
                          <Button
                            variant="ghost"
                            size="sm"
                            onClick={() => handleEditProperty(property)}
                          >
                            <Edit className="h-4 w-4" />
                          </Button>
                          <Button
                            variant="ghost"
                            size="sm"
                            onClick={() => handleDeleteProperty(property.id, property.title)}
                          >
                            <Trash2 className="h-4 w-4" />
                          </Button>
                        </div>
                      </TableCell>
                    </TableRow>
                  ))
                )}
              </TableBody>
            </Table>
          </CardContent>
        </Card>

        {/* نافذة التعديل */}
        <Dialog open={isEditDialogOpen} onOpenChange={setIsEditDialogOpen}>
          <DialogContent className="max-w-md">
            <DialogHeader>
              <DialogTitle>تعديل العقار</DialogTitle>
            </DialogHeader>
            <div className="space-y-4 max-h-96 overflow-y-auto">
              <div>
                <Label htmlFor="edit-title">عنوان العقار *</Label>
                <Input
                  id="edit-title"
                  value={formData.title}
                  onChange={(e) => {
                    console.log('📝 تعديل العنوان:', e.target.value);
                    setFormData({...formData, title: e.target.value});
                  }}
                  placeholder="أدخل عنوان العقار"
                />
              </div>
              <div>
                <Label htmlFor="edit-type">نوع العقار *</Label>
                <select
                  id="edit-type"
                  value={formData.type}
                  onChange={(e) => {
                    console.log('📝 تعديل النوع:', e.target.value);
                    setFormData({...formData, type: e.target.value});
                  }}
                  className="w-full p-2 border rounded-md"
                >
                  <option value="">اختر النوع</option>
                  <option value="شقة">شقة</option>
                  <option value="فيلا">فيلا</option>
                  <option value="أرض">أرض</option>
                  <option value="محل تجاري">محل تجاري</option>
                  <option value="مكتب">مكتب</option>
                </select>
              </div>
              <div>
                <Label htmlFor="edit-location">الموقع *</Label>
                <Input
                  id="edit-location"
                  value={formData.location}
                  onChange={(e) => {
                    console.log('📝 تعديل الموقع:', e.target.value);
                    setFormData({...formData, location: e.target.value});
                  }}
                  placeholder="المدينة - الحي"
                />
              </div>
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <Label htmlFor="edit-area">المساحة (م²)</Label>
                  <Input
                    id="edit-area"
                    type="number"
                    value={formData.area}
                    onChange={(e) => {
                      console.log('📝 تعديل المساحة:', e.target.value);
                      setFormData({...formData, area: e.target.value});
                    }}
                    placeholder="0"
                  />
                </div>
                <div>
                  <Label htmlFor="edit-price">السعر (ريال)</Label>
                  <Input
                    id="edit-price"
                    value={formData.price}
                    onChange={(e) => {
                      console.log('📝 تعديل السعر:', e.target.value);
                      setFormData({...formData, price: e.target.value});
                    }}
                    placeholder="0"
                  />
                </div>
              </div>
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <Label htmlFor="edit-bedrooms">عدد الغرف</Label>
                  <Input
                    id="edit-bedrooms"
                    type="number"
                    value={formData.bedrooms}
                    onChange={(e) => {
                      console.log('📝 تعديل عدد الغرف:', e.target.value);
                      setFormData({...formData, bedrooms: e.target.value});
                    }}
                    placeholder="0"
                  />
                </div>
                <div>
                  <Label htmlFor="edit-bathrooms">دورات المياه</Label>
                  <Input
                    id="edit-bathrooms"
                    type="number"
                    value={formData.bathrooms}
                    onChange={(e) => {
                      console.log('📝 تعديل دورات المياه:', e.target.value);
                      setFormData({...formData, bathrooms: e.target.value});
                    }}
                    placeholder="0"
                  />
                </div>
              </div>
              <div>
                <Label htmlFor="edit-status">الحالة</Label>
                <select
                  id="edit-status"
                  value={formData.status}
                  onChange={(e) => {
                    console.log('📝 تعديل الحالة:', e.target.value);
                    setFormData({...formData, status: e.target.value});
                  }}
                  className="w-full p-2 border rounded-md"
                >
                  <option value="متاح">متاح</option>
                  <option value="تحت العرض">تحت العرض</option>
                  <option value="تم البيع">تم البيع</option>
                </select>
              </div>
            </div>
            <DialogFooter>
              <Button variant="outline" onClick={() => {
                setIsEditDialogOpen(false);
                setPropertyToEdit(null);
              }}>
                <X className="mr-2 h-4 w-4" />
                إلغاء
              </Button>
              <Button onClick={handleSaveEdit}>
                <Save className="mr-2 h-4 w-4" />
                حفظ التغييرات
              </Button>
            </DialogFooter>
          </DialogContent>
        </Dialog>
      </div>
    </MainLayout>
  );
};

export default Properties;
