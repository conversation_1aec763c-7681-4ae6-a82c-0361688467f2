
import React, { useState, useEffect } from "react";
import MainLayout from "@/components/layout/MainLayout";
import { But<PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Tabs, <PERSON><PERSON>Content, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Badge } from "@/components/ui/badge";
import {
  Search,
  Plus,
  Filter,
  Building,
  Home,
  MapPin,
  Layers,
  Square,
} from "lucide-react";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog";
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import { Textarea } from "@/components/ui/textarea";
import { useForm } from "react-hook-form";
import { useToast } from "@/components/ui/use-toast";

const propertyTypes = ["شقة", "فيلا", "أرض", "محل تجاري", "مكتب"];

const propertiesData = [
  {
    id: "P-001",
    title: "فيلا فاخرة في حي الشرفية",
    type: "فيلا",
    location: "المدينة المنورة - الشرفية",
    area: 450,
    price: "2,500,000",
    bedrooms: 5,
    bathrooms: 6,
    status: "متاح",
  },
  {
    id: "P-002",
    title: "شقة مميزة في مجمع الياسمين",
    type: "شقة",
    location: "المدينة المنورة - قرب الجامعة",
    area: 180,
    price: "850,000",
    bedrooms: 3,
    bathrooms: 2,
    status: "تم البيع",
  },
  {
    id: "P-003",
    title: "أرض تجارية بالعوالي",
    type: "أرض",
    location: "المدينة المنورة - العوالي",
    area: 750,
    price: "1,900,000",
    bedrooms: null,
    bathrooms: null,
    status: "متاح",
  },
  {
    id: "P-004",
    title: "محل تجاري في مول الحمراء",
    type: "محل تجاري",
    location: "المدينة المنورة - طريق الملك عبدالله",
    area: 120,
    price: "1,200,000",
    bedrooms: null,
    bathrooms: 1,
    status: "متاح",
  },
  {
    id: "P-005",
    title: "فيلا دوبلكس في النرجس",
    type: "فيلا",
    location: "المدينة المنورة - النرجس",
    area: 380,
    price: "1,950,000",
    bedrooms: 4,
    bathrooms: 5,
    status: "تحت العرض",
  },
  {
    id: "P-006",
    title: "شقة علوية مع سطح خاص",
    type: "شقة",
    location: "المدينة المنورة - الأزهري",
    area: 220,
    price: "950,000",
    bedrooms: 4,
    bathrooms: 3,
    status: "تم البيع",
  },
  {
    id: "P-007",
    title: "مكتب إداري في برج الأعمال",
    type: "مكتب",
    location: "المدينة المنورة - وسط المدينة",
    area: 150,
    price: "1,100,000",
    bedrooms: null,
    bathrooms: 2,
    status: "متاح",
  },
  {
    id: "P-008",
    title: "أرض سكنية في مخطط الملك فهد",
    type: "أرض",
    location: "المدينة المنورة - مخطط الملك فهد",
    area: 625,
    price: "1,400,000",
    bedrooms: null,
    bathrooms: null,
    status: "متاح",
  },
];

const Properties: React.FC = () => {
  const [activeTab, setActiveTab] = useState("all");
  const [propertyTypeFilter, setPropertyTypeFilter] = useState("");
  const [searchQuery, setSearchQuery] = useState("");
  const { toast } = useToast();
  const [properties, setProperties] = useState(propertiesData);
  const [isAddDialogOpen, setIsAddDialogOpen] = useState(false);

  // نموذج إضافة عقار جديد
  const form = useForm({
    defaultValues: {
      title: "",
      type: "",
      location: "",
      area: "",
      price: "",
      bedrooms: "",
      bathrooms: "",
      description: "",
      status: "متاح",
    },
  });

  // استخدام useEffect للاستماع لحدث حذف العقار
  useEffect(() => {
    const handleDeleteProperty = (event: any) => {
      const { propertyId } = event.detail;
      // حذف العقار من القائمة
      setProperties(properties.filter(p => p.id !== propertyId));
    };

    // إضافة مستمع الحدث
    document.addEventListener('deleteProperty', handleDeleteProperty);

    // إزالة مستمع الحدث عند تفكيك المكون
    return () => {
      document.removeEventListener('deleteProperty', handleDeleteProperty);
    };
  }, [properties]);

  // إضافة عنصر لتخزين بيانات العقارات للمشاركة مع المكونات الفرعية
  useEffect(() => {
    const propertiesDataElement = document.getElementById('properties-data');
    if (propertiesDataElement) {
      propertiesDataElement.setAttribute('data-properties', JSON.stringify(properties));
    } else {
      const dataElement = document.createElement('div');
      dataElement.id = 'properties-data';
      dataElement.style.display = 'none';
      dataElement.setAttribute('data-properties', JSON.stringify(properties));
      document.body.appendChild(dataElement);
    }
  }, [properties]);

  const onSubmit = (data: any) => {
    // إنشاء معرف فريد للعقار الجديد
    const newId = `P-${String(properties.length + 1).padStart(3, '0')}`;

    // إنشاء عقار جديد
    const newProperty = {
      id: newId,
      title: data.title,
      type: data.type,
      location: data.location,
      area: Number(data.area),
      price: data.price,
      bedrooms: data.bedrooms ? Number(data.bedrooms) : null,
      bathrooms: data.bathrooms ? Number(data.bathrooms) : null,
      status: data.status,
    };

    // إضافة العقار الجديد إلى القائمة
    setProperties([...properties, newProperty]);

    // إغلاق نافذة الإضافة
    setIsAddDialogOpen(false);

    // إعادة تعيين النموذج
    form.reset();

    // عرض رسالة نجاح
    toast({
      title: "تمت الإضافة بنجاح",
      description: `تم إضافة العقار "${data.title}" بنجاح.`,
    });
  };

  const filteredProperties = properties
    .filter((property) => {
      if (activeTab === "all") return true;
      if (activeTab === "available") return property.status === "متاح";
      if (activeTab === "sold") return property.status === "تم البيع";
      if (activeTab === "pending") return property.status === "تحت العرض";
      return true;
    })
    .filter((property) => {
      if (!propertyTypeFilter) return true;
      return property.type === propertyTypeFilter;
    })
    .filter((property) => {
      if (!searchQuery) return true;
      return (
        property.title.includes(searchQuery) ||
        property.location.includes(searchQuery) ||
        property.id.includes(searchQuery)
      );
    });

  return (
    <MainLayout>
      <div className="space-y-6">
        <div className="flex justify-between items-center">
          <h1 className="text-2xl font-bold">إدارة العقارات</h1>
          <Dialog open={isAddDialogOpen} onOpenChange={setIsAddDialogOpen}>
            <DialogTrigger asChild>
              <Button className="gap-2" onClick={() => setIsAddDialogOpen(true)}>
                <Plus size={18} />
                إضافة عقار جديد
              </Button>
            </DialogTrigger>
            <DialogContent className="sm:max-w-[425px]">
              <DialogHeader>
                <DialogTitle>إضافة عقار جديد</DialogTitle>
                <DialogDescription>
                  قم بإدخال بيانات العقار الجديد. اضغط على "إضافة" عند الانتهاء.
                </DialogDescription>
              </DialogHeader>
              <Form {...form}>
                <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-4">
                  <FormField
                    control={form.control}
                    name="title"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>عنوان العقار</FormLabel>
                        <FormControl>
                          <Input placeholder="عنوان العقار" {...field} />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                  <FormField
                    control={form.control}
                    name="type"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>نوع العقار</FormLabel>
                        <Select
                          onValueChange={field.onChange}
                          defaultValue={field.value}
                        >
                          <FormControl>
                            <SelectTrigger>
                              <SelectValue placeholder="اختر نوع العقار" />
                            </SelectTrigger>
                          </FormControl>
                          <SelectContent>
                            {propertyTypes.map((type) => (
                              <SelectItem key={type} value={type}>
                                {type}
                              </SelectItem>
                            ))}
                          </SelectContent>
                        </Select>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                  <FormField
                    control={form.control}
                    name="location"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>الموقع</FormLabel>
                        <FormControl>
                          <Input placeholder="المدينة - الحي" {...field} />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                  <div className="grid grid-cols-2 gap-4">
                    <FormField
                      control={form.control}
                      name="area"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>المساحة (م²)</FormLabel>
                          <FormControl>
                            <Input type="number" placeholder="0" {...field} />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                    <FormField
                      control={form.control}
                      name="price"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>السعر (ريال)</FormLabel>
                          <FormControl>
                            <Input placeholder="0" {...field} />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                  </div>
                  <div className="grid grid-cols-2 gap-4">
                    <FormField
                      control={form.control}
                      name="bedrooms"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>عدد الغرف</FormLabel>
                          <FormControl>
                            <Input type="number" placeholder="0" {...field} />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                    <FormField
                      control={form.control}
                      name="bathrooms"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>دورات المياه</FormLabel>
                          <FormControl>
                            <Input type="number" placeholder="0" {...field} />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                  </div>
                  <FormField
                    control={form.control}
                    name="description"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>وصف العقار</FormLabel>
                        <FormControl>
                          <Textarea placeholder="وصف تفصيلي للعقار" {...field} />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                  <FormField
                    control={form.control}
                    name="status"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>الحالة</FormLabel>
                        <Select
                          onValueChange={field.onChange}
                          defaultValue={field.value}
                        >
                          <FormControl>
                            <SelectTrigger>
                              <SelectValue placeholder="اختر الحالة" />
                            </SelectTrigger>
                          </FormControl>
                          <SelectContent>
                            <SelectItem value="متاح">متاح</SelectItem>
                            <SelectItem value="تحت العرض">تحت العرض</SelectItem>
                            <SelectItem value="تم البيع">تم البيع</SelectItem>
                          </SelectContent>
                        </Select>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                  <DialogFooter>
                    <Button type="submit">إضافة</Button>
                  </DialogFooter>
                </form>
              </Form>
            </DialogContent>
          </Dialog>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
          <Card>
            <CardHeader className="pb-2">
              <CardTitle className="text-sm font-medium text-muted-foreground">
                إجمالي العقارات
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{properties.length}</div>
            </CardContent>
          </Card>
          <Card>
            <CardHeader className="pb-2">
              <CardTitle className="text-sm font-medium text-muted-foreground">
                العقارات المتاحة
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">
                {properties.filter((p) => p.status === "متاح").length}
              </div>
              <p className="text-xs text-muted-foreground mt-1">
                {(
                  (properties.filter((p) => p.status === "متاح").length /
                    properties.length) *
                  100
                ).toFixed(0)}
                % من إجمالي العقارات
              </p>
            </CardContent>
          </Card>
          <Card>
            <CardHeader className="pb-2">
              <CardTitle className="text-sm font-medium text-muted-foreground">
                العقارات المباعة
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">
                {properties.filter((p) => p.status === "تم البيع").length}
              </div>
              <p className="text-xs text-muted-foreground mt-1">
                {(
                  (properties.filter((p) => p.status === "تم البيع").length /
                    properties.length) *
                  100
                ).toFixed(0)}
                % من إجمالي العقارات
              </p>
            </CardContent>
          </Card>
          <Card>
            <CardHeader className="pb-2">
              <CardTitle className="text-sm font-medium text-muted-foreground">
                تحت العرض
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">
                {properties.filter((p) => p.status === "تحت العرض").length}
              </div>
              <p className="text-xs text-muted-foreground mt-1">
                {(
                  (properties.filter((p) => p.status === "تحت العرض").length /
                    properties.length) *
                  100
                ).toFixed(0)}
                % من إجمالي العقارات
              </p>
            </CardContent>
          </Card>
        </div>

        <Card>
          <CardHeader>
            <div className="flex flex-col gap-4 md:flex-row md:items-center md:justify-between">
              <CardTitle>قائمة العقارات</CardTitle>
              <div className="flex flex-col gap-4 md:flex-row md:items-center">
                <div className="relative">
                  <Search className="absolute right-3 top-1/2 h-4 w-4 -translate-y-1/2 text-muted-foreground" />
                  <Input
                    placeholder="بحث في العقارات..."
                    className="pr-9 w-full md:w-[250px]"
                    value={searchQuery}
                    onChange={(e) => setSearchQuery(e.target.value)}
                  />
                </div>
                <Select value={propertyTypeFilter} onValueChange={setPropertyTypeFilter}>
                  <SelectTrigger className="w-full md:w-[180px]">
                    <SelectValue placeholder="نوع العقار" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all-types">جميع الأنواع</SelectItem>
                    {propertyTypes.map((type) => (
                      <SelectItem key={type} value={type}>
                        {type}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
                <Button variant="outline" size="icon">
                  <Filter size={18} />
                </Button>
              </div>
            </div>
          </CardHeader>
          <CardContent>
            <Tabs defaultValue="all" onValueChange={setActiveTab} value={activeTab}>
              <TabsList className="grid w-full grid-cols-4">
                <TabsTrigger value="all">جميع العقارات</TabsTrigger>
                <TabsTrigger value="available">متاح</TabsTrigger>
                <TabsTrigger value="pending">تحت العرض</TabsTrigger>
                <TabsTrigger value="sold">تم البيع</TabsTrigger>
              </TabsList>
              <TabsContent value="all" className="pt-4">
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                  {filteredProperties.map((property) => (
                    <PropertyCard key={property.id} property={property} />
                  ))}
                </div>
              </TabsContent>
              <TabsContent value="available" className="pt-4">
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                  {filteredProperties.map((property) => (
                    <PropertyCard key={property.id} property={property} />
                  ))}
                </div>
              </TabsContent>
              <TabsContent value="pending" className="pt-4">
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                  {filteredProperties.map((property) => (
                    <PropertyCard key={property.id} property={property} />
                  ))}
                </div>
              </TabsContent>
              <TabsContent value="sold" className="pt-4">
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                  {filteredProperties.map((property) => (
                    <PropertyCard key={property.id} property={property} />
                  ))}
                </div>
              </TabsContent>
            </Tabs>
          </CardContent>
        </Card>
      </div>
    </MainLayout>
  );
};

interface PropertyCardProps {
  property: {
    id: string;
    title: string;
    type: string;
    location: string;
    area: number;
    price: string;
    bedrooms: number | null;
    bathrooms: number | null;
    status: string;
  };
}

const PropertyCard: React.FC<PropertyCardProps> = ({ property }) => {
  const { toast } = useToast();
  const [showDetails, setShowDetails] = useState(false);
  const [properties, setProperties] = useState<any[]>([]);

  // استخدام useEffect للحصول على قائمة العقارات من الكومبوننت الأب
  useEffect(() => {
    const propertiesFromParent = document.getElementById('properties-data')?.getAttribute('data-properties');
    if (propertiesFromParent) {
      try {
        setProperties(JSON.parse(propertiesFromParent));
      } catch (e) {
        console.error('Error parsing properties data:', e);
      }
    }
  }, []);

  const handleViewDetails = () => {
    setShowDetails(true);
  };

  const handleDeleteProperty = () => {
    if (confirm(`هل أنت متأكد من حذف العقار "${property.title}"؟`)) {
      // إرسال حدث مخصص للكومبوننت الأب لحذف العقار
      const event = new CustomEvent('deleteProperty', {
        detail: { propertyId: property.id }
      });
      document.dispatchEvent(event);

      toast({
        title: "تم الحذف بنجاح",
        description: `تم حذف العقار "${property.title}" بنجاح.`,
      });
    }
  };

  return (
    <>
      <Card className="overflow-hidden">
        <div className="h-48 bg-gray-200 flex items-center justify-center">
          <Building size={48} className="text-gray-400" />
        </div>
        <CardContent className="p-4">
          <div className="flex justify-between items-start">
            <h3 className="font-bold">{property.title}</h3>
            <Badge
              variant={
                property.status === "متاح"
                  ? "default"
                  : property.status === "تحت العرض"
                  ? "secondary"
                  : "outline"
              }
            >
              {property.status}
            </Badge>
          </div>
          <div className="flex items-center gap-1 text-muted-foreground mt-2">
            <MapPin size={14} />
            <span className="text-sm">{property.location}</span>
          </div>
          <div className="flex items-center gap-1 text-muted-foreground mt-1">
            <Square size={14} />
            <span className="text-sm">{property.area} م²</span>
          </div>
          <div className="flex items-center gap-1 text-muted-foreground mt-1">
            <Layers size={14} />
            <span className="text-sm">{property.type}</span>
          </div>
          <div className="mt-3 flex justify-between items-center">
            <div className="font-bold text-lg">{property.price} ر.س</div>
            <div className="flex gap-4">
              {property.bedrooms !== null && (
                <div className="flex items-center gap-1">
                  <Home size={14} />
                  <span>{property.bedrooms}</span>
                </div>
              )}
              {property.bathrooms !== null && (
                <div className="flex items-center gap-1">
                  <svg
                    width="14"
                    height="14"
                    viewBox="0 0 24 24"
                    fill="none"
                    xmlns="http://www.w3.org/2000/svg"
                  >
                    <path
                      d="M9 22H15M9 22V19M9 22H7C5.89543 22 5 21.1046 5 20V14M15 22V19M15 22H17C18.1046 22 19 21.1046 19 20V14M5 14V11C5 9.89543 5.89543 9 7 9H17C18.1046 9 19 9.89543 19 11V14M5 14H19M15 4V5C15 6.10457 14.1046 7 13 7H11C9.89543 7 9 6.10457 9 5V4C9 2.89543 9.89543 2 11 2H13C14.1046 2 15 2.89543 15 4Z"
                      stroke="currentColor"
                      strokeWidth="2"
                      strokeLinecap="round"
                    />
                  </svg>
                  <span>{property.bathrooms}</span>
                </div>
              )}
            </div>
          </div>
          <Button className="w-full mt-4" variant="outline" onClick={handleViewDetails}>
            عرض التفاصيل
          </Button>
        </CardContent>
      </Card>

      <Dialog open={showDetails} onOpenChange={setShowDetails}>
        <DialogContent className="sm:max-w-[600px]">
          <DialogHeader>
            <DialogTitle>{property.title}</DialogTitle>
            <DialogDescription>
              {property.location}
            </DialogDescription>
          </DialogHeader>
          <div className="h-64 bg-gray-200 flex items-center justify-center rounded-md mb-4">
            <Building size={64} className="text-gray-400" />
          </div>
          <div className="grid grid-cols-2 gap-4 mb-4">
            <div>
              <h4 className="text-sm font-semibold mb-1">نوع العقار</h4>
              <p>{property.type}</p>
            </div>
            <div>
              <h4 className="text-sm font-semibold mb-1">المساحة</h4>
              <p>{property.area} م²</p>
            </div>
            <div>
              <h4 className="text-sm font-semibold mb-1">السعر</h4>
              <p>{property.price} ريال</p>
            </div>
            <div>
              <h4 className="text-sm font-semibold mb-1">الحالة</h4>
              <Badge
                variant={
                  property.status === "متاح"
                    ? "default"
                    : property.status === "تحت العرض"
                    ? "secondary"
                    : "outline"
                }
              >
                {property.status}
              </Badge>
            </div>
            {property.bedrooms !== null && (
              <div>
                <h4 className="text-sm font-semibold mb-1">عدد الغرف</h4>
                <p>{property.bedrooms}</p>
              </div>
            )}
            {property.bathrooms !== null && (
              <div>
                <h4 className="text-sm font-semibold mb-1">دورات المياه</h4>
                <p>{property.bathrooms}</p>
              </div>
            )}
          </div>
          <div className="mb-4">
            <h4 className="text-sm font-semibold mb-1">الوصف</h4>
            <p className="text-sm text-muted-foreground">
              هذا وصف تفصيلي للعقار يتضمن معلومات إضافية عن الموقع والمميزات والخدمات المتوفرة.
            </p>
          </div>
          <DialogFooter className="flex gap-2">
            <Button variant="outline" onClick={() => {
              toast({
                title: "تم حجز العقار",
                description: "تم حجز العقار بنجاح وسيتم التواصل معك قريبًا.",
              });
              setShowDetails(false);
            }}>
              حجز العقار
            </Button>
            <Button variant="destructive" onClick={handleDeleteProperty}>
              حذف العقار
            </Button>
            <Button onClick={() => setShowDetails(false)}>إغلاق</Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </>
  );
};

export default Properties;

