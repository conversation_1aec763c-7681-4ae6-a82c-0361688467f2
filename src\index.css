
@import url('https://fonts.googleapis.com/css2?family=Tajawal:wght@300;400;500;700&display=swap');

@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  :root {
    --background: 0 0% 100%;
    --foreground: 222.2 84% 4.9%;

    --card: 0 0% 100%;
    --card-foreground: 222.2 84% 4.9%;

    --popover: 0 0% 100%;
    --popover-foreground: 222.2 84% 4.9%;

    --primary: 212 76% 45%;
    --primary-foreground: 210 40% 98%;

    --secondary: 142 52% 46%;
    --secondary-foreground: 355.7 100% 100%;

    --muted: 210 40% 96.1%;
    --muted-foreground: 215.4 16.3% 46.9%;

    --accent: 210 40% 96.1%;
    --accent-foreground: 222.2 47.4% 11.2%;

    --destructive: 0 84.2% 60.2%;
    --destructive-foreground: 210 40% 98%;

    --border: 214.3 31.8% 91.4%;
    --input: 214.3 31.8% 91.4%;
    --ring: 222.2 84% 4.9%;

    --radius: 0.5rem;

    --sidebar-background: 212 76% 20%;
    --sidebar-foreground: 0 0% 100%;
    --sidebar-primary: 142 52% 46%;
    --sidebar-primary-foreground: 0 0% 100%;
    --sidebar-accent: 212 76% 15%;
    --sidebar-accent-foreground: 0 0% 90%;
    --sidebar-border: 212 76% 17%;
    --sidebar-ring: 217.2 91.2% 59.8%;
  }

  .dark {
    --background: 222.2 84% 4.9%;
    --foreground: 210 40% 98%;

    --card: 222.2 84% 4.9%;
    --card-foreground: 210 40% 98%;

    --popover: 222.2 84% 4.9%;
    --popover-foreground: 210 40% 98%;

    --primary: 217.2 91.2% 59.8%;
    --primary-foreground: 222.2 47.4% 11.2%;

    --secondary: 142 52% 46%;
    --secondary-foreground: 0 0% 100%;

    --muted: 217.2 32.6% 17.5%;
    --muted-foreground: 215 20.2% 65.1%;

    --accent: 217.2 32.6% 17.5%;
    --accent-foreground: 210 40% 98%;

    --destructive: 0 62.8% 30.6%;
    --destructive-foreground: 210 40% 98%;

    --border: 217.2 32.6% 17.5%;
    --input: 217.2 32.6% 17.5%;
    --ring: 212.7 26.8% 83.9%;

    --sidebar-background: 215 28% 17%;
    --sidebar-foreground: 210 40% 98%;
    --sidebar-primary: 142 52% 46%;
    --sidebar-primary-foreground: 0 0% 100%;
    --sidebar-accent: 215 28% 12%;
    --sidebar-accent-foreground: 210 40% 98%;
    --sidebar-border: 215 28% 12%;
    --sidebar-ring: 217.2 91.2% 59.8%;
  }
}

@layer base {
  * {
    @apply border-border;
  }

  html {
    font-family: 'Tajawal', sans-serif;
  }

  body {
    @apply bg-background text-foreground;
  }

  [dir="rtl"] * {
    text-align: right;
  }
}

@layer components {
  .stat-card {
    @apply bg-white rounded-lg shadow-md p-5 border border-border transition-all hover:border-primary/20 hover:shadow-lg;
  }

  .dashboard-grid {
    @apply grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-4;
  }

  .stats-container {
    @apply grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-4 mb-6;
  }
}
