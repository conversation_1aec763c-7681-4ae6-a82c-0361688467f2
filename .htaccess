# Security Headers
Header always set X-Content-Type-Options nosniff
Header always set X-Frame-Options DENY
Header always set X-XSS-Protection "1; mode=block"
Header always set Referrer-Policy "strict-origin-when-cross-origin"
Header always set Content-Security-Policy "default-src 'self'; script-src 'self' 'unsafe-inline'; style-src 'self' 'unsafe-inline' fonts.googleapis.com; font-src 'self' fonts.gstatic.com; img-src 'self' data:; connect-src 'self';"

# CORS Headers for API
<Files "api.php">
    Header always set Access-Control-Allow-Origin "*"
    Header always set Access-Control-Allow-Methods "GET, POST, PUT, DELETE, OPTIONS"
    Header always set Access-Control-Allow-Headers "Content-Type, Authorization"
</Files>

# Protect data directory
<Directory "data">
    Order Deny,Allow
    Deny from all
</Directory>

# Protect backup files
<FilesMatch "\.(json|bak|backup)$">
    Order Deny,Allow
    Deny from all
    <Files "api.php">
        Allow from all
    </Files>
</FilesMatch>

# Enable compression
<IfModule mod_deflate.c>
    AddOutputFilterByType DEFLATE text/plain
    AddOutputFilterByType DEFLATE text/html
    AddOutputFilterByType DEFLATE text/xml
    AddOutputFilterByType DEFLATE text/css
    AddOutputFilterByType DEFLATE application/xml
    AddOutputFilterByType DEFLATE application/xhtml+xml
    AddOutputFilterByType DEFLATE application/rss+xml
    AddOutputFilterByType DEFLATE application/javascript
    AddOutputFilterByType DEFLATE application/x-javascript
    AddOutputFilterByType DEFLATE application/json
</IfModule>

# Cache static files
<IfModule mod_expires.c>
    ExpiresActive On
    ExpiresByType text/css "access plus 1 month"
    ExpiresByType application/javascript "access plus 1 month"
    ExpiresByType image/png "access plus 1 month"
    ExpiresByType image/jpg "access plus 1 month"
    ExpiresByType image/jpeg "access plus 1 month"
    ExpiresByType image/gif "access plus 1 month"
    ExpiresByType image/svg+xml "access plus 1 month"
</IfModule>

# Redirect to index.php if file doesn't exist
RewriteEngine On
RewriteCond %{REQUEST_FILENAME} !-f
RewriteCond %{REQUEST_FILENAME} !-d
RewriteCond %{REQUEST_URI} !^/api\.php
RewriteRule ^(.*)$ index.php [QSA,L]

# Prevent access to sensitive files
<FilesMatch "\.(htaccess|htpasswd|ini|log|sh|inc|bak)$">
    Order Allow,Deny
    Deny from all
</FilesMatch>

# PHP Security Settings
<IfModule mod_php7.c>
    php_flag display_errors Off
    php_flag log_errors On
    php_flag expose_php Off
    php_value max_execution_time 30
    php_value max_input_time 30
    php_value memory_limit 128M
    php_value post_max_size 10M
    php_value upload_max_filesize 5M
</IfModule>

<IfModule mod_php8.c>
    php_flag display_errors Off
    php_flag log_errors On
    php_flag expose_php Off
    php_value max_execution_time 30
    php_value max_input_time 30
    php_value memory_limit 128M
    php_value post_max_size 10M
    php_value upload_max_filesize 5M
</IfModule>
