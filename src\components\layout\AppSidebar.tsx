import React, { useState } from "react";
import { Link, useLocation } from "react-router-dom";
import { cn } from "@/lib/utils";
import {
  BarChart3,
  Building2,
  FileText,
  Home,
  UserCog,
  Users,
  Layers,
  Wrench,
  DollarSign,
  Menu,
  X,
  ShoppingBag,
} from "lucide-react";
import { Button } from "@/components/ui/button";

type SidebarItem = {
  title: string;
  icon: React.ElementType;
  path: string;
  role: string[];
};

const sidebarItems: SidebarItem[] = [
  {
    title: "لوحة التحكم",
    icon: Home,
    path: "/",
    role: ["admin", "manager", "user"],
  },
  {
    title: "المستخدمين",
    icon: Users,
    path: "/users",
    role: ["admin"],
  },
  {
    title: "إدارة العقارات",
    icon: Building2,
    path: "/properties",
    role: ["admin", "manager", "sales"],
  },
  {
    title: "المبيعات",
    icon: DollarSign,
    path: "/sales",
    role: ["admin", "manager", "sales"],
  },
  {
    title: "المشتريات",
    icon: ShoppingBag,
    path: "/procurement",
    role: ["admin", "manager", "procurement"],
  },
  {
    title: "الموردين",
    icon: Layers,
    path: "/suppliers",
    role: ["admin", "manager", "procurement"],
  },
  {
    title: "المستخلصات",
    icon: FileText,
    path: "/invoices",
    role: ["admin", "manager", "finance"],
  },
  {
    title: "الصيانة",
    icon: Wrench,
    path: "/maintenance",
    role: ["admin", "manager", "maintenance"],
  },
  {
    title: "التقارير",
    icon: BarChart3,
    path: "/reports",
    role: ["admin", "manager"],
  },
  {
    title: "الصلاحيات",
    icon: UserCog,
    path: "/permissions",
    role: ["admin"],
  },
];

interface AppSidebarProps {
  userRole?: string;
}

const AppSidebar: React.FC<AppSidebarProps> = ({ userRole = "admin" }) => {
  const [isSidebarOpen, setIsSidebarOpen] = useState(true);
  const location = useLocation();

  const toggleSidebar = () => {
    setIsSidebarOpen(!isSidebarOpen);
  };

  // Filter sidebar items based on user role
  const filteredItems = sidebarItems.filter((item) =>
    item.role.includes(userRole)
  );

  return (
    <>
      <div
        className={cn(
          "bg-sidebar fixed top-0 h-full transition-all duration-300 z-20 border-l border-sidebar-border flex flex-col",
          isSidebarOpen ? "w-64" : "w-16"
        )}
      >
        <div className="flex h-16 items-center px-4 border-b border-sidebar-border justify-between">
          <div className={cn("flex items-center gap-2", !isSidebarOpen && "hidden")}>
            <div className="h-8 w-8 rounded-md bg-rafe-secondary flex items-center justify-center text-white font-bold">
              ر
            </div>
            <h1 className="text-white font-bold text-lg">رافع العقارية</h1>
          </div>
          <Button
            variant="ghost"
            size="icon"
            className="text-sidebar-foreground hover:bg-sidebar-accent hover:text-sidebar-foreground"
            onClick={toggleSidebar}
          >
            {isSidebarOpen ? <X size={20} /> : <Menu size={20} />}
          </Button>
        </div>

        <div className="flex-1 overflow-y-auto py-4">
          <nav className="px-2 space-y-1">
            {filteredItems.map((item) => {
              const IconComponent = item.icon;
              const isActive = location.pathname === item.path;

              return (
                <Link
                  key={item.path}
                  to={item.path}
                  className={cn(
                    "flex items-center px-3 py-2 rounded-md transition-colors",
                    isActive
                      ? "bg-sidebar-primary text-sidebar-primary-foreground"
                      : "text-sidebar-foreground hover:bg-sidebar-accent hover:text-sidebar-accent-foreground",
                    !isSidebarOpen && "justify-center"
                  )}
                >
                  <IconComponent size={20} />
                  {isSidebarOpen && <span className="mr-3">{item.title}</span>}
                </Link>
              );
            })}
          </nav>
        </div>

        <div className="p-4 border-t border-sidebar-border">
          {isSidebarOpen && (
            <div className="text-sidebar-foreground text-xs">
              <p>فرع المدينة المنورة</p>
              <p>إصدار 1.0</p>
            </div>
          )}
        </div>
      </div>

      {/* Main content padding based on sidebar state */}
      <div
        className={cn(
          "transition-all duration-300",
          isSidebarOpen ? "mr-64" : "mr-16"
        )}
      />
    </>
  );
};

export default AppSidebar;
