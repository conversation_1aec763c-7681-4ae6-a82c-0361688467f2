import React, { useState } from "react";
import MainLayout from "@/components/layout/MainLayout";
import { Button } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Ta<PERSON>, Ta<PERSON><PERSON>ontent, Ta<PERSON>List, TabsTrigger } from "@/components/ui/tabs";
import { Badge } from "@/components/ui/badge";
import {
  Search,
  Plus,
  FileText,
  Clock,
  CheckCircle2,
  AlertCircle,
  XCircle,
  Filter,
  Calendar,
} from "lucide-react";
import {
  Table,
  TableBody,
  TableCaption,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";

const maintenanceData = [
  {
    id: "MT-001",
    propertyName: "فيلا الشرفية - وحدة رقم 3",
    requestedBy: "أحمد محمد (مستأجر)",
    type: "تكييف",
    priority: "عالية",
    date: "2024-05-10",
    status: "مكتمل",
    cost: 1200,
  },
  {
    id: "MT-002",
    propertyName: "مجمع الياسمين - شقة 102",
    requestedBy: "سارة عبدالله (مالك)",
    type: "سباكة",
    priority: "متوسطة",
    date: "2024-05-15",
    status: "قيد التنفيذ",
    cost: 850,
  },
  {
    id: "MT-003",
    propertyName: "برج الضيافة - مكتب 304",
    requestedBy: "خالد العمري (مستأجر)",
    type: "كهرباء",
    priority: "عالية",
    date: "2024-05-12",
    status: "قيد التنفيذ",
    cost: 1500,
  },
  {
    id: "MT-004",
    propertyName: "فيلا النرجس - وحدة رقم 7",
    requestedBy: "محمد سعيد (مالك)",
    type: "نجارة",
    priority: "منخفضة",
    date: "2024-05-08",
    status: "مكتمل",
    cost: 600,
  },
  {
    id: "MT-005",
    propertyName: "مجمع الواحة - شقة 205",
    requestedBy: "سلمان الفيصل (مستأجر)",
    type: "تكييف",
    priority: "متوسطة",
    date: "2024-05-17",
    status: "معلق",
    cost: 0,
  },
  {
    id: "MT-006",
    propertyName: "برج المستقبل - مكتب 512",
    requestedBy: "نورة الشمري (مالكة)",
    type: "سباكة",
    priority: "عالية",
    date: "2024-05-16",
    status: "جديد",
    cost: 0,
  },
  {
    id: "MT-007",
    propertyName: "فيلا السلام - وحدة رقم 2",
    requestedBy: "عمر راشد (مستأجر)",
    type: "كهرباء",
    priority: "عالية",
    date: "2024-05-18",
    status: "مرفوض",
    cost: 0,
  },
];

const getMaintenanceStatusColor = (status: string) => {
  switch (status) {
    case "مكتمل":
      return "default";
    case "قيد التنفيذ":
      return "secondary";
    case "جديد":
      return "outline";
    case "معلق":
      return "outline";
    case "مرفوض":
      return "destructive";
    default:
      return "outline";
  }
};

const getMaintenancePriorityColor = (priority: string) => {
  switch (priority) {
    case "عالية":
      return "destructive";
    case "متوسطة":
      return "secondary";
    case "منخفضة":
      return "outline";
    default:
      return "outline";
  }
};

const getMaintenanceIcon = (status: string) => {
  switch (status) {
    case "مكتمل":
      return <CheckCircle2 className="w-4 h-4 text-green-500" />;
    case "قيد التنفيذ":
      return <Clock className="w-4 h-4 text-blue-500" />;
    case "معلق":
      return <AlertCircle className="w-4 h-4 text-amber-500" />;
    case "مرفوض":
      return <XCircle className="w-4 h-4 text-red-500" />;
    case "جديد":
      return <Calendar className="w-4 h-4 text-gray-500" />;
    default:
      return null;
  }
};

const Maintenance: React.FC = () => {
  const [activeTab, setActiveTab] = useState("all");
  const [searchQuery, setSearchQuery] = useState("");
  const [statusFilter, setStatusFilter] = useState("");

  const filteredRequests = maintenanceData
    .filter((request) => {
      if (activeTab === "all") return true;
      if (activeTab === "new") return request.status === "جديد";
      if (activeTab === "inProgress") return request.status === "قيد التنفيذ";
      if (activeTab === "completed") return request.status === "مكتمل";
      if (activeTab === "suspended") return request.status === "معلق" || request.status === "مرفوض";
      return true;
    })
    .filter((request) => {
      if (!statusFilter) return true;
      return request.status === statusFilter;
    })
    .filter((request) => {
      if (!searchQuery) return true;
      return (
        request.propertyName.includes(searchQuery) ||
        request.id.includes(searchQuery) ||
        request.type.includes(searchQuery) ||
        request.requestedBy.includes(searchQuery)
      );
    });

  const completedCount = maintenanceData.filter(
    (request) => request.status === "مكتمل"
  ).length;
  const pendingCount = maintenanceData.filter(
    (request) => request.status === "قيد التنفيذ"
  ).length;
  const newCount = maintenanceData.filter(
    (request) => request.status === "جديد"
  ).length;
  const suspendedCount = maintenanceData.filter(
    (request) => request.status === "معلق" || request.status === "مرفوض"
  ).length;

  const totalCost = maintenanceData.reduce((sum, request) => sum + request.cost, 0);

  return (
    <MainLayout>
      <div className="space-y-6">
        <div className="flex justify-between items-center">
          <h1 className="text-2xl font-bold">الصيانة</h1>
          <div className="flex gap-3">
            <Button variant="outline" className="gap-2">
              <FileText size={18} />
              تصدير التقرير
            </Button>
            <Button className="gap-2">
              <Plus size={18} />
              طلب صيانة جديد
            </Button>
          </div>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-5 gap-4">
          <Card>
            <CardHeader className="pb-2">
              <CardTitle className="text-sm font-medium text-muted-foreground">
                طلبات الصيانة
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{maintenanceData.length}</div>
            </CardContent>
          </Card>
          <Card>
            <CardHeader className="pb-2">
              <CardTitle className="text-sm font-medium text-muted-foreground">
                الجديدة
              </CardTitle>
            </CardHeader>
            <CardContent className="flex justify-between items-center">
              <div className="text-2xl font-bold">{newCount}</div>
              <Badge variant="outline">{Math.round((newCount / maintenanceData.length) * 100)}%</Badge>
            </CardContent>
          </Card>
          <Card>
            <CardHeader className="pb-2">
              <CardTitle className="text-sm font-medium text-muted-foreground">
                قيد التنفيذ
              </CardTitle>
            </CardHeader>
            <CardContent className="flex justify-between items-center">
              <div className="text-2xl font-bold">{pendingCount}</div>
              <Badge variant="secondary">{Math.round((pendingCount / maintenanceData.length) * 100)}%</Badge>
            </CardContent>
          </Card>
          <Card>
            <CardHeader className="pb-2">
              <CardTitle className="text-sm font-medium text-muted-foreground">
                المكتملة
              </CardTitle>
            </CardHeader>
            <CardContent className="flex justify-between items-center">
              <div className="text-2xl font-bold">{completedCount}</div>
              <Badge>{Math.round((completedCount / maintenanceData.length) * 100)}%</Badge>
            </CardContent>
          </Card>
          <Card>
            <CardHeader className="pb-2">
              <CardTitle className="text-sm font-medium text-muted-foreground">
                تكلفة الصيانة
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{totalCost} ريال</div>
            </CardContent>
          </Card>
        </div>

        <Card>
          <CardHeader>
            <div className="flex flex-col gap-4 md:flex-row md:items-center md:justify-between">
              <CardTitle>طلبات الصيانة</CardTitle>
              <div className="flex flex-col gap-4 md:flex-row md:items-center">
                <div className="relative">
                  <Search className="absolute right-3 top-1/2 h-4 w-4 -translate-y-1/2 text-muted-foreground" />
                  <Input
                    placeholder="بحث في الطلبات..."
                    className="pr-9 max-w-[250px]"
                    value={searchQuery}
                    onChange={(e) => setSearchQuery(e.target.value)}
                  />
                </div>
                <Select value={statusFilter} onValueChange={setStatusFilter}>
                  <SelectTrigger className="w-[180px]">
                    <SelectValue placeholder="الحالة" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all-statuses">جميع الحالات</SelectItem>
                    <SelectItem value="جديد">جديد</SelectItem>
                    <SelectItem value="قيد التنفيذ">قيد التنفيذ</SelectItem>
                    <SelectItem value="مكتمل">مكتمل</SelectItem>
                    <SelectItem value="معلق">معلق</SelectItem>
                    <SelectItem value="مرفوض">مرفوض</SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </div>
          </CardHeader>
          <CardContent>
            <Tabs defaultValue="all" onValueChange={setActiveTab} value={activeTab}>
              <TabsList className="grid w-full grid-cols-5">
                <TabsTrigger value="all">الكل</TabsTrigger>
                <TabsTrigger value="new">جديد</TabsTrigger>
                <TabsTrigger value="inProgress">قيد التنفيذ</TabsTrigger>
                <TabsTrigger value="completed">مكتمل</TabsTrigger>
                <TabsTrigger value="suspended">معلق / مرفوض</TabsTrigger>
              </TabsList>
              <TabsContent value="all" className="pt-4">
                <MaintenanceTable requests={filteredRequests} />
              </TabsContent>
              <TabsContent value="new" className="pt-4">
                <MaintenanceTable requests={filteredRequests} />
              </TabsContent>
              <TabsContent value="inProgress" className="pt-4">
                <MaintenanceTable requests={filteredRequests} />
              </TabsContent>
              <TabsContent value="completed" className="pt-4">
                <MaintenanceTable requests={filteredRequests} />
              </TabsContent>
              <TabsContent value="suspended" className="pt-4">
                <MaintenanceTable requests={filteredRequests} />
              </TabsContent>
            </Tabs>
          </CardContent>
        </Card>
      </div>
    </MainLayout>
  );
};

interface MaintenanceTableProps {
  requests: {
    id: string;
    propertyName: string;
    requestedBy: string;
    type: string;
    priority: string;
    date: string;
    status: string;
    cost: number;
  }[];
}

const MaintenanceTable: React.FC<MaintenanceTableProps> = ({ requests }) => {
  return (
    <Table>
      <TableCaption>قائمة بجميع طلبات الصيانة</TableCaption>
      <TableHeader>
        <TableRow>
          <TableHead>رقم الطلب</TableHead>
          <TableHead>الوحدة</TableHead>
          <TableHead>نوع الصيانة</TableHead>
          <TableHead>مقدم الطلب</TableHead>
          <TableHead>الأولوية</TableHead>
          <TableHead>التاريخ</TableHead>
          <TableHead>التكلفة</TableHead>
          <TableHead>الحالة</TableHead>
        </TableRow>
      </TableHeader>
      <TableBody>
        {requests.map((request) => (
          <TableRow key={request.id} className="cursor-pointer hover:bg-muted/50">
            <TableCell className="font-medium">{request.id}</TableCell>
            <TableCell>{request.propertyName}</TableCell>
            <TableCell>{request.type}</TableCell>
            <TableCell>{request.requestedBy}</TableCell>
            <TableCell>
              <Badge variant={getMaintenancePriorityColor(request.priority)}>
                {request.priority}
              </Badge>
            </TableCell>
            <TableCell>
              {new Date(request.date).toLocaleDateString("ar-SA")}
            </TableCell>
            <TableCell>
              {request.cost === 0 ? "-" : `${request.cost} ريال`}
            </TableCell>
            <TableCell>
              <div className="flex items-center gap-2">
                {getMaintenanceIcon(request.status)}
                <Badge variant={getMaintenanceStatusColor(request.status)}>
                  {request.status}
                </Badge>
              </div>
            </TableCell>
          </TableRow>
        ))}
      </TableBody>
    </Table>
  );
};

export default Maintenance;
