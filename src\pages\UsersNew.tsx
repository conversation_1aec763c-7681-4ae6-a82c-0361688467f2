import React, { useState, useEffect } from "react";
import MainLayout from "@/components/layout/MainLayout";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Badge } from "@/components/ui/badge";
import {
  Table,
  TableBody,
  TableCaption,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog";
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import { useForm } from "react-hook-form";
import { Search, Plus, UserCog, Shield, Mail, Eye, EyeOff, User, Key } from "lucide-react";
import { useToast } from "@/components/ui/use-toast";
import UsersTable from "@/components/users/UsersTable";
import BackupManager from "@/components/backup/BackupManager";
import { simpleUserService } from "@/services/simpleUserService";
import { User as UserType } from "@/lib/simpleDatabase";

// بيانات المستخدمين النموذجية
const initialUsers = [
  {
    id: 1,
    name: "أحمد محمد",
    email: "<EMAIL>",
    role: "مدير",
    department: "المبيعات",
    status: "نشط",
    lastActive: "2024-05-20",
  },
  {
    id: 2,
    name: "سارة عبدالله",
    email: "<EMAIL>",
    role: "مسؤول",
    department: "المالية",
    status: "نشط",
    lastActive: "2024-05-22",
  },
  {
    id: 3,
    name: "خالد العمري",
    email: "<EMAIL>",
    role: "موظف",
    department: "المبيعات",
    status: "غير نشط",
    lastActive: "2024-04-15",
  },
  {
    id: 4,
    name: "نورة الشمري",
    email: "<EMAIL>",
    role: "مدير",
    department: "الصيانة",
    status: "نشط",
    lastActive: "2024-05-21",
  },
  {
    id: 5,
    name: "فيصل السعيد",
    email: "<EMAIL>",
    role: "موظف",
    department: "المشتريات",
    status: "نشط",
    lastActive: "2024-05-19",
  },
];

const UsersNew = () => {
  const { toast } = useToast();
  const [activeTab, setActiveTab] = useState("all");
  const [searchQuery, setSearchQuery] = useState("");
  const [roleFilter, setRoleFilter] = useState("");
  const [showPassword, setShowPassword] = useState(false);
  const [users, setUsers] = useState<UserType[]>([]);
  const [isAddDialogOpen, setIsAddDialogOpen] = useState(false);
  const [isEditDialogOpen, setIsEditDialogOpen] = useState(false);
  const [userToEdit, setUserToEdit] = useState<UserType | null>(null);
  const [isLoading, setIsLoading] = useState(true);

  // تحميل المستخدمين من قاعدة البيانات
  useEffect(() => {
    loadUsers();
  }, []);

  const loadUsers = async () => {
    try {
      setIsLoading(true);
      console.log('بدء تحميل المستخدمين...');

      // تهيئة البيانات الأولية إذا لم تكن موجودة
      await simpleUserService.initializeDefaultUsers();
      console.log('تم تهيئة البيانات الأولية');

      // تحميل جميع المستخدمين
      const allUsers = await simpleUserService.getAllUsers();
      console.log('تم تحميل المستخدمين:', allUsers);

      setUsers(allUsers);
    } catch (error) {
      console.error('خطأ في تحميل المستخدمين:', error);
      toast({
        title: "خطأ في تحميل البيانات",
        description: "حدث خطأ أثناء تحميل بيانات المستخدمين.",
        variant: "destructive",
      });
    } finally {
      setIsLoading(false);
    }
  };

  // نموذج إضافة مستخدم جديد
  const addForm = useForm({
    defaultValues: {
      name: "",
      email: "",
      password: "",
      role: "",
      department: "",
    },
  });

  // نموذج تعديل مستخدم
  const editForm = useForm({
    defaultValues: {
      name: "",
      email: "",
      role: "",
      department: "",
      status: "",
    },
  });

  // وظيفة إضافة مستخدم جديد
  const onAddSubmit = async (data: any) => {
    try {
      console.log('بدء إضافة مستخدم جديد:', data);

      const userData = {
        name: data.name,
        email: data.email,
        role: data.role,
        department: data.department,
        status: "نشط",
        lastActive: new Date().toISOString().split('T')[0],
      };

      console.log('بيانات المستخدم المراد إضافتها:', userData);

      // إضافة المستخدم إلى قاعدة البيانات
      const result = await simpleUserService.addUser(userData);
      console.log('نتيجة الإضافة:', result);

      // إعادة تحميل المستخدمين
      await loadUsers();

      // إغلاق نافذة الإضافة
      setIsAddDialogOpen(false);

      // إعادة تعيين النموذج
      addForm.reset();

      // عرض رسالة نجاح
      toast({
        title: "تمت الإضافة بنجاح",
        description: `تم إضافة المستخدم ${data.name} بنجاح.`,
      });
    } catch (error) {
      console.error('خطأ في إضافة المستخدم:', error);
      toast({
        title: "خطأ في الإضافة",
        description: "حدث خطأ أثناء إضافة المستخدم. يرجى المحاولة مرة أخرى.",
        variant: "destructive",
      });
    }
  };

  // وظيفة تعديل مستخدم
  const onEditSubmit = async (data: any) => {
    if (!userToEdit) return;

    try {
      const updatedUser: UserType = {
        ...userToEdit,
        name: data.name,
        email: data.email,
        role: data.role,
        department: data.department,
        status: data.status,
      };

      // تحديث المستخدم في قاعدة البيانات
      await simpleUserService.updateUser(updatedUser);

      // إعادة تحميل المستخدمين
      await loadUsers();

      // إغلاق نافذة التعديل
      setIsEditDialogOpen(false);

      // عرض رسالة نجاح
      toast({
        title: "تم التعديل بنجاح",
        description: `تم تعديل بيانات المستخدم ${data.name} بنجاح.`,
      });
    } catch (error) {
      console.error('خطأ في تعديل المستخدم:', error);
      toast({
        title: "خطأ في التعديل",
        description: "حدث خطأ أثناء تعديل المستخدم. يرجى المحاولة مرة أخرى.",
        variant: "destructive",
      });
    }
  };

  // وظيفة حذف مستخدم
  const handleDeleteUser = async (userId: number) => {
    console.log('محاولة حذف المستخدم:', userId);

    if (confirm("هل أنت متأكد من حذف هذا المستخدم؟")) {
      try {
        console.log('بدء عملية الحذف للمستخدم:', userId);

        // حذف المستخدم من قاعدة البيانات
        await simpleUserService.deleteUser(userId);
        console.log('تم حذف المستخدم من قاعدة البيانات');

        // إعادة تحميل المستخدمين
        await loadUsers();

        toast({
          title: "تم الحذف بنجاح",
          description: "تم حذف المستخدم بنجاح.",
        });
      } catch (error) {
        console.error('خطأ في حذف المستخدم:', error);
        toast({
          title: "خطأ في الحذف",
          description: "حدث خطأ أثناء حذف المستخدم. يرجى المحاولة مرة أخرى.",
          variant: "destructive",
        });
      }
    }
  };

  // وظيفة فتح نافذة تعديل مستخدم
  const handleEditUser = (user: any) => {
    setUserToEdit(user);
    editForm.reset({
      name: user.name,
      email: user.email,
      role: user.role,
      department: user.department,
      status: user.status,
    });
    setIsEditDialogOpen(true);
  };

  // تصفية المستخدمين حسب الفلاتر
  const filteredUsers = users
    .filter((user) => {
      if (activeTab === "all") return true;
      if (activeTab === "active") return user.status === "نشط";
      if (activeTab === "inactive") return user.status === "غير نشط";
      return true;
    })
    .filter((user) => {
      if (!roleFilter) return true;
      return user.role === roleFilter;
    })
    .filter((user) => {
      if (!searchQuery) return true;
      return (
        user.name.includes(searchQuery) ||
        user.email.includes(searchQuery) ||
        user.department.includes(searchQuery)
      );
    });

  const activeUsers = users.filter((user) => user.status === "نشط").length;
  const inactiveUsers = users.filter((user) => user.status === "غير نشط").length;

  if (isLoading) {
    return (
      <MainLayout>
        <div className="flex items-center justify-center h-64">
          <div className="text-center">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto mb-4"></div>
            <p>جاري تحميل البيانات...</p>
          </div>
        </div>
      </MainLayout>
    );
  }

  return (
    <MainLayout>
      <div className="space-y-6">
        {/* إدارة النسخ الاحتياطية */}
        <BackupManager />

        <div className="flex justify-between items-center">
          <h1 className="text-2xl font-bold">إدارة المستخدمين</h1>
          <Dialog open={isAddDialogOpen} onOpenChange={setIsAddDialogOpen}>
            <DialogTrigger asChild>
              <Button className="gap-2">
                <Plus size={18} />
                إضافة مستخدم
              </Button>
            </DialogTrigger>
            <DialogContent className="sm:max-w-[425px]">
              <DialogHeader>
                <DialogTitle>إضافة مستخدم جديد</DialogTitle>
                <DialogDescription>
                  قم بإدخال بيانات المستخدم الجديد. اضغط على "إضافة" عند الانتهاء.
                </DialogDescription>
              </DialogHeader>
              <Form {...addForm}>
                <form onSubmit={addForm.handleSubmit(onAddSubmit)} className="space-y-4">
                  <FormField
                    control={addForm.control}
                    name="name"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>الاسم</FormLabel>
                        <FormControl>
                          <Input placeholder="أدخل اسم المستخدم" {...field} />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                  <FormField
                    control={addForm.control}
                    name="email"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>البريد الإلكتروني</FormLabel>
                        <FormControl>
                          <div className="relative">
                            <Mail className="absolute right-3 top-1/2 h-4 w-4 -translate-y-1/2 text-muted-foreground" />
                            <Input className="pr-9" placeholder="<EMAIL>" {...field} />
                          </div>
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                  <FormField
                    control={addForm.control}
                    name="password"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>كلمة المرور</FormLabel>
                        <FormControl>
                          <div className="relative">
                            <Key className="absolute right-3 top-1/2 h-4 w-4 -translate-y-1/2 text-muted-foreground" />
                            <Input
                              className="pr-9"
                              type={showPassword ? "text" : "password"}
                              placeholder="●●●●●●●●"
                              {...field}
                            />
                            <Button
                              type="button"
                              variant="ghost"
                              size="icon"
                              className="absolute left-0 top-0"
                              onClick={() => setShowPassword(!showPassword)}
                            >
                              {showPassword ? <EyeOff className="h-4 w-4" /> : <Eye className="h-4 w-4" />}
                            </Button>
                          </div>
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                  <FormField
                    control={addForm.control}
                    name="role"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>الدور</FormLabel>
                        <Select
                          onValueChange={field.onChange}
                          defaultValue={field.value}
                        >
                          <FormControl>
                            <SelectTrigger>
                              <SelectValue placeholder="اختر الدور" />
                            </SelectTrigger>
                          </FormControl>
                          <SelectContent>
                            <SelectItem value="مدير">مدير</SelectItem>
                            <SelectItem value="مسؤول">مسؤول</SelectItem>
                            <SelectItem value="موظف">موظف</SelectItem>
                          </SelectContent>
                        </Select>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                  <FormField
                    control={addForm.control}
                    name="department"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>القسم</FormLabel>
                        <Select
                          onValueChange={field.onChange}
                          defaultValue={field.value}
                        >
                          <FormControl>
                            <SelectTrigger>
                              <SelectValue placeholder="اختر القسم" />
                            </SelectTrigger>
                          </FormControl>
                          <SelectContent>
                            <SelectItem value="المبيعات">المبيعات</SelectItem>
                            <SelectItem value="المالية">المالية</SelectItem>
                            <SelectItem value="المشتريات">المشتريات</SelectItem>
                            <SelectItem value="الصيانة">الصيانة</SelectItem>
                          </SelectContent>
                        </Select>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                  <DialogFooter>
                    <Button type="submit">إضافة</Button>
                  </DialogFooter>
                </form>
              </Form>
            </DialogContent>
          </Dialog>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          <Card>
            <CardHeader className="pb-2">
              <CardTitle className="text-sm font-medium text-muted-foreground">
                إجمالي المستخدمين
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{users.length}</div>
            </CardContent>
          </Card>
          <Card>
            <CardHeader className="pb-2">
              <CardTitle className="text-sm font-medium text-muted-foreground">
                مستخدمين نشطين
              </CardTitle>
            </CardHeader>
            <CardContent className="flex justify-between items-center">
              <div className="text-2xl font-bold">{activeUsers}</div>
              <Badge variant="default">{Math.round((activeUsers / users.length) * 100)}%</Badge>
            </CardContent>
          </Card>
          <Card>
            <CardHeader className="pb-2">
              <CardTitle className="text-sm font-medium text-muted-foreground">
                مستخدمين غير نشطين
              </CardTitle>
            </CardHeader>
            <CardContent className="flex justify-between items-center">
              <div className="text-2xl font-bold">{inactiveUsers}</div>
              <Badge variant="secondary">{Math.round((inactiveUsers / users.length) * 100)}%</Badge>
            </CardContent>
          </Card>
        </div>

        <Card>
          <CardHeader>
            <div className="flex flex-col gap-4 md:flex-row md:items-center md:justify-between">
              <CardTitle>قائمة المستخدمين</CardTitle>
              <div className="flex flex-col gap-4 md:flex-row md:items-center">
                <div className="relative">
                  <Search className="absolute right-3 top-1/2 h-4 w-4 -translate-y-1/2 text-muted-foreground" />
                  <Input
                    placeholder="بحث في المستخدمين..."
                    className="pr-9 max-w-[250px]"
                    value={searchQuery}
                    onChange={(e) => setSearchQuery(e.target.value)}
                  />
                </div>
                <Select value={roleFilter} onValueChange={setRoleFilter}>
                  <SelectTrigger className="w-[180px]">
                    <SelectValue placeholder="الدور" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="">جميع الأدوار</SelectItem>
                    <SelectItem value="مدير">مدير</SelectItem>
                    <SelectItem value="مسؤول">مسؤول</SelectItem>
                    <SelectItem value="موظف">موظف</SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </div>
          </CardHeader>
          <CardContent>
            <Tabs defaultValue="all" onValueChange={setActiveTab} value={activeTab}>
              <TabsList className="grid w-full grid-cols-3">
                <TabsTrigger value="all">الكل</TabsTrigger>
                <TabsTrigger value="active">نشط</TabsTrigger>
                <TabsTrigger value="inactive">غير نشط</TabsTrigger>
              </TabsList>
              <TabsContent value="all" className="pt-4">
                <UsersTable
                  users={filteredUsers}
                  onDelete={handleDeleteUser}
                  onEdit={handleEditUser}
                />
              </TabsContent>
              <TabsContent value="active" className="pt-4">
                <UsersTable
                  users={filteredUsers}
                  onDelete={handleDeleteUser}
                  onEdit={handleEditUser}
                />
              </TabsContent>
              <TabsContent value="inactive" className="pt-4">
                <UsersTable
                  users={filteredUsers}
                  onDelete={handleDeleteUser}
                  onEdit={handleEditUser}
                />
              </TabsContent>
            </Tabs>
          </CardContent>
        </Card>
      </div>

      {/* نافذة تعديل المستخدم */}
      <Dialog open={isEditDialogOpen} onOpenChange={setIsEditDialogOpen}>
        <DialogContent className="sm:max-w-[425px]">
          <DialogHeader>
            <DialogTitle>تعديل بيانات المستخدم</DialogTitle>
            <DialogDescription>
              قم بتعديل بيانات المستخدم. اضغط على "حفظ" عند الانتهاء.
            </DialogDescription>
          </DialogHeader>
          <Form {...editForm}>
            <form onSubmit={editForm.handleSubmit(onEditSubmit)} className="space-y-4">
              <FormField
                control={editForm.control}
                name="name"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>الاسم</FormLabel>
                    <FormControl>
                      <Input placeholder="أدخل اسم المستخدم" {...field} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
              <FormField
                control={editForm.control}
                name="email"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>البريد الإلكتروني</FormLabel>
                    <FormControl>
                      <div className="relative">
                        <Mail className="absolute right-3 top-1/2 h-4 w-4 -translate-y-1/2 text-muted-foreground" />
                        <Input className="pr-9" placeholder="<EMAIL>" {...field} />
                      </div>
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
              <FormField
                control={editForm.control}
                name="role"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>الدور</FormLabel>
                    <Select
                      onValueChange={field.onChange}
                      defaultValue={field.value}
                    >
                      <FormControl>
                        <SelectTrigger>
                          <SelectValue placeholder="اختر الدور" />
                        </SelectTrigger>
                      </FormControl>
                      <SelectContent>
                        <SelectItem value="مدير">مدير</SelectItem>
                        <SelectItem value="مسؤول">مسؤول</SelectItem>
                        <SelectItem value="موظف">موظف</SelectItem>
                      </SelectContent>
                    </Select>
                    <FormMessage />
                  </FormItem>
                )}
              />
              <FormField
                control={editForm.control}
                name="department"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>القسم</FormLabel>
                    <Select
                      onValueChange={field.onChange}
                      defaultValue={field.value}
                    >
                      <FormControl>
                        <SelectTrigger>
                          <SelectValue placeholder="اختر القسم" />
                        </SelectTrigger>
                      </FormControl>
                      <SelectContent>
                        <SelectItem value="المبيعات">المبيعات</SelectItem>
                        <SelectItem value="المالية">المالية</SelectItem>
                        <SelectItem value="المشتريات">المشتريات</SelectItem>
                        <SelectItem value="الصيانة">الصيانة</SelectItem>
                      </SelectContent>
                    </Select>
                    <FormMessage />
                  </FormItem>
                )}
              />
              <FormField
                control={editForm.control}
                name="status"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>الحالة</FormLabel>
                    <Select
                      onValueChange={field.onChange}
                      defaultValue={field.value}
                    >
                      <FormControl>
                        <SelectTrigger>
                          <SelectValue placeholder="اختر الحالة" />
                        </SelectTrigger>
                      </FormControl>
                      <SelectContent>
                        <SelectItem value="نشط">نشط</SelectItem>
                        <SelectItem value="غير نشط">غير نشط</SelectItem>
                      </SelectContent>
                    </Select>
                    <FormMessage />
                  </FormItem>
                )}
              />
              <DialogFooter>
                <Button type="submit">حفظ التغييرات</Button>
              </DialogFooter>
            </form>
          </Form>
        </DialogContent>
      </Dialog>
    </MainLayout>
  );
};

export default UsersNew;
