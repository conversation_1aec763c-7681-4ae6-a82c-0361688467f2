
import { Toaster } from "@/components/ui/toaster";
import { Toaster as Sonner } from "@/components/ui/sonner";
import { TooltipProvider } from "@/components/ui/tooltip";
import { QueryClient, QueryClientProvider } from "@tanstack/react-query";
import { BrowserRouter, Routes, Route } from "react-router-dom";
import { ThemeProvider } from "next-themes";
import Index from "./pages/Index";
import NotFound from "./pages/NotFound";
import UsersSimple from "./pages/UsersSimple";
import Properties from "./pages/Properties";
import Sales from "./pages/Sales";
import Procurement from "./pages/Procurement";
import Suppliers from "./pages/Suppliers";
import Invoices from "./pages/Invoices";
import Maintenance from "./pages/Maintenance";
import Reports from "./pages/Reports";
import Permissions from "./pages/Permissions";

const queryClient = new QueryClient();

const App = () => (
  <QueryClientProvider client={queryClient}>
    <ThemeProvider attribute="class" defaultTheme="light">
      <TooltipProvider>
        <Toaster />
        <Sonner />
        <BrowserRouter>
          <Routes>
            <Route path="/" element={<Index />} />
            <Route path="/users" element={<UsersSimple />} />
            <Route path="/properties" element={<Properties />} />
            <Route path="/sales" element={<Sales />} />
            <Route path="/procurement" element={<Procurement />} />
            <Route path="/suppliers" element={<Suppliers />} />
            <Route path="/invoices" element={<Invoices />} />
            <Route path="/maintenance" element={<Maintenance />} />
            <Route path="/reports" element={<Reports />} />
            <Route path="/permissions" element={<Permissions />} />
            {/* Catch-all route */}
            <Route path="*" element={<NotFound />} />
          </Routes>
        </BrowserRouter>
      </TooltipProvider>
    </ThemeProvider>
  </QueryClientProvider>
);

export default App;
