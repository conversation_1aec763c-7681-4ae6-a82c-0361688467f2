
import React, { useState } from "react";
import MainLayout from "@/components/layout/MainLayout";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Badge } from "@/components/ui/badge";
import {
  Table,
  TableBody,
  TableCaption,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog";
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import { useForm } from "react-hook-form";
import { Search, Plus, UserCog, Users, Shield, Mail, Eye, EyeOff, User, Key } from "lucide-react";
import { useToast } from "@/components/ui/use-toast";

// بيانات المستخدمين النموذجية
const usersData = [
  {
    id: 1,
    name: "أحمد محمد",
    email: "<EMAIL>",
    role: "مدير",
    department: "المبيعات",
    status: "نشط",
    lastActive: "2024-05-20",
  },
  {
    id: 2,
    name: "سارة عبدالله",
    email: "<EMAIL>",
    role: "مسؤول",
    department: "المالية",
    status: "نشط",
    lastActive: "2024-05-22",
  },
  {
    id: 3,
    name: "خالد العمري",
    email: "<EMAIL>",
    role: "موظف",
    department: "المبيعات",
    status: "غير نشط",
    lastActive: "2024-04-15",
  },
  {
    id: 4,
    name: "نورة الشمري",
    email: "<EMAIL>",
    role: "مدير",
    department: "الصيانة",
    status: "نشط",
    lastActive: "2024-05-21",
  },
  {
    id: 5,
    name: "فيصل السعيد",
    email: "<EMAIL>",
    role: "موظف",
    department: "المشتريات",
    status: "نشط",
    lastActive: "2024-05-19",
  },
  {
    id: 6,
    name: "لمى الحربي",
    email: "<EMAIL>",
    role: "موظف",
    department: "المالية",
    status: "غير نشط",
    lastActive: "2024-03-10",
  },
];

const Users: React.FC = () => {
  const { toast } = useToast();
  const [activeTab, setActiveTab] = useState("all");
  const [searchQuery, setSearchQuery] = useState("");
  const [roleFilter, setRoleFilter] = useState("");
  const [showPassword, setShowPassword] = useState(false);

  // نموذج إضافة مستخدم جديد
  const form = useForm({
    defaultValues: {
      name: "",
      email: "",
      password: "",
      role: "",
      department: "",
    },
  });

  const onSubmit = (data: any) => {
    toast({
      title: "تمت الإضافة بنجاح",
      description: `تم إضافة المستخدم ${data.name} بنجاح.`,
    });
    console.log(data);
  };

  // تصفية المستخدمين حسب الفلاتر
  const filteredUsers = usersData
    .filter((user) => {
      if (activeTab === "all") return true;
      if (activeTab === "active") return user.status === "نشط";
      if (activeTab === "inactive") return user.status === "غير نشط";
      return true;
    })
    .filter((user) => {
      if (!roleFilter) return true;
      return user.role === roleFilter;
    })
    .filter((user) => {
      if (!searchQuery) return true;
      return (
        user.name.includes(searchQuery) ||
        user.email.includes(searchQuery) ||
        user.department.includes(searchQuery)
      );
    });

  const activeUsers = usersData.filter((user) => user.status === "نشط").length;
  const inactiveUsers = usersData.filter(
    (user) => user.status === "غير نشط"
  ).length;

  return (
    <MainLayout>
      <div className="space-y-6">
        <div className="flex justify-between items-center">
          <h1 className="text-2xl font-bold">إدارة المستخدمين</h1>
          <Dialog>
            <DialogTrigger asChild>
              <Button className="gap-2">
                <Plus size={18} />
                إضافة مستخدم
              </Button>
            </DialogTrigger>
            <DialogContent className="sm:max-w-[425px]">
              <DialogHeader>
                <DialogTitle>إضافة مستخدم جديد</DialogTitle>
                <DialogDescription>
                  قم بإدخال بيانات المستخدم الجديد. اضغط على "إضافة" عند الانتهاء.
                </DialogDescription>
              </DialogHeader>
              <Form {...form}>
                <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-4">
                  <FormField
                    control={form.control}
                    name="name"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>الاسم</FormLabel>
                        <FormControl>
                          <Input placeholder="أدخل اسم المستخدم" {...field} />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                  <FormField
                    control={form.control}
                    name="email"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>البريد الإلكتروني</FormLabel>
                        <FormControl>
                          <div className="relative">
                            <Mail className="absolute right-3 top-1/2 h-4 w-4 -translate-y-1/2 text-muted-foreground" />
                            <Input className="pr-9" placeholder="<EMAIL>" {...field} />
                          </div>
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                  <FormField
                    control={form.control}
                    name="password"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>كلمة المرور</FormLabel>
                        <FormControl>
                          <div className="relative">
                            <Key className="absolute right-3 top-1/2 h-4 w-4 -translate-y-1/2 text-muted-foreground" />
                            <Input 
                              className="pr-9" 
                              type={showPassword ? "text" : "password"} 
                              placeholder="●●●●●●●●" 
                              {...field} 
                            />
                            <Button
                              type="button"
                              variant="ghost"
                              size="icon"
                              className="absolute left-0 top-0"
                              onClick={() => setShowPassword(!showPassword)}
                            >
                              {showPassword ? <EyeOff className="h-4 w-4" /> : <Eye className="h-4 w-4" />}
                            </Button>
                          </div>
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                  <FormField
                    control={form.control}
                    name="role"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>الدور</FormLabel>
                        <Select
                          onValueChange={field.onChange}
                          defaultValue={field.value}
                        >
                          <FormControl>
                            <SelectTrigger>
                              <SelectValue placeholder="اختر الدور" />
                            </SelectTrigger>
                          </FormControl>
                          <SelectContent>
                            <SelectItem value="مدير">مدير</SelectItem>
                            <SelectItem value="مسؤول">مسؤول</SelectItem>
                            <SelectItem value="موظف">موظف</SelectItem>
                          </SelectContent>
                        </Select>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                  <FormField
                    control={form.control}
                    name="department"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>القسم</FormLabel>
                        <Select
                          onValueChange={field.onChange}
                          defaultValue={field.value}
                        >
                          <FormControl>
                            <SelectTrigger>
                              <SelectValue placeholder="اختر القسم" />
                            </SelectTrigger>
                          </FormControl>
                          <SelectContent>
                            <SelectItem value="المبيعات">المبيعات</SelectItem>
                            <SelectItem value="المالية">المالية</SelectItem>
                            <SelectItem value="المشتريات">المشتريات</SelectItem>
                            <SelectItem value="الصيانة">الصيانة</SelectItem>
                          </SelectContent>
                        </Select>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                  <DialogFooter>
                    <Button type="submit">إضافة</Button>
                  </DialogFooter>
                </form>
              </Form>
            </DialogContent>
          </Dialog>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          <Card>
            <CardHeader className="pb-2">
              <CardTitle className="text-sm font-medium text-muted-foreground">
                إجمالي المستخدمين
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{usersData.length}</div>
            </CardContent>
          </Card>
          <Card>
            <CardHeader className="pb-2">
              <CardTitle className="text-sm font-medium text-muted-foreground">
                مستخدمين نشطين
              </CardTitle>
            </CardHeader>
            <CardContent className="flex justify-between items-center">
              <div className="text-2xl font-bold">{activeUsers}</div>
              <Badge variant="default">{Math.round((activeUsers / usersData.length) * 100)}%</Badge>
            </CardContent>
          </Card>
          <Card>
            <CardHeader className="pb-2">
              <CardTitle className="text-sm font-medium text-muted-foreground">
                مستخدمين غير نشطين
              </CardTitle>
            </CardHeader>
            <CardContent className="flex justify-between items-center">
              <div className="text-2xl font-bold">{inactiveUsers}</div>
              <Badge variant="secondary">{Math.round((inactiveUsers / usersData.length) * 100)}%</Badge>
            </CardContent>
          </Card>
        </div>

        <Card>
          <CardHeader>
            <div className="flex flex-col gap-4 md:flex-row md:items-center md:justify-between">
              <CardTitle>قائمة المستخدمين</CardTitle>
              <div className="flex flex-col gap-4 md:flex-row md:items-center">
                <div className="relative">
                  <Search className="absolute right-3 top-1/2 h-4 w-4 -translate-y-1/2 text-muted-foreground" />
                  <Input
                    placeholder="بحث في المستخدمين..."
                    className="pr-9 max-w-[250px]"
                    value={searchQuery}
                    onChange={(e) => setSearchQuery(e.target.value)}
                  />
                </div>
                <Select value={roleFilter} onValueChange={setRoleFilter}>
                  <SelectTrigger className="w-[180px]">
                    <SelectValue placeholder="الدور" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="">جميع الأدوار</SelectItem>
                    <SelectItem value="مدير">مدير</SelectItem>
                    <SelectItem value="مسؤول">مسؤول</SelectItem>
                    <SelectItem value="موظف">موظف</SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </div>
          </CardHeader>
          <CardContent>
            <Tabs defaultValue="all" onValueChange={setActiveTab} value={activeTab}>
              <TabsList className="grid w-full grid-cols-3">
                <TabsTrigger value="all">الكل</TabsTrigger>
                <TabsTrigger value="active">نشط</TabsTrigger>
                <TabsTrigger value="inactive">غير نشط</TabsTrigger>
              </TabsList>
              <TabsContent value="all" className="pt-4">
                <UsersTable users={filteredUsers} />
              </TabsContent>
              <TabsContent value="active" className="pt-4">
                <UsersTable users={filteredUsers} />
              </TabsContent>
              <TabsContent value="inactive" className="pt-4">
                <UsersTable users={filteredUsers} />
              </TabsContent>
            </Tabs>
          </CardContent>
        </Card>
      </div>
    </MainLayout>
  );
};

interface UsersTableProps {
  users: {
    id: number;
    name: string;
    email: string;
    role: string;
    department: string;
    status: string;
    lastActive: string;
  }[];
}

const UsersTable: React.FC<UsersTableProps> = ({ users }) => {
  return (
    <Table>
      <TableCaption>قائمة المستخدمين المسجلين في النظام</TableCaption>
      <TableHeader>
        <TableRow>
          <TableHead className="w-[100px]">المعرف</TableHead>
          <TableHead>الاسم</TableHead>
          <TableHead>البريد الإلكتروني</TableHead>
          <TableHead>الدور</TableHead>
          <TableHead>القسم</TableHead>
          <TableHead>آخر نشاط</TableHead>
          <TableHead>الحالة</TableHead>
        </TableRow>
      </TableHeader>
      <TableBody>
        {users.map((user) => (
          <TableRow key={user.id} className="cursor-pointer hover:bg-muted/50">
            <TableCell className="font-medium">#{user.id}</TableCell>
            <TableCell>
              <div className="flex items-center gap-2">
                <div className="w-8 h-8 rounded-full bg-primary/10 flex items-center justify-center">
                  <User size={16} className="text-primary" />
                </div>
                {user.name}
              </div>
            </TableCell>
            <TableCell>{user.email}</TableCell>
            <TableCell>
              <div className="flex items-center gap-1">
                <UserCog size={16} className="text-muted-foreground" />
                {user.role}
              </div>
            </TableCell>
            <TableCell>{user.department}</TableCell>
            <TableCell>{new Date(user.lastActive).toLocaleDateString("ar-SA")}</TableCell>
            <TableCell>
              <Badge
                variant={user.status === "نشط" ? "default" : "secondary"}
              >
                {user.status}
              </Badge>
            </TableCell>
          </TableRow>
        ))}
      </TableBody>
    </Table>
  );
};

export default Users;
