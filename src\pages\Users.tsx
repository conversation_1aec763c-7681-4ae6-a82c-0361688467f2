import React, { useState, useEffect } from "react";
import MainLayout from "@/components/layout/MainLayout";
import { But<PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Badge } from "@/components/ui/badge";
import { Label } from "@/components/ui/label";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import {
  Dialog,
  DialogContent,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog";
import { Plus, Edit, Trash2, User as UserIcon, Save, X } from "lucide-react";
import { useToast } from "@/components/ui/use-toast";
import { DataService, User } from "@/lib/storage";

const Users = () => {
  const { toast } = useToast();
  const [users, setUsers] = useState<User[]>([]);
  const [isAddDialogOpen, setIsAddDialogOpen] = useState(false);
  const [isEditDialogOpen, setIsEditDialogOpen] = useState(false);
  const [userToEdit, setUserToEdit] = useState<User | null>(null);
  const [isLoading, setIsLoading] = useState(true);

  // بيانات النموذج
  const [formData, setFormData] = useState({
    name: "",
    email: "",
    role: "",
    department: "",
    status: "نشط"
  });

  // تحميل البيانات عند بدء التطبيق
  useEffect(() => {
    loadUsers();
  }, []);

  const loadUsers = () => {
    try {
      setIsLoading(true);
      console.log('🔄 جاري تحميل المستخدمين...');
      const loadedUsers = DataService.getUsers();
      setUsers(loadedUsers);
      console.log('✅ تم تحميل المستخدمين بنجاح:', loadedUsers);
    } catch (error) {
      console.error('❌ خطأ في تحميل المستخدمين:', error);
      toast({
        title: "خطأ في التحميل",
        description: "حدث خطأ أثناء تحميل بيانات المستخدمين",
        variant: "destructive",
      });
    } finally {
      setIsLoading(false);
    }
  };

  // إضافة مستخدم جديد
  const handleAddUser = () => {
    console.log('🔄 محاولة إضافة مستخدم:', formData);

    if (!formData.name.trim() || !formData.email.trim()) {
      toast({
        title: "خطأ في البيانات",
        description: "يرجى ملء الاسم والبريد الإلكتروني على الأقل",
        variant: "destructive",
      });
      return;
    }

    try {
      const newUser = DataService.addUser({
        name: formData.name.trim(),
        email: formData.email.trim(),
        role: formData.role.trim() || "موظف",
        department: formData.department.trim() || "عام",
        status: formData.status
      });

      console.log('✅ تم إضافة المستخدم:', newUser);

      // إعادة تحميل البيانات
      loadUsers();

      // إعادة تعيين النموذج
      setFormData({ name: "", email: "", role: "", department: "", status: "نشط" });
      setIsAddDialogOpen(false);

      toast({
        title: "تم بنجاح",
        description: `تم إضافة المستخدم ${newUser.name} بنجاح`,
      });
    } catch (error) {
      console.error('❌ خطأ في إضافة المستخدم:', error);
      toast({
        title: "خطأ في الإضافة",
        description: "حدث خطأ أثناء إضافة المستخدم",
        variant: "destructive",
      });
    }
  };

  // تعديل مستخدم
  const handleEditUser = (user: User) => {
    console.log('🔄 فتح نافذة تعديل المستخدم:', user);
    setUserToEdit(user);
    setFormData({
      name: user.name,
      email: user.email,
      role: user.role,
      department: user.department,
      status: user.status
    });
    setIsEditDialogOpen(true);
  };

  // حفظ التعديل
  const handleSaveEdit = () => {
    if (!userToEdit) return;

    console.log('🔄 محاولة حفظ تعديل المستخدم:', formData);

    if (!formData.name.trim() || !formData.email.trim()) {
      toast({
        title: "خطأ في البيانات",
        description: "يرجى ملء الاسم والبريد الإلكتروني على الأقل",
        variant: "destructive",
      });
      return;
    }

    try {
      const updatedUser: User = {
        ...userToEdit,
        name: formData.name.trim(),
        email: formData.email.trim(),
        role: formData.role.trim() || "موظف",
        department: formData.department.trim() || "عام",
        status: formData.status
      };

      DataService.updateUser(updatedUser);
      console.log('✅ تم تعديل المستخدم:', updatedUser);

      // إعادة تحميل البيانات
      loadUsers();

      setIsEditDialogOpen(false);
      setUserToEdit(null);
      setFormData({ name: "", email: "", role: "", department: "", status: "نشط" });

      toast({
        title: "تم التعديل",
        description: `تم تعديل بيانات المستخدم ${updatedUser.name} بنجاح`,
      });
    } catch (error) {
      console.error('❌ خطأ في تعديل المستخدم:', error);
      toast({
        title: "خطأ في التعديل",
        description: "حدث خطأ أثناء تعديل المستخدم",
        variant: "destructive",
      });
    }
  };

  // حذف مستخدم
  const handleDeleteUser = (userId: number, userName: string) => {
    console.log('🔄 محاولة حذف المستخدم:', userId, userName);

    if (confirm(`هل أنت متأكد من حذف المستخدم "${userName}"؟`)) {
      try {
        DataService.deleteUser(userId);
        console.log('✅ تم حذف المستخدم:', userId);

        // إعادة تحميل البيانات
        loadUsers();

        toast({
          title: "تم الحذف",
          description: `تم حذف المستخدم ${userName} بنجاح`,
        });
      } catch (error) {
        console.error('❌ خطأ في حذف المستخدم:', error);
        toast({
          title: "خطأ في الحذف",
          description: "حدث خطأ أثناء حذف المستخدم",
          variant: "destructive",
        });
      }
    }
  };

  // إحصائيات
  const activeUsers = users.filter(u => u.status === "نشط").length;
  const inactiveUsers = users.filter(u => u.status === "غير نشط").length;

  if (isLoading) {
    return (
      <MainLayout>
        <div className="flex items-center justify-center h-64">
          <div className="text-center">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto mb-4"></div>
            <p>جاري تحميل البيانات...</p>
          </div>
        </div>
      </MainLayout>
    );
  }

  return (
    <MainLayout>
      <div className="space-y-6">
        {/* العنوان وزر الإضافة */}
        <div className="flex justify-between items-center">
          <h1 className="text-2xl font-bold">إدارة المستخدمين</h1>
          <Dialog open={isAddDialogOpen} onOpenChange={setIsAddDialogOpen}>
            <DialogTrigger asChild>
              <Button onClick={() => {
                console.log('🔄 فتح نافذة إضافة مستخدم');
                setFormData({ name: "", email: "", role: "", department: "", status: "نشط" });
              }}>
                <Plus className="mr-2 h-4 w-4" />
                إضافة مستخدم
              </Button>
            </DialogTrigger>
            <DialogContent>
              <DialogHeader>
                <DialogTitle>إضافة مستخدم جديد</DialogTitle>
              </DialogHeader>
              <div className="space-y-4">
                <div>
                  <Label htmlFor="name">الاسم *</Label>
                  <Input
                    id="name"
                    value={formData.name}
                    onChange={(e) => {
                      console.log('📝 تغيير الاسم:', e.target.value);
                      setFormData({...formData, name: e.target.value});
                    }}
                    placeholder="أدخل الاسم"
                  />
                </div>
                <div>
                  <Label htmlFor="email">البريد الإلكتروني *</Label>
                  <Input
                    id="email"
                    type="email"
                    value={formData.email}
                    onChange={(e) => {
                      console.log('📝 تغيير البريد الإلكتروني:', e.target.value);
                      setFormData({...formData, email: e.target.value});
                    }}
                    placeholder="أدخل البريد الإلكتروني"
                  />
                </div>
                <div>
                  <Label htmlFor="role">الدور</Label>
                  <Input
                    id="role"
                    value={formData.role}
                    onChange={(e) => {
                      console.log('📝 تغيير الدور:', e.target.value);
                      setFormData({...formData, role: e.target.value});
                    }}
                    placeholder="مثل: مدير، موظف، مسؤول"
                  />
                </div>
                <div>
                  <Label htmlFor="department">القسم</Label>
                  <Input
                    id="department"
                    value={formData.department}
                    onChange={(e) => {
                      console.log('📝 تغيير القسم:', e.target.value);
                      setFormData({...formData, department: e.target.value});
                    }}
                    placeholder="مثل: المبيعات، المالية، الصيانة"
                  />
                </div>
                <div>
                  <Label htmlFor="status">الحالة</Label>
                  <select
                    id="status"
                    value={formData.status}
                    onChange={(e) => {
                      console.log('📝 تغيير الحالة:', e.target.value);
                      setFormData({...formData, status: e.target.value});
                    }}
                    className="w-full p-2 border rounded-md"
                  >
                    <option value="نشط">نشط</option>
                    <option value="غير نشط">غير نشط</option>
                  </select>
                </div>
              </div>
              <DialogFooter>
                <Button variant="outline" onClick={() => setIsAddDialogOpen(false)}>
                  <X className="mr-2 h-4 w-4" />
                  إلغاء
                </Button>
                <Button onClick={handleAddUser}>
                  <Save className="mr-2 h-4 w-4" />
                  إضافة
                </Button>
              </DialogFooter>
            </DialogContent>
          </Dialog>
        </div>

        {/* الإحصائيات */}
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          <Card>
            <CardHeader>
              <CardTitle className="text-sm">إجمالي المستخدمين</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{users.length}</div>
            </CardContent>
          </Card>
          <Card>
            <CardHeader>
              <CardTitle className="text-sm">نشط</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold text-green-600">{activeUsers}</div>
            </CardContent>
          </Card>
          <Card>
            <CardHeader>
              <CardTitle className="text-sm">غير نشط</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold text-red-600">{inactiveUsers}</div>
            </CardContent>
          </Card>
        </div>

        {/* جدول المستخدمين */}
        <Card>
          <CardHeader>
            <CardTitle>قائمة المستخدمين ({users.length})</CardTitle>
          </CardHeader>
          <CardContent>
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>المعرف</TableHead>
                  <TableHead>الاسم</TableHead>
                  <TableHead>البريد الإلكتروني</TableHead>
                  <TableHead>الدور</TableHead>
                  <TableHead>القسم</TableHead>
                  <TableHead>الحالة</TableHead>
                  <TableHead>الإجراءات</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {users.length === 0 ? (
                  <TableRow>
                    <TableCell colSpan={7} className="text-center py-8">
                      لا توجد بيانات
                    </TableCell>
                  </TableRow>
                ) : (
                  users.map((user) => (
                    <TableRow key={user.id}>
                      <TableCell>#{user.id}</TableCell>
                      <TableCell>
                        <div className="flex items-center gap-2">
                          <UserIcon className="h-4 w-4" />
                          {user.name}
                        </div>
                      </TableCell>
                      <TableCell>{user.email}</TableCell>
                      <TableCell>{user.role}</TableCell>
                      <TableCell>{user.department}</TableCell>
                      <TableCell>
                        <Badge variant={user.status === "نشط" ? "default" : "secondary"}>
                          {user.status}
                        </Badge>
                      </TableCell>
                      <TableCell>
                        <div className="flex gap-2">
                          <Button
                            variant="ghost"
                            size="sm"
                            onClick={() => handleEditUser(user)}
                          >
                            <Edit className="h-4 w-4" />
                          </Button>
                          <Button
                            variant="ghost"
                            size="sm"
                            onClick={() => handleDeleteUser(user.id, user.name)}
                          >
                            <Trash2 className="h-4 w-4" />
                          </Button>
                        </div>
                      </TableCell>
                    </TableRow>
                  ))
                )}
              </TableBody>
            </Table>
          </CardContent>
        </Card>

        {/* نافذة التعديل */}
        <Dialog open={isEditDialogOpen} onOpenChange={setIsEditDialogOpen}>
          <DialogContent>
            <DialogHeader>
              <DialogTitle>تعديل المستخدم</DialogTitle>
            </DialogHeader>
            <div className="space-y-4">
              <div>
                <Label htmlFor="edit-name">الاسم *</Label>
                <Input
                  id="edit-name"
                  value={formData.name}
                  onChange={(e) => {
                    console.log('📝 تعديل الاسم:', e.target.value);
                    setFormData({...formData, name: e.target.value});
                  }}
                  placeholder="أدخل الاسم"
                />
              </div>
              <div>
                <Label htmlFor="edit-email">البريد الإلكتروني *</Label>
                <Input
                  id="edit-email"
                  type="email"
                  value={formData.email}
                  onChange={(e) => {
                    console.log('📝 تعديل البريد الإلكتروني:', e.target.value);
                    setFormData({...formData, email: e.target.value});
                  }}
                  placeholder="أدخل البريد الإلكتروني"
                />
              </div>
              <div>
                <Label htmlFor="edit-role">الدور</Label>
                <Input
                  id="edit-role"
                  value={formData.role}
                  onChange={(e) => {
                    console.log('📝 تعديل الدور:', e.target.value);
                    setFormData({...formData, role: e.target.value});
                  }}
                  placeholder="مثل: مدير، موظف، مسؤول"
                />
              </div>
              <div>
                <Label htmlFor="edit-department">القسم</Label>
                <Input
                  id="edit-department"
                  value={formData.department}
                  onChange={(e) => {
                    console.log('📝 تعديل القسم:', e.target.value);
                    setFormData({...formData, department: e.target.value});
                  }}
                  placeholder="مثل: المبيعات، المالية، الصيانة"
                />
              </div>
              <div>
                <Label htmlFor="edit-status">الحالة</Label>
                <select
                  id="edit-status"
                  value={formData.status}
                  onChange={(e) => {
                    console.log('📝 تعديل الحالة:', e.target.value);
                    setFormData({...formData, status: e.target.value});
                  }}
                  className="w-full p-2 border rounded-md"
                >
                  <option value="نشط">نشط</option>
                  <option value="غير نشط">غير نشط</option>
                </select>
              </div>
            </div>
            <DialogFooter>
              <Button variant="outline" onClick={() => {
                setIsEditDialogOpen(false);
                setUserToEdit(null);
              }}>
                <X className="mr-2 h-4 w-4" />
                إلغاء
              </Button>
              <Button onClick={handleSaveEdit}>
                <Save className="mr-2 h-4 w-4" />
                حفظ التغييرات
              </Button>
            </DialogFooter>
          </DialogContent>
        </Dialog>
      </div>
    </MainLayout>
  );
};

export default Users;
