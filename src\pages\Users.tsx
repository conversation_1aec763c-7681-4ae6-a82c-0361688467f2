
import React, { useState, useEffect } from "react";
import MainLayout from "@/components/layout/MainLayout";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Badge } from "@/components/ui/badge";
import {
  Table,
  TableBody,
  TableCaption,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog";
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import { useForm } from "react-hook-form";
import { Search, Plus, UserCog, Users as UsersIcon, Shield, Mail, Eye, EyeOff, User, Key } from "lucide-react";
import { useToast } from "@/components/ui/use-toast";

// بيانات المستخدمين النموذجية
const usersData = [
  {
    id: 1,
    name: "أحمد محمد",
    email: "<EMAIL>",
    role: "مدير",
    department: "المبيعات",
    status: "نشط",
    lastActive: "2024-05-20",
  },
  {
    id: 2,
    name: "سارة عبدالله",
    email: "<EMAIL>",
    role: "مسؤول",
    department: "المالية",
    status: "نشط",
    lastActive: "2024-05-22",
  },
  {
    id: 3,
    name: "خالد العمري",
    email: "<EMAIL>",
    role: "موظف",
    department: "المبيعات",
    status: "غير نشط",
    lastActive: "2024-04-15",
  },
  {
    id: 4,
    name: "نورة الشمري",
    email: "<EMAIL>",
    role: "مدير",
    department: "الصيانة",
    status: "نشط",
    lastActive: "2024-05-21",
  },
  {
    id: 5,
    name: "فيصل السعيد",
    email: "<EMAIL>",
    role: "موظف",
    department: "المشتريات",
    status: "نشط",
    lastActive: "2024-05-19",
  },
  {
    id: 6,
    name: "لمى الحربي",
    email: "<EMAIL>",
    role: "موظف",
    department: "المالية",
    status: "غير نشط",
    lastActive: "2024-03-10",
  },
];

const Users: React.FC = () => {
  const { toast } = useToast();
  const [activeTab, setActiveTab] = useState("all");
  const [searchQuery, setSearchQuery] = useState("");
  const [roleFilter, setRoleFilter] = useState("");
  const [showPassword, setShowPassword] = useState(false);
  const [users, setUsers] = useState(usersData);
  const [isAddDialogOpen, setIsAddDialogOpen] = useState(false);

  // نموذج إضافة مستخدم جديد
  const form = useForm({
    defaultValues: {
      name: "",
      email: "",
      password: "",
      role: "",
      department: "",
    },
  });

  // استخدام useEffect للاستماع لحدث حذف المستخدم
  useEffect(() => {
    const handleDeleteUser = (event: any) => {
      const { userId } = event.detail;
      // حذف المستخدم من القائمة
      setUsers(users.filter(u => u.id !== userId));
    };

    const handleUpdateUsers = (event: any) => {
      const { users: updatedUsers } = event.detail;
      // تحديث قائمة المستخدمين
      setUsers(updatedUsers);
    };

    // إضافة مستمعي الأحداث
    document.addEventListener('deleteUser', handleDeleteUser);
    document.addEventListener('updateUsers', handleUpdateUsers);

    // إزالة مستمعي الأحداث عند تفكيك المكون
    return () => {
      document.removeEventListener('deleteUser', handleDeleteUser);
      document.removeEventListener('updateUsers', handleUpdateUsers);
    };
  }, [users]);

  // إضافة عنصر لتخزين بيانات المستخدمين للمشاركة مع المكونات الفرعية
  useEffect(() => {
    const usersDataElement = document.getElementById('users-data');
    if (usersDataElement) {
      usersDataElement.setAttribute('data-users', JSON.stringify(users));
    } else {
      const dataElement = document.createElement('div');
      dataElement.id = 'users-data';
      dataElement.style.display = 'none';
      dataElement.setAttribute('data-users', JSON.stringify(users));
      document.body.appendChild(dataElement);
    }
  }, [users]);

  const onSubmit = (data: any) => {
    // إنشاء معرف فريد للمستخدم الجديد
    const newId = users.length > 0 ? Math.max(...users.map(u => u.id)) + 1 : 1;

    // إنشاء مستخدم جديد
    const newUser = {
      id: newId,
      name: data.name,
      email: data.email,
      role: data.role,
      department: data.department,
      status: "نشط",
      lastActive: new Date().toISOString().split('T')[0],
    };

    // إضافة المستخدم الجديد إلى القائمة
    setUsers([...users, newUser]);

    // إغلاق نافذة الإضافة
    setIsAddDialogOpen(false);

    // إعادة تعيين النموذج
    form.reset();

    // عرض رسالة نجاح
    toast({
      title: "تمت الإضافة بنجاح",
      description: `تم إضافة المستخدم ${data.name} بنجاح.`,
    });
  };

  // تصفية المستخدمين حسب الفلاتر
  const filteredUsers = users
    .filter((user) => {
      if (activeTab === "all") return true;
      if (activeTab === "active") return user.status === "نشط";
      if (activeTab === "inactive") return user.status === "غير نشط";
      return true;
    })
    .filter((user) => {
      if (!roleFilter) return true;
      return user.role === roleFilter;
    })
    .filter((user) => {
      if (!searchQuery) return true;
      return (
        user.name.includes(searchQuery) ||
        user.email.includes(searchQuery) ||
        user.department.includes(searchQuery)
      );
    });

  const activeUsers = users.filter((user) => user.status === "نشط").length;
  const inactiveUsers = users.filter(
    (user) => user.status === "غير نشط"
  ).length;

  return (
    <MainLayout>
      <div className="space-y-6">
        <div className="flex justify-between items-center">
          <h1 className="text-2xl font-bold">إدارة المستخدمين</h1>
          <Dialog open={isAddDialogOpen} onOpenChange={setIsAddDialogOpen}>
            <DialogTrigger asChild>
              <Button className="gap-2" onClick={() => setIsAddDialogOpen(true)}>
                <Plus size={18} />
                إضافة مستخدم
              </Button>
            </DialogTrigger>
            <DialogContent className="sm:max-w-[425px]">
              <DialogHeader>
                <DialogTitle>إضافة مستخدم جديد</DialogTitle>
                <DialogDescription>
                  قم بإدخال بيانات المستخدم الجديد. اضغط على "إضافة" عند الانتهاء.
                </DialogDescription>
              </DialogHeader>
              <Form {...form}>
                <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-4">
                  <FormField
                    control={form.control}
                    name="name"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>الاسم</FormLabel>
                        <FormControl>
                          <Input placeholder="أدخل اسم المستخدم" {...field} />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                  <FormField
                    control={form.control}
                    name="email"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>البريد الإلكتروني</FormLabel>
                        <FormControl>
                          <div className="relative">
                            <Mail className="absolute right-3 top-1/2 h-4 w-4 -translate-y-1/2 text-muted-foreground" />
                            <Input className="pr-9" placeholder="<EMAIL>" {...field} />
                          </div>
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                  <FormField
                    control={form.control}
                    name="password"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>كلمة المرور</FormLabel>
                        <FormControl>
                          <div className="relative">
                            <Key className="absolute right-3 top-1/2 h-4 w-4 -translate-y-1/2 text-muted-foreground" />
                            <Input
                              className="pr-9"
                              type={showPassword ? "text" : "password"}
                              placeholder="●●●●●●●●"
                              {...field}
                            />
                            <Button
                              type="button"
                              variant="ghost"
                              size="icon"
                              className="absolute left-0 top-0"
                              onClick={() => setShowPassword(!showPassword)}
                            >
                              {showPassword ? <EyeOff className="h-4 w-4" /> : <Eye className="h-4 w-4" />}
                            </Button>
                          </div>
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                  <FormField
                    control={form.control}
                    name="role"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>الدور</FormLabel>
                        <Select
                          onValueChange={field.onChange}
                          defaultValue={field.value}
                        >
                          <FormControl>
                            <SelectTrigger>
                              <SelectValue placeholder="اختر الدور" />
                            </SelectTrigger>
                          </FormControl>
                          <SelectContent>
                            <SelectItem value="مدير">مدير</SelectItem>
                            <SelectItem value="مسؤول">مسؤول</SelectItem>
                            <SelectItem value="موظف">موظف</SelectItem>
                          </SelectContent>
                        </Select>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                  <FormField
                    control={form.control}
                    name="department"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>القسم</FormLabel>
                        <Select
                          onValueChange={field.onChange}
                          defaultValue={field.value}
                        >
                          <FormControl>
                            <SelectTrigger>
                              <SelectValue placeholder="اختر القسم" />
                            </SelectTrigger>
                          </FormControl>
                          <SelectContent>
                            <SelectItem value="المبيعات">المبيعات</SelectItem>
                            <SelectItem value="المالية">المالية</SelectItem>
                            <SelectItem value="المشتريات">المشتريات</SelectItem>
                            <SelectItem value="الصيانة">الصيانة</SelectItem>
                          </SelectContent>
                        </Select>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                  <DialogFooter>
                    <Button type="submit">إضافة</Button>
                  </DialogFooter>
                </form>
              </Form>
            </DialogContent>
          </Dialog>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          <Card>
            <CardHeader className="pb-2">
              <CardTitle className="text-sm font-medium text-muted-foreground">
                إجمالي المستخدمين
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{users.length}</div>
            </CardContent>
          </Card>
          <Card>
            <CardHeader className="pb-2">
              <CardTitle className="text-sm font-medium text-muted-foreground">
                مستخدمين نشطين
              </CardTitle>
            </CardHeader>
            <CardContent className="flex justify-between items-center">
              <div className="text-2xl font-bold">{activeUsers}</div>
              <Badge variant="default">{Math.round((activeUsers / usersData.length) * 100)}%</Badge>
            </CardContent>
          </Card>
          <Card>
            <CardHeader className="pb-2">
              <CardTitle className="text-sm font-medium text-muted-foreground">
                مستخدمين غير نشطين
              </CardTitle>
            </CardHeader>
            <CardContent className="flex justify-between items-center">
              <div className="text-2xl font-bold">{inactiveUsers}</div>
              <Badge variant="secondary">{Math.round((inactiveUsers / usersData.length) * 100)}%</Badge>
            </CardContent>
          </Card>
        </div>

        <Card>
          <CardHeader>
            <div className="flex flex-col gap-4 md:flex-row md:items-center md:justify-between">
              <CardTitle>قائمة المستخدمين</CardTitle>
              <div className="flex flex-col gap-4 md:flex-row md:items-center">
                <div className="relative">
                  <Search className="absolute right-3 top-1/2 h-4 w-4 -translate-y-1/2 text-muted-foreground" />
                  <Input
                    placeholder="بحث في المستخدمين..."
                    className="pr-9 max-w-[250px]"
                    value={searchQuery}
                    onChange={(e) => setSearchQuery(e.target.value)}
                  />
                </div>
                <Select value={roleFilter} onValueChange={setRoleFilter}>
                  <SelectTrigger className="w-[180px]">
                    <SelectValue placeholder="الدور" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="">جميع الأدوار</SelectItem>
                    <SelectItem value="مدير">مدير</SelectItem>
                    <SelectItem value="مسؤول">مسؤول</SelectItem>
                    <SelectItem value="موظف">موظف</SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </div>
          </CardHeader>
          <CardContent>
            <Tabs defaultValue="all" onValueChange={setActiveTab} value={activeTab}>
              <TabsList className="grid w-full grid-cols-3">
                <TabsTrigger value="all">الكل</TabsTrigger>
                <TabsTrigger value="active">نشط</TabsTrigger>
                <TabsTrigger value="inactive">غير نشط</TabsTrigger>
              </TabsList>
              <TabsContent value="all" className="pt-4">
                <UsersTable users={filteredUsers} />
              </TabsContent>
              <TabsContent value="active" className="pt-4">
                <UsersTable users={filteredUsers} />
              </TabsContent>
              <TabsContent value="inactive" className="pt-4">
                <UsersTable users={filteredUsers} />
              </TabsContent>
            </Tabs>
          </CardContent>
        </Card>
      </div>
    </MainLayout>
  );
};

interface UsersTableProps {
  users: {
    id: number;
    name: string;
    email: string;
    role: string;
    department: string;
    status: string;
    lastActive: string;
  }[];
}

const UsersTable: React.FC<UsersTableProps> = ({ users }) => {
  const { toast } = useToast();
  const [userToEdit, setUserToEdit] = useState<any>(null);
  const [isEditDialogOpen, setIsEditDialogOpen] = useState(false);
  const [allUsers, setAllUsers] = useState<any[]>([]);

  // استخدام useEffect للحصول على قائمة المستخدمين من الكومبوننت الأب
  useEffect(() => {
    const usersFromParent = document.getElementById('users-data')?.getAttribute('data-users');
    if (usersFromParent) {
      try {
        setAllUsers(JSON.parse(usersFromParent));
      } catch (e) {
        console.error('Error parsing users data:', e);
      }
    }
  }, []);

  // نموذج تعديل المستخدم
  const editForm = useForm({
    defaultValues: {
      name: "",
      email: "",
      role: "",
      department: "",
      status: "",
    },
  });

  const handleEditUser = (user: any) => {
    setUserToEdit(user);
    editForm.reset({
      name: user.name,
      email: user.email,
      role: user.role,
      department: user.department,
      status: user.status,
    });
    setIsEditDialogOpen(true);
  };

  const handleDeleteUser = (userId: number) => {
    if (confirm(`هل أنت متأكد من حذف هذا المستخدم؟`)) {
      // إرسال حدث مخصص للكومبوننت الأب لحذف المستخدم
      const event = new CustomEvent('deleteUser', {
        detail: { userId }
      });
      document.dispatchEvent(event);

      toast({
        title: "تم الحذف بنجاح",
        description: `تم حذف المستخدم بنجاح.`,
      });
    }
  };

  const onEditSubmit = (data: any) => {
    // إرسال حدث مخصص للكومبوننت الأب لتعديل المستخدم
    const updatedUser = {
      ...userToEdit,
      name: data.name,
      email: data.email,
      role: data.role,
      department: data.department,
      status: data.status,
    };

    // تحديث المستخدم في القائمة
    const updatedUsers = allUsers.map(u =>
      u.id === userToEdit.id ? updatedUser : u
    );

    // تحديث عنصر البيانات
    const usersDataElement = document.getElementById('users-data');
    if (usersDataElement) {
      usersDataElement.setAttribute('data-users', JSON.stringify(updatedUsers));
    }

    // إرسال حدث لتحديث القائمة
    const event = new CustomEvent('updateUsers', {
      detail: { users: updatedUsers }
    });
    document.dispatchEvent(event);

    toast({
      title: "تم التعديل بنجاح",
      description: `تم تعديل بيانات المستخدم ${data.name} بنجاح.`,
    });
    setIsEditDialogOpen(false);
  };

  return (
    <>
      <Table>
        <TableCaption>قائمة المستخدمين المسجلين في النظام</TableCaption>
        <TableHeader>
          <TableRow>
            <TableHead className="w-[100px]">المعرف</TableHead>
            <TableHead>الاسم</TableHead>
            <TableHead>البريد الإلكتروني</TableHead>
            <TableHead>الدور</TableHead>
            <TableHead>القسم</TableHead>
            <TableHead>آخر نشاط</TableHead>
            <TableHead>الحالة</TableHead>
            <TableHead className="text-left">الإجراءات</TableHead>
          </TableRow>
        </TableHeader>
        <TableBody>
          {users.length === 0 ? (
            <TableRow>
              <TableCell colSpan={8} className="text-center py-6 text-muted-foreground">
                لا توجد بيانات للعرض
              </TableCell>
            </TableRow>
          ) : (
            users.map((user) => (
              <TableRow key={user.id} className="cursor-pointer hover:bg-muted/50">
                <TableCell className="font-medium">#{user.id}</TableCell>
                <TableCell>
                  <div className="flex items-center gap-2">
                    <div className="w-8 h-8 rounded-full bg-primary/10 flex items-center justify-center">
                      <User size={16} className="text-primary" />
                    </div>
                    {user.name}
                  </div>
                </TableCell>
                <TableCell>{user.email}</TableCell>
                <TableCell>
                  <div className="flex items-center gap-1">
                    <UserCog size={16} className="text-muted-foreground" />
                    {user.role}
                  </div>
                </TableCell>
                <TableCell>{user.department}</TableCell>
                <TableCell>{new Date(user.lastActive).toLocaleDateString("ar-SA")}</TableCell>
                <TableCell>
                  <Badge
                    variant={user.status === "نشط" ? "default" : "secondary"}
                  >
                    {user.status}
                  </Badge>
                </TableCell>
                <TableCell>
                  <div className="flex items-center gap-2 justify-end">
                    <Button variant="ghost" size="icon" onClick={() => handleEditUser(user)}>
                      <svg width="15" height="15" viewBox="0 0 15 15" fill="none" xmlns="http://www.w3.org/2000/svg">
                        <path d="M11.8536 1.14645C11.6583 0.951184 11.3417 0.951184 11.1465 1.14645L3.71455 8.57836C3.62459 8.66832 3.55263 8.77461 3.50251 8.89155L2.04044 12.303C1.9599 12.491 2.00189 12.709 2.14646 12.8536C2.29103 12.9981 2.50905 13.0401 2.69697 12.9596L6.10847 11.4975C6.2254 11.4474 6.3317 11.3754 6.42166 11.2855L13.8536 3.85355C14.0488 3.65829 14.0488 3.34171 13.8536 3.14645L11.8536 1.14645ZM4.42166 9.28547L11.5 2.20711L12.7929 3.5L5.71455 10.5784L4.21924 11.2192L3.78081 10.7808L4.42166 9.28547Z" fill="currentColor" fillRule="evenodd" clipRule="evenodd"></path>
                      </svg>
                    </Button>
                    <Button variant="ghost" size="icon" onClick={() => {
                      toast({
                        title: user.status === "نشط" ? "تم تعطيل المستخدم" : "تم تفعيل المستخدم",
                        description: `تم ${user.status === "نشط" ? "تعطيل" : "تفعيل"} حساب المستخدم ${user.name} بنجاح.`,
                      });
                    }}>
                      {user.status === "نشط" ? (
                        <svg width="15" height="15" viewBox="0 0 15 15" fill="none" xmlns="http://www.w3.org/2000/svg">
                          <path d="M8.14645 3.14645C8.34171 2.95118 8.65829 2.95118 8.85355 3.14645L12.8536 7.14645C13.0488 7.34171 13.0488 7.65829 12.8536 7.85355L8.85355 11.8536C8.65829 12.0488 8.34171 12.0488 8.14645 11.8536C7.95118 11.6583 7.95118 11.3417 8.14645 11.1464L11.2929 8H2.5C2.22386 8 2 7.77614 2 7.5C2 7.22386 2.22386 7 2.5 7H11.2929L8.14645 3.85355C7.95118 3.65829 7.95118 3.34171 8.14645 3.14645Z" fill="currentColor" fillRule="evenodd" clipRule="evenodd"></path>
                        </svg>
                      ) : (
                        <svg width="15" height="15" viewBox="0 0 15 15" fill="none" xmlns="http://www.w3.org/2000/svg">
                          <path d="M6.85355 3.14645C7.04882 3.34171 7.04882 3.65829 6.85355 3.85355L3.70711 7H12.5C12.7761 7 13 7.22386 13 7.5C13 7.77614 12.7761 8 12.5 8H3.70711L6.85355 11.1464C7.04882 11.3417 7.04882 11.6583 6.85355 11.8536C6.65829 12.0488 6.34171 12.0488 6.14645 11.8536L2.14645 7.85355C1.95118 7.65829 1.95118 7.34171 2.14645 7.14645L6.14645 3.14645C6.34171 2.95118 6.65829 2.95118 6.85355 3.14645Z" fill="currentColor" fillRule="evenodd" clipRule="evenodd"></path>
                        </svg>
                      )}
                    </Button>
                    <Button variant="ghost" size="icon" onClick={() => handleDeleteUser(user.id)}>
                      <svg width="15" height="15" viewBox="0 0 15 15" fill="none" xmlns="http://www.w3.org/2000/svg">
                        <path d="M5.5 1C5.22386 1 5 1.22386 5 1.5C5 1.77614 5.22386 2 5.5 2H9.5C9.77614 2 10 1.77614 10 1.5C10 1.22386 9.77614 1 9.5 1H5.5ZM3 3.5C3 3.22386 3.22386 3 3.5 3H11.5C11.7761 3 12 3.22386 12 3.5C12 3.77614 11.7761 4 11.5 4H3.5C3.22386 4 3 3.77614 3 3.5ZM3.5 5C3.22386 5 3 5.22386 3 5.5C3 5.77614 3.22386 6 3.5 6H4V12C4 12.5523 4.44772 13 5 13H10C10.5523 13 11 12.5523 11 12V6H11.5C11.7761 6 12 5.77614 12 5.5C12 5.22386 11.7761 5 11.5 5H3.5ZM5 6H10V12H5V6Z" fill="currentColor" fillRule="evenodd" clipRule="evenodd"></path>
                      </svg>
                    </Button>
                  </div>
                </TableCell>
              </TableRow>
            ))
          )}
        </TableBody>
      </Table>

      <Dialog open={isEditDialogOpen} onOpenChange={setIsEditDialogOpen}>
        <DialogContent className="sm:max-w-[425px]">
          <DialogHeader>
            <DialogTitle>تعديل بيانات المستخدم</DialogTitle>
            <DialogDescription>
              قم بتعديل بيانات المستخدم. اضغط على "حفظ" عند الانتهاء.
            </DialogDescription>
          </DialogHeader>
          <Form {...editForm}>
            <form onSubmit={editForm.handleSubmit(onEditSubmit)} className="space-y-4">
              <FormField
                control={editForm.control}
                name="name"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>الاسم</FormLabel>
                    <FormControl>
                      <Input placeholder="أدخل اسم المستخدم" {...field} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
              <FormField
                control={editForm.control}
                name="email"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>البريد الإلكتروني</FormLabel>
                    <FormControl>
                      <div className="relative">
                        <Mail className="absolute right-3 top-1/2 h-4 w-4 -translate-y-1/2 text-muted-foreground" />
                        <Input className="pr-9" placeholder="<EMAIL>" {...field} />
                      </div>
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
              <FormField
                control={editForm.control}
                name="role"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>الدور</FormLabel>
                    <Select
                      onValueChange={field.onChange}
                      defaultValue={field.value}
                    >
                      <FormControl>
                        <SelectTrigger>
                          <SelectValue placeholder="اختر الدور" />
                        </SelectTrigger>
                      </FormControl>
                      <SelectContent>
                        <SelectItem value="مدير">مدير</SelectItem>
                        <SelectItem value="مسؤول">مسؤول</SelectItem>
                        <SelectItem value="موظف">موظف</SelectItem>
                      </SelectContent>
                    </Select>
                    <FormMessage />
                  </FormItem>
                )}
              />
              <FormField
                control={editForm.control}
                name="department"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>القسم</FormLabel>
                    <Select
                      onValueChange={field.onChange}
                      defaultValue={field.value}
                    >
                      <FormControl>
                        <SelectTrigger>
                          <SelectValue placeholder="اختر القسم" />
                        </SelectTrigger>
                      </FormControl>
                      <SelectContent>
                        <SelectItem value="المبيعات">المبيعات</SelectItem>
                        <SelectItem value="المالية">المالية</SelectItem>
                        <SelectItem value="المشتريات">المشتريات</SelectItem>
                        <SelectItem value="الصيانة">الصيانة</SelectItem>
                      </SelectContent>
                    </Select>
                    <FormMessage />
                  </FormItem>
                )}
              />
              <FormField
                control={editForm.control}
                name="status"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>الحالة</FormLabel>
                    <Select
                      onValueChange={field.onChange}
                      defaultValue={field.value}
                    >
                      <FormControl>
                        <SelectTrigger>
                          <SelectValue placeholder="اختر الحالة" />
                        </SelectTrigger>
                      </FormControl>
                      <SelectContent>
                        <SelectItem value="نشط">نشط</SelectItem>
                        <SelectItem value="غير نشط">غير نشط</SelectItem>
                      </SelectContent>
                    </Select>
                    <FormMessage />
                  </FormItem>
                )}
              />
              <DialogFooter>
                <Button type="submit">حفظ التغييرات</Button>
              </DialogFooter>
            </form>
          </Form>
        </DialogContent>
      </Dialog>
    </>
  );
};

export default Users;
