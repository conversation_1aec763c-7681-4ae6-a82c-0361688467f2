
import React from "react";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { Badge } from "@/components/ui/badge";

interface MaintenanceRequest {
  id: string;
  property: string;
  issue: string;
  date: string;
  status: "open" | "inProgress" | "resolved";
  priority: "low" | "medium" | "high";
}

const maintenanceRequests: MaintenanceRequest[] = [
  {
    id: "MR001",
    property: "واحة النخيل - شقة 102",
    issue: "تسرب مياه في الحمام",
    date: "2025-05-20",
    status: "open",
    priority: "high",
  },
  {
    id: "MR002",
    property: "واحة النخيل - شقة 315",
    issue: "مشكلة في نظام التكييف",
    date: "2025-05-19",
    status: "inProgress",
    priority: "medium",
  },
  {
    id: "MR003",
    property: "أبراج الروضة - شقة 204",
    issue: "إصلاح باب المدخل",
    date: "2025-05-18",
    status: "inProgress",
    priority: "low",
  },
  {
    id: "MR004",
    property: "واحة النخيل - شقة 207",
    issue: "انقطاع في التيار الكهربائي",
    date: "2025-05-15",
    status: "resolved",
    priority: "high",
  },
];

const MaintenanceRequests: React.FC = () => {
  return (
    <div className="rounded-md border bg-white">
      <Table>
        <TableHeader>
          <TableRow>
            <TableHead>رقم البلاغ</TableHead>
            <TableHead className="hidden sm:table-cell">العقار</TableHead>
            <TableHead>المشكلة</TableHead>
            <TableHead className="hidden md:table-cell">التاريخ</TableHead>
            <TableHead>الأولوية</TableHead>
            <TableHead>الحالة</TableHead>
          </TableRow>
        </TableHeader>
        <TableBody>
          {maintenanceRequests.map((request) => (
            <TableRow key={request.id} className="hover:bg-muted/50">
              <TableCell className="font-medium">{request.id}</TableCell>
              <TableCell className="hidden sm:table-cell">
                {request.property}
              </TableCell>
              <TableCell>{request.issue}</TableCell>
              <TableCell className="hidden md:table-cell">
                {new Date(request.date).toLocaleDateString("ar-SA")}
              </TableCell>
              <TableCell>
                <Badge
                  variant="outline"
                  className={
                    request.priority === "high"
                      ? "bg-red-50 text-red-700 border-red-200"
                      : request.priority === "medium"
                      ? "bg-yellow-50 text-yellow-700 border-yellow-200"
                      : "bg-green-50 text-green-700 border-green-200"
                  }
                >
                  {request.priority === "high"
                    ? "عالية"
                    : request.priority === "medium"
                    ? "متوسطة"
                    : "منخفضة"}
                </Badge>
              </TableCell>
              <TableCell>
                <Badge
                  variant="outline"
                  className={
                    request.status === "open"
                      ? "bg-blue-50 text-blue-700 border-blue-200"
                      : request.status === "inProgress"
                      ? "bg-yellow-50 text-yellow-700 border-yellow-200"
                      : "bg-green-50 text-green-700 border-green-200"
                  }
                >
                  {request.status === "open"
                    ? "مفتوح"
                    : request.status === "inProgress"
                    ? "قيد المعالجة"
                    : "تم الحل"}
                </Badge>
              </TableCell>
            </TableRow>
          ))}
        </TableBody>
      </Table>
    </div>
  );
};

export default MaintenanceRequests;
