
import React from "react";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { Badge } from "@/components/ui/badge";

interface Project {
  id: string;
  name: string;
  location: string;
  progress: number;
  status: "active" | "completed" | "planned";
  units: number;
  soldUnits: number;
}

const projects: Project[] = [
  {
    id: "PRJ001",
    name: "واحة النخيل",
    location: "المدينة المنورة - طريق الملك عبدالله",
    progress: 75,
    status: "active",
    units: 48,
    soldUnits: 32,
  },
  {
    id: "PRJ002",
    name: "بوابة طيبة",
    location: "المدينة المنورة - العزيزية",
    progress: 100,
    status: "completed",
    units: 36,
    soldUnits: 36,
  },
  {
    id: "PRJ003",
    name: "أبراج الروضة",
    location: "المدينة المنورة - الروضة",
    progress: 50,
    status: "active",
    units: 64,
    soldUnits: 28,
  },
  {
    id: "PRJ004",
    name: "إشراق الحرم",
    location: "المدينة المنورة - قربان",
    progress: 25,
    status: "active",
    units: 24,
    soldUnits: 8,
  },
  {
    id: "PRJ005",
    name: "نور المدينة",
    location: "المدينة المنورة - العوالي",
    progress: 0,
    status: "planned",
    units: 42,
    soldUnits: 0,
  },
];

const ProjectsTable: React.FC = () => {
  return (
    <div className="rounded-md border bg-white">
      <Table>
        <TableHeader>
          <TableRow>
            <TableHead>رمز المشروع</TableHead>
            <TableHead>اسم المشروع</TableHead>
            <TableHead className="hidden md:table-cell">الموقع</TableHead>
            <TableHead className="hidden sm:table-cell">التقدم</TableHead>
            <TableHead>الحالة</TableHead>
            <TableHead className="text-left">الوحدات المباعة</TableHead>
          </TableRow>
        </TableHeader>
        <TableBody>
          {projects.map((project) => (
            <TableRow key={project.id} className="hover:bg-muted/50">
              <TableCell className="font-medium">{project.id}</TableCell>
              <TableCell>{project.name}</TableCell>
              <TableCell className="hidden md:table-cell">
                {project.location}
              </TableCell>
              <TableCell className="hidden sm:table-cell">
                <div className="w-full bg-muted rounded-full h-2.5 mb-1">
                  <div
                    className="bg-primary h-2.5 rounded-full"
                    style={{ width: `${project.progress}%` }}
                  ></div>
                </div>
                <span className="text-xs text-muted-foreground">
                  {project.progress}%
                </span>
              </TableCell>
              <TableCell>
                <Badge
                  variant="outline"
                  className={
                    project.status === "active"
                      ? "bg-blue-50 text-blue-700 border-blue-200"
                      : project.status === "completed"
                      ? "bg-green-50 text-green-700 border-green-200"
                      : "bg-yellow-50 text-yellow-700 border-yellow-200"
                  }
                >
                  {project.status === "active"
                    ? "نشط"
                    : project.status === "completed"
                    ? "مكتمل"
                    : "مخطط"}
                </Badge>
              </TableCell>
              <TableCell>
                <div className="text-left">
                  {project.soldUnits}/{project.units}
                </div>
              </TableCell>
            </TableRow>
          ))}
        </TableBody>
      </Table>
    </div>
  );
};

export default ProjectsTable;
