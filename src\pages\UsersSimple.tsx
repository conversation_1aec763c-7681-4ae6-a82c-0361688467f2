import React, { useState } from "react";
import MainLayout from "@/components/layout/MainLayout";
import { Button } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Badge } from "@/components/ui/badge";
import { Plus, Edit, Trash2 } from "lucide-react";
import { useToast } from "@/components/ui/use-toast";

interface User {
  id: number;
  name: string;
  email: string;
  role: string;
  status: string;
}

const UsersSimple = () => {
  const { toast } = useToast();
  const [users, setUsers] = useState<User[]>([
    { id: 1, name: "أحمد محمد", email: "<EMAIL>", role: "مدير", status: "نشط" },
    { id: 2, name: "سارة عبدالله", email: "<EMAIL>", role: "مسؤول", status: "نشط" },
    { id: 3, name: "خالد العمري", email: "<EMAIL>", role: "موظف", status: "غير نشط" },
  ]);

  const [showAddForm, setShowAddForm] = useState(false);
  const [newUser, setNewUser] = useState({ name: "", email: "", role: "", status: "نشط" });

  const handleAddUser = () => {
    if (!newUser.name || !newUser.email) {
      toast({
        title: "خطأ",
        description: "يرجى ملء جميع الحقول",
        variant: "destructive",
      });
      return;
    }

    const user: User = {
      id: Math.max(...users.map(u => u.id)) + 1,
      ...newUser
    };

    setUsers([...users, user]);
    setNewUser({ name: "", email: "", role: "", status: "نشط" });
    setShowAddForm(false);
    
    toast({
      title: "تم بنجاح",
      description: "تم إضافة المستخدم",
    });
  };

  const handleDeleteUser = (id: number) => {
    if (confirm("هل أنت متأكد من الحذف؟")) {
      setUsers(users.filter(u => u.id !== id));
      toast({
        title: "تم الحذف",
        description: "تم حذف المستخدم",
      });
    }
  };

  return (
    <MainLayout>
      <div className="space-y-6">
        <div className="flex justify-between items-center">
          <h1 className="text-2xl font-bold">إدارة المستخدمين</h1>
          <Button onClick={() => setShowAddForm(!showAddForm)}>
            <Plus className="mr-2 h-4 w-4" />
            إضافة مستخدم
          </Button>
        </div>

        {/* نموذج الإضافة */}
        {showAddForm && (
          <Card>
            <CardHeader>
              <CardTitle>إضافة مستخدم جديد</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <Input
                placeholder="الاسم"
                value={newUser.name}
                onChange={(e) => setNewUser({...newUser, name: e.target.value})}
              />
              <Input
                placeholder="البريد الإلكتروني"
                type="email"
                value={newUser.email}
                onChange={(e) => setNewUser({...newUser, email: e.target.value})}
              />
              <Input
                placeholder="الدور"
                value={newUser.role}
                onChange={(e) => setNewUser({...newUser, role: e.target.value})}
              />
              <div className="flex gap-2">
                <Button onClick={handleAddUser}>إضافة</Button>
                <Button variant="outline" onClick={() => setShowAddForm(false)}>إلغاء</Button>
              </div>
            </CardContent>
          </Card>
        )}

        {/* الإحصائيات */}
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          <Card>
            <CardHeader>
              <CardTitle className="text-sm">إجمالي المستخدمين</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{users.length}</div>
            </CardContent>
          </Card>
          <Card>
            <CardHeader>
              <CardTitle className="text-sm">نشط</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold text-green-600">
                {users.filter(u => u.status === "نشط").length}
              </div>
            </CardContent>
          </Card>
          <Card>
            <CardHeader>
              <CardTitle className="text-sm">غير نشط</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold text-red-600">
                {users.filter(u => u.status === "غير نشط").length}
              </div>
            </CardContent>
          </Card>
        </div>

        {/* قائمة المستخدمين */}
        <Card>
          <CardHeader>
            <CardTitle>قائمة المستخدمين</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              {users.map((user) => (
                <div key={user.id} className="flex items-center justify-between p-4 border rounded-lg">
                  <div className="flex-1">
                    <div className="flex items-center gap-4">
                      <div>
                        <h3 className="font-medium">{user.name}</h3>
                        <p className="text-sm text-gray-600">{user.email}</p>
                      </div>
                      <Badge variant={user.status === "نشط" ? "default" : "secondary"}>
                        {user.status}
                      </Badge>
                      <span className="text-sm text-gray-500">{user.role}</span>
                    </div>
                  </div>
                  <div className="flex gap-2">
                    <Button variant="ghost" size="sm">
                      <Edit className="h-4 w-4" />
                    </Button>
                    <Button 
                      variant="ghost" 
                      size="sm" 
                      onClick={() => handleDeleteUser(user.id)}
                    >
                      <Trash2 className="h-4 w-4" />
                    </Button>
                  </div>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      </div>
    </MainLayout>
  );
};

export default UsersSimple;
