var q=function(G){return q=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(D){return typeof D}:function(D){return D&&typeof Symbol=="function"&&D.constructor===Symbol&&D!==Symbol.prototype?"symbol":typeof D},q(G)},M=function(G,D){var X=Object.keys(G);if(Object.getOwnPropertySymbols){var Y=Object.getOwnPropertySymbols(G);D&&(Y=Y.filter(function(K){return Object.getOwnPropertyDescriptor(G,K).enumerable})),X.push.apply(X,Y)}return X},x=function(G){for(var D=1;D<arguments.length;D++){var X=arguments[D]!=null?arguments[D]:{};D%2?M(Object(X),!0).forEach(function(Y){C6(G,Y,X[Y])}):Object.getOwnPropertyDescriptors?Object.defineProperties(G,Object.getOwnPropertyDescriptors(X)):M(Object(X)).forEach(function(Y){Object.defineProperty(G,Y,Object.getOwnPropertyDescriptor(X,Y))})}return G},C6=function(G,D,X){if(D=H6(D),D in G)Object.defineProperty(G,D,{value:X,enumerable:!0,configurable:!0,writable:!0});else G[D]=X;return G},H6=function(G){var D=U6(G,"string");return q(D)=="symbol"?D:String(D)},U6=function(G,D){if(q(G)!="object"||!G)return G;var X=G[Symbol.toPrimitive];if(X!==void 0){var Y=X.call(G,D||"default");if(q(Y)!="object")return Y;throw new TypeError("@@toPrimitive must return a primitive value.")}return(D==="string"?String:Number)(G)};(function(G){var D=Object.defineProperty,X=function H(B,U){for(var C in U)D(B,C,{get:U[C],enumerable:!0,configurable:!0,set:function I(J){return U[C]=function(){return J}}})},Y={lessThanXSeconds:{one:"1\u79D2\u672A\u6E80",other:"{{count}}\u79D2\u672A\u6E80",oneWithSuffix:"\u7D041\u79D2",otherWithSuffix:"\u7D04{{count}}\u79D2"},xSeconds:{one:"1\u79D2",other:"{{count}}\u79D2"},halfAMinute:"30\u79D2",lessThanXMinutes:{one:"1\u5206\u672A\u6E80",other:"{{count}}\u5206\u672A\u6E80",oneWithSuffix:"\u7D041\u5206",otherWithSuffix:"\u7D04{{count}}\u5206"},xMinutes:{one:"1\u5206",other:"{{count}}\u5206"},aboutXHours:{one:"\u7D041\u6642\u9593",other:"\u7D04{{count}}\u6642\u9593"},xHours:{one:"1\u6642\u9593",other:"{{count}}\u6642\u9593"},xDays:{one:"1\u65E5",other:"{{count}}\u65E5"},aboutXWeeks:{one:"\u7D041\u9031\u9593",other:"\u7D04{{count}}\u9031\u9593"},xWeeks:{one:"1\u9031\u9593",other:"{{count}}\u9031\u9593"},aboutXMonths:{one:"\u7D041\u304B\u6708",other:"\u7D04{{count}}\u304B\u6708"},xMonths:{one:"1\u304B\u6708",other:"{{count}}\u304B\u6708"},aboutXYears:{one:"\u7D041\u5E74",other:"\u7D04{{count}}\u5E74"},xYears:{one:"1\u5E74",other:"{{count}}\u5E74"},overXYears:{one:"1\u5E74\u4EE5\u4E0A",other:"{{count}}\u5E74\u4EE5\u4E0A"},almostXYears:{one:"1\u5E74\u8FD1\u304F",other:"{{count}}\u5E74\u8FD1\u304F"}},K=function H(B,U,C){C=C||{};var I,J=Y[B];if(typeof J==="string")I=J;else if(U===1)if(C.addSuffix&&J.oneWithSuffix)I=J.oneWithSuffix;else I=J.one;else if(C.addSuffix&&J.otherWithSuffix)I=J.otherWithSuffix.replace("{{count}}",String(U));else I=J.other.replace("{{count}}",String(U));if(C.addSuffix)if(C.comparison&&C.comparison>0)return I+"\u5F8C";else return I+"\u524D";return I};function N(H){return function(){var B=arguments.length>0&&arguments[0]!==void 0?arguments[0]:{},U=B.width?String(B.width):H.defaultWidth,C=H.formats[U]||H.formats[H.defaultWidth];return C}}var W={full:"y\u5E74M\u6708d\u65E5EEEE",long:"y\u5E74M\u6708d\u65E5",medium:"y/MM/dd",short:"y/MM/dd"},$={full:"H\u6642mm\u5206ss\u79D2 zzzz",long:"H:mm:ss z",medium:"H:mm:ss",short:"H:mm"},S={full:"{{date}} {{time}}",long:"{{date}} {{time}}",medium:"{{date}} {{time}}",short:"{{date}} {{time}}"},R={date:N({formats:W,defaultWidth:"full"}),time:N({formats:$,defaultWidth:"full"}),dateTime:N({formats:S,defaultWidth:"full"})},L={lastWeek:"\u5148\u9031\u306Eeeee\u306Ep",yesterday:"\u6628\u65E5\u306Ep",today:"\u4ECA\u65E5\u306Ep",tomorrow:"\u660E\u65E5\u306Ep",nextWeek:"\u7FCC\u9031\u306Eeeee\u306Ep",other:"P"},f=function H(B,U,C,I){return L[B]};function E(H){return function(B,U){var C=U!==null&&U!==void 0&&U.context?String(U.context):"standalone",I;if(C==="formatting"&&H.formattingValues){var J=H.defaultFormattingWidth||H.defaultWidth,Z=U!==null&&U!==void 0&&U.width?String(U.width):J;I=H.formattingValues[Z]||H.formattingValues[J]}else{var Q=H.defaultWidth,A=U!==null&&U!==void 0&&U.width?String(U.width):H.defaultWidth;I=H.values[A]||H.values[Q]}var T=H.argumentCallback?H.argumentCallback(B):B;return I[T]}}var j={narrow:["BC","AC"],abbreviated:["\u7D00\u5143\u524D","\u897F\u66A6"],wide:["\u7D00\u5143\u524D","\u897F\u66A6"]},V={narrow:["1","2","3","4"],abbreviated:["Q1","Q2","Q3","Q4"],wide:["\u7B2C1\u56DB\u534A\u671F","\u7B2C2\u56DB\u534A\u671F","\u7B2C3\u56DB\u534A\u671F","\u7B2C4\u56DB\u534A\u671F"]},v={narrow:["1","2","3","4","5","6","7","8","9","10","11","12"],abbreviated:["1\u6708","2\u6708","3\u6708","4\u6708","5\u6708","6\u6708","7\u6708","8\u6708","9\u6708","10\u6708","11\u6708","12\u6708"],wide:["1\u6708","2\u6708","3\u6708","4\u6708","5\u6708","6\u6708","7\u6708","8\u6708","9\u6708","10\u6708","11\u6708","12\u6708"]},w={narrow:["\u65E5","\u6708","\u706B","\u6C34","\u6728","\u91D1","\u571F"],short:["\u65E5","\u6708","\u706B","\u6C34","\u6728","\u91D1","\u571F"],abbreviated:["\u65E5","\u6708","\u706B","\u6C34","\u6728","\u91D1","\u571F"],wide:["\u65E5\u66DC\u65E5","\u6708\u66DC\u65E5","\u706B\u66DC\u65E5","\u6C34\u66DC\u65E5","\u6728\u66DC\u65E5","\u91D1\u66DC\u65E5","\u571F\u66DC\u65E5"]},P={narrow:{am:"\u5348\u524D",pm:"\u5348\u5F8C",midnight:"\u6DF1\u591C",noon:"\u6B63\u5348",morning:"\u671D",afternoon:"\u5348\u5F8C",evening:"\u591C",night:"\u6DF1\u591C"},abbreviated:{am:"\u5348\u524D",pm:"\u5348\u5F8C",midnight:"\u6DF1\u591C",noon:"\u6B63\u5348",morning:"\u671D",afternoon:"\u5348\u5F8C",evening:"\u591C",night:"\u6DF1\u591C"},wide:{am:"\u5348\u524D",pm:"\u5348\u5F8C",midnight:"\u6DF1\u591C",noon:"\u6B63\u5348",morning:"\u671D",afternoon:"\u5348\u5F8C",evening:"\u591C",night:"\u6DF1\u591C"}},_={narrow:{am:"\u5348\u524D",pm:"\u5348\u5F8C",midnight:"\u6DF1\u591C",noon:"\u6B63\u5348",morning:"\u671D",afternoon:"\u5348\u5F8C",evening:"\u591C",night:"\u6DF1\u591C"},abbreviated:{am:"\u5348\u524D",pm:"\u5348\u5F8C",midnight:"\u6DF1\u591C",noon:"\u6B63\u5348",morning:"\u671D",afternoon:"\u5348\u5F8C",evening:"\u591C",night:"\u6DF1\u591C"},wide:{am:"\u5348\u524D",pm:"\u5348\u5F8C",midnight:"\u6DF1\u591C",noon:"\u6B63\u5348",morning:"\u671D",afternoon:"\u5348\u5F8C",evening:"\u591C",night:"\u6DF1\u591C"}},F=function H(B,U){var C=Number(B),I=String(U===null||U===void 0?void 0:U.unit);switch(I){case"year":return"".concat(C,"\u5E74");case"quarter":return"\u7B2C".concat(C,"\u56DB\u534A\u671F");case"month":return"".concat(C,"\u6708");case"week":return"\u7B2C".concat(C,"\u9031");case"date":return"".concat(C,"\u65E5");case"hour":return"".concat(C,"\u6642");case"minute":return"".concat(C,"\u5206");case"second":return"".concat(C,"\u79D2");default:return"".concat(C)}},h={ordinalNumber:F,era:E({values:j,defaultWidth:"wide"}),quarter:E({values:V,defaultWidth:"wide",argumentCallback:function H(B){return Number(B)-1}}),month:E({values:v,defaultWidth:"wide"}),day:E({values:w,defaultWidth:"wide"}),dayPeriod:E({values:P,defaultWidth:"wide",formattingValues:_,defaultFormattingWidth:"wide"})};function k(H){return function(B){var U=arguments.length>1&&arguments[1]!==void 0?arguments[1]:{},C=B.match(H.matchPattern);if(!C)return null;var I=C[0],J=B.match(H.parsePattern);if(!J)return null;var Z=H.valueCallback?H.valueCallback(J[0]):J[0];Z=U.valueCallback?U.valueCallback(Z):Z;var Q=B.slice(I.length);return{value:Z,rest:Q}}}function O(H){return function(B){var U=arguments.length>1&&arguments[1]!==void 0?arguments[1]:{},C=U.width,I=C&&H.matchPatterns[C]||H.matchPatterns[H.defaultMatchWidth],J=B.match(I);if(!J)return null;var Z=J[0],Q=C&&H.parsePatterns[C]||H.parsePatterns[H.defaultParseWidth],A=Array.isArray(Q)?y(Q,function(z){return z.test(Z)}):b(Q,function(z){return z.test(Z)}),T;T=H.valueCallback?H.valueCallback(A):A,T=U.valueCallback?U.valueCallback(T):T;var t=B.slice(Z.length);return{value:T,rest:t}}}var b=function H(B,U){for(var C in B)if(Object.prototype.hasOwnProperty.call(B,C)&&U(B[C]))return C;return},y=function H(B,U){for(var C=0;C<B.length;C++)if(U(B[C]))return C;return},c=/^第?\d+(年|四半期|月|週|日|時|分|秒)?/i,m=/\d+/i,g={narrow:/^(B\.?C\.?|A\.?D\.?)/i,abbreviated:/^(紀元[前後]|西暦)/i,wide:/^(紀元[前後]|西暦)/i},p={narrow:[/^B/i,/^A/i],any:[/^(紀元前)/i,/^(西暦|紀元後)/i]},u={narrow:/^[1234]/i,abbreviated:/^Q[1234]/i,wide:/^第[1234一二三四１２３４]四半期/i},d={any:[/(1|一|１)/i,/(2|二|２)/i,/(3|三|３)/i,/(4|四|４)/i]},l={narrow:/^([123456789]|1[012])/,abbreviated:/^([123456789]|1[012])月/i,wide:/^([123456789]|1[012])月/i},i={any:[/^1\D/,/^2/,/^3/,/^4/,/^5/,/^6/,/^7/,/^8/,/^9/,/^10/,/^11/,/^12/]},n={narrow:/^[日月火水木金土]/,short:/^[日月火水木金土]/,abbreviated:/^[日月火水木金土]/,wide:/^[日月火水木金土]曜日/},s={any:[/^日/,/^月/,/^火/,/^水/,/^木/,/^金/,/^土/]},o={any:/^(AM|PM|午前|午後|正午|深夜|真夜中|夜|朝)/i},r={any:{am:/^(A|午前)/i,pm:/^(P|午後)/i,midnight:/^深夜|真夜中/i,noon:/^正午/i,morning:/^朝/i,afternoon:/^午後/i,evening:/^夜/i,night:/^深夜/i}},a={ordinalNumber:k({matchPattern:c,parsePattern:m,valueCallback:function H(B){return parseInt(B,10)}}),era:O({matchPatterns:g,defaultMatchWidth:"wide",parsePatterns:p,defaultParseWidth:"any"}),quarter:O({matchPatterns:u,defaultMatchWidth:"wide",parsePatterns:d,defaultParseWidth:"any",valueCallback:function H(B){return B+1}}),month:O({matchPatterns:l,defaultMatchWidth:"wide",parsePatterns:i,defaultParseWidth:"any"}),day:O({matchPatterns:n,defaultMatchWidth:"wide",parsePatterns:s,defaultParseWidth:"any"}),dayPeriod:O({matchPatterns:o,defaultMatchWidth:"any",parsePatterns:r,defaultParseWidth:"any"})},e={code:"ja",formatDistance:K,formatLong:R,formatRelative:f,localize:h,match:a,options:{weekStartsOn:0,firstWeekContainsDate:1}};window.dateFns=x(x({},window.dateFns),{},{locale:x(x({},(G=window.dateFns)===null||G===void 0?void 0:G.locale),{},{ja:e})})})();

//# debugId=7B28AFF91CB81A7264756e2164756e21
