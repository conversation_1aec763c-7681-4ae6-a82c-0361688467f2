// قاعدة بيانات مبسطة باستخدام localStorage مع IndexedDB كنسخة احتياطية
export class SimpleDatabase {
  private storageKey = 'rafea_app_data';

  // الحصول على البيانات من localStorage
  private getData(): any {
    try {
      const data = localStorage.getItem(this.storageKey);
      return data ? JSON.parse(data) : this.getDefaultData();
    } catch (error) {
      console.error('خطأ في قراءة البيانات:', error);
      return this.getDefaultData();
    }
  }

  // حفظ البيانات في localStorage
  private saveData(data: any): void {
    try {
      localStorage.setItem(this.storageKey, JSON.stringify(data));
      console.log('تم حفظ البيانات بنجاح');
    } catch (error) {
      console.error('خطأ في حفظ البيانات:', error);
    }
  }

  // البيانات الافتراضية
  private getDefaultData(): any {
    return {
      users: [
        {
          id: 1,
          name: "أحمد محمد",
          email: "<EMAIL>",
          role: "مدير",
          department: "المبيعات",
          status: "نشط",
          lastActive: "2024-05-20",
          createdAt: new Date().toISOString(),
        },
        {
          id: 2,
          name: "سارة عبدالله",
          email: "<EMAIL>",
          role: "مسؤول",
          department: "المالية",
          status: "نشط",
          lastActive: "2024-05-22",
          createdAt: new Date().toISOString(),
        },
        {
          id: 3,
          name: "خالد العمري",
          email: "<EMAIL>",
          role: "موظف",
          department: "المبيعات",
          status: "غير نشط",
          lastActive: "2024-04-15",
          createdAt: new Date().toISOString(),
        },
        {
          id: 4,
          name: "نورة الشمري",
          email: "<EMAIL>",
          role: "مدير",
          department: "الصيانة",
          status: "نشط",
          lastActive: "2024-05-21",
          createdAt: new Date().toISOString(),
        },
        {
          id: 5,
          name: "فيصل السعيد",
          email: "<EMAIL>",
          role: "موظف",
          department: "المشتريات",
          status: "نشط",
          lastActive: "2024-05-19",
          createdAt: new Date().toISOString(),
        },
      ],
      properties: [],
      invoices: [],
      procurement: [],
      suppliers: [],
      sales: [],
      maintenance: [],
    };
  }

  // الحصول على جميع المستخدمين
  async getAllUsers(): Promise<any[]> {
    console.log('جاري تحميل المستخدمين...');
    const data = this.getData();
    console.log('المستخدمون المحملون:', data.users);
    return data.users || [];
  }

  // إضافة مستخدم جديد
  async addUser(userData: any): Promise<number> {
    console.log('إضافة مستخدم جديد:', userData);
    const data = this.getData();
    
    // إنشاء معرف جديد
    const maxId = data.users.length > 0 ? Math.max(...data.users.map((u: any) => u.id)) : 0;
    const newId = maxId + 1;
    
    const newUser = {
      ...userData,
      id: newId,
      createdAt: new Date().toISOString(),
    };
    
    data.users.push(newUser);
    this.saveData(data);
    
    console.log('تم إضافة المستخدم بنجاح:', newUser);
    return newId;
  }

  // تحديث مستخدم
  async updateUser(updatedUser: any): Promise<void> {
    console.log('تحديث المستخدم:', updatedUser);
    const data = this.getData();
    
    const index = data.users.findIndex((u: any) => u.id === updatedUser.id);
    if (index !== -1) {
      data.users[index] = updatedUser;
      this.saveData(data);
      console.log('تم تحديث المستخدم بنجاح');
    } else {
      throw new Error('المستخدم غير موجود');
    }
  }

  // حذف مستخدم
  async deleteUser(userId: number): Promise<void> {
    console.log('حذف المستخدم:', userId);
    const data = this.getData();
    
    const initialLength = data.users.length;
    data.users = data.users.filter((u: any) => u.id !== userId);
    
    if (data.users.length < initialLength) {
      this.saveData(data);
      console.log('تم حذف المستخدم بنجاح');
    } else {
      throw new Error('المستخدم غير موجود');
    }
  }

  // الحصول على مستخدم واحد
  async getUser(userId: number): Promise<any> {
    const data = this.getData();
    return data.users.find((u: any) => u.id === userId) || null;
  }

  // إنشاء نسخة احتياطية
  async createBackup(): Promise<string> {
    const data = this.getData();
    const backup = {
      ...data,
      timestamp: new Date().toISOString(),
      version: '1.0',
    };
    return JSON.stringify(backup, null, 2);
  }

  // استعادة النسخة الاحتياطية
  async restoreBackup(backupData: string): Promise<void> {
    try {
      const backup = JSON.parse(backupData);
      this.saveData(backup);
      console.log('تم استعادة النسخة الاحتياطية بنجاح');
    } catch (error) {
      throw new Error('فشل في استعادة النسخة الاحتياطية: ' + error);
    }
  }

  // تنزيل النسخة الاحتياطية
  async downloadBackup(): Promise<void> {
    try {
      const backupData = await this.createBackup();
      const blob = new Blob([backupData], { type: 'application/json' });
      const url = URL.createObjectURL(blob);
      
      const link = document.createElement('a');
      link.href = url;
      link.download = `rafea-backup-${new Date().toISOString().split('T')[0]}.json`;
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
      
      URL.revokeObjectURL(url);
    } catch (error) {
      throw new Error('فشل في تنزيل النسخة الاحتياطية: ' + error);
    }
  }

  // رفع واستعادة النسخة الاحتياطية من ملف
  async uploadAndRestoreBackup(file: File): Promise<void> {
    return new Promise((resolve, reject) => {
      const reader = new FileReader();
      
      reader.onload = async (event) => {
        try {
          const backupData = event.target?.result as string;
          await this.restoreBackup(backupData);
          resolve();
        } catch (error) {
          reject(error);
        }
      };
      
      reader.onerror = () => {
        reject(new Error('فشل في قراءة الملف'));
      };
      
      reader.readAsText(file);
    });
  }

  // مسح جميع البيانات
  async clearAllData(): Promise<void> {
    localStorage.removeItem(this.storageKey);
    console.log('تم مسح جميع البيانات');
  }

  // إعادة تعيين البيانات إلى القيم الافتراضية
  async resetToDefault(): Promise<void> {
    const defaultData = this.getDefaultData();
    this.saveData(defaultData);
    console.log('تم إعادة تعيين البيانات إلى القيم الافتراضية');
  }
}

// إنشاء مثيل واحد من قاعدة البيانات
export const simpleDatabase = new SimpleDatabase();

// أنواع البيانات
export interface User {
  id?: number;
  name: string;
  email: string;
  role: string;
  department: string;
  status: string;
  lastActive: string;
  createdAt?: string;
}
