"use strict";
exports.hu = void 0;
var _index = require("./hu/_lib/formatDistance.js");
var _index2 = require("./hu/_lib/formatLong.js");
var _index3 = require("./hu/_lib/formatRelative.js");
var _index4 = require("./hu/_lib/localize.js");
var _index5 = require("./hu/_lib/match.js");

/**
 * @category Locales
 * @summary Hungarian locale.
 * @language Hungarian
 * @iso-639-2 hun
 * <AUTHOR> [@pshpak](https://github.com/pshpak)
 * <AUTHOR> [@eduardopsll](https://github.com/eduardopsll)
 * <AUTHOR> [@twodcube](https://github.com/twodcube)
 */
const hu = (exports.hu = {
  code: "hu",
  formatDistance: _index.formatDistance,
  formatLong: _index2.formatLong,
  formatRelative: _index3.formatRelative,
  localize: _index4.localize,
  match: _index5.match,
  options: {
    weekStartsOn: 1 /* Monday */,
    firstWeekContainsDate: 4,
  },
});
