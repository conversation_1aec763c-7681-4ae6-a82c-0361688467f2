# 🏢 نظام إدارة العقارات - رافعة المدينة

[![النشر](https://img.shields.io/badge/النشر-جاهز-brightgreen)](https://rafea-madinah.netlify.app)
[![الإصدار](https://img.shields.io/badge/الإصدار-2.0.0-blue)](https://github.com/rafea-madinah/property-management-suite)
[![الترخيص](https://img.shields.io/badge/الترخيص-MIT-green)](LICENSE)

نظام شامل لإدارة العقارات والمبيعات والمستخلصات والصيانة مع قاعدة بيانات محلية موثوقة.

## 🚀 النشر المباشر

**التطبيق جاهز للنشر الآن!**

### نشر سريع (5 دقائق):
1. اذهب إلى [netlify.com](https://netlify.com)
2. اسح<PERSON> مجلد `dist` إلى الموقع
3. احصل على رابط مجاني فوراً!

📁 **ملفات النشر**: مجلد `dist` جاهز (16.69 KB)

## ✨ المميزات الجديدة

### 🗄️ نظام تخزين محلي موثوق
- قاعدة بيانات محلية باستخدام localStorage
- نسخ احتياطي تلقائي كل 5 دقائق
- استيراد وتصدير البيانات
- حماية من فقدان البيانات

### 📊 إدارة شاملة للبيانات
- **العقارات**: إدارة كاملة مع CRUD operations
- **المستخدمين**: إدارة الصلاحيات والأدوار
- **المستخلصات**: تتبع الفواتير والمدفوعات
- **الموردين**: قاعدة بيانات شاملة
- **المشتريات**: تتبع الطلبات والمواد

### 🎨 واجهة مستخدم محسنة
- تصميم عربي متجاوب (RTL)
- PWA - يعمل كتطبيق على الهاتف
- رسائل تفاعلية وتنبيهات
- جداول تفاعلية مع البحث
- إحصائيات مباشرة

## 🛠️ التقنيات المستخدمة

- **Frontend**: React 18 + TypeScript
- **Build Tool**: Vite
- **UI Framework**: Tailwind CSS + shadcn/ui
- **Routing**: React Router
- **Icons**: Lucide React
- **Storage**: localStorage API
- **PWA**: Web App Manifest

## 🚀 التشغيل والتطوير

### التشغيل المحلي
```bash
# تثبيت المكتبات
npm install

# تشغيل التطبيق
npm run dev

# فتح المتصفح على: http://localhost:8080
```

### البناء للإنتاج
```bash
# بناء التطبيق
npm run build:prod

# معاينة البناء
npm run preview

# نشر على Netlify
npm run deploy:netlify
```

## 📱 PWA Support

التطبيق يدعم Progressive Web App:
- تثبيت على الهاتف المحمول
- العمل بدون إنترنت
- تحديثات تلقائية
- تجربة تطبيق أصلي

## 🔒 الأمان والخصوصية

- جميع البيانات محفوظة محلياً
- لا يتم إرسال أي بيانات للخارج
- HTTPS إجباري في النشر
- Headers أمان محسنة

## 📋 الصفحات المتاحة

- **🏠 الرئيسية**: لوحة تحكم مع إحصائيات
- **🏢 العقارات**: إدارة العقارات والوحدات
- **👥 المستخدمين**: إدارة المستخدمين والصلاحيات
- **📄 المستخلصات**: إدارة الفواتير والمدفوعات
- **🏭 الموردين**: قاعدة بيانات الموردين
- **🛒 المشتريات**: تتبع الطلبات والمواد
- **💰 المبيعات**: إدارة عمليات البيع
- **🔧 الصيانة**: إدارة طلبات الصيانة
- **📊 التقارير**: تقارير شاملة
- **🔐 الصلاحيات**: إدارة صلاحيات المستخدمين

## 🎯 دليل الاستخدام السريع

### إضافة بيانات جديدة:
1. انقر على زر "إضافة" في أي صفحة
2. املأ البيانات المطلوبة (المميزة بـ *)
3. انقر "حفظ" أو "إضافة"

### تعديل البيانات:
1. ابحث عن العنصر في الجدول
2. انقر على أيقونة التعديل ✏️
3. عدّل البيانات المطلوبة
4. انقر "حفظ التغييرات"

### حذف البيانات:
1. انقر على أيقونة الحذف 🗑️
2. أكد عملية الحذف في النافذة المنبثقة

## 📊 هيكل البيانات

### العقارات (Properties)
```typescript
interface Property {
  id: number;
  title: string;
  type: string;
  location: string;
  area: string;
  price: string;
  status: string;
  description: string;
  createdAt: string;
}
```

### المستخدمين (Users)
```typescript
interface User {
  id: number;
  name: string;
  email: string;
  phone?: string;
  role: string;
  department: string;
  status: string;
  createdAt: string;
}
```

### المستخلصات (Invoices)
```typescript
interface Invoice {
  id: string;
  customerName: string;
  customerPhone: string;
  customerEmail: string;
  propertyId: string;
  propertyTitle: string;
  amount: string;
  status: string;
  date: string;
  dueDate: string;
}
```

## 🔧 ملفات النشر

### ملفات الإعداد المتوفرة:
- `netlify.toml` - إعدادات Netlify
- `vercel.json` - إعدادات Vercel
- `manifest.json` - PWA manifest
- `robots.txt` - SEO optimization

### Scripts متاحة:
```bash
npm run dev           # تشغيل التطبيق محلياً
npm run build         # بناء للإنتاج
npm run build:prod    # بناء محسن للإنتاج
npm run preview       # معاينة البناء
npm run deploy:netlify # نشر على Netlify
npm run deploy:vercel  # نشر على Vercel
```

## 📚 التوثيق الإضافي

- **DEPLOYMENT_GUIDE.md** - دليل النشر الشامل
- **FINAL_DEPLOYMENT.md** - تعليمات النشر النهائي
- **USER_GUIDE.md** - دليل المستخدم
- **CHANGELOG.md** - سجل التغييرات
- **TESTING_CHECKLIST.md** - قائمة الاختبارات

## 🆘 الدعم الفني

### مشاكل شائعة:
- **التطبيق لا يعمل**: تحقق من HTTPS ووجود جميع الملفات
- **البيانات لا تحفظ**: تحقق من دعم localStorage
- **PWA لا يعمل**: تأكد من HTTPS وملف manifest

### طلب المساعدة:
1. راجع التوثيق أولاً
2. فحص وحدة التحكم للأخطاء
3. اتصل بفريق الدعم

## 🎉 الخلاصة

**نظام إدارة العقارات - رافعة المدينة** هو تطبيق ويب شامل ومتطور لإدارة العقارات والمبيعات. التطبيق جاهز للنشر والاستخدام الفوري مع جميع المميزات المطلوبة.

### ✅ ما تم إنجازه:
- نظام تخزين محلي موثوق
- واجهة مستخدم عربية متكاملة
- دعم PWA للهواتف المحمولة
- إحصائيات شاملة ومباشرة
- نظام أمان محسن
- توثيق شامل

### 🚀 الخطوة التالية:
**نشر التطبيق والبدء في الاستخدام!**

---

**📞 للدعم**: راجع ملفات التوثيق أو اتصل بفريق التطوير  
**🌐 النشر**: جاهز للنشر على Netlify أو Vercel  
**📱 PWA**: يعمل كتطبيق على جميع الأجهزة  
**🔒 الأمان**: بيانات آمنة ومحفوظة محلياً
