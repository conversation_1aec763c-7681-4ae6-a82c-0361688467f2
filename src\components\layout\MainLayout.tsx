
import React, { useState } from "react";
import AppSidebar from "./AppSidebar";
import Header from "./Header";

interface MainLayoutProps {
  children: React.ReactNode;
}

const MainLayout: React.FC<MainLayoutProps> = ({ children }) => {
  const [isSidebarOpen, setIsSidebarOpen] = useState(true);

  return (
    <div dir="rtl" className="min-h-screen bg-muted/30">
      <AppSidebar userRole="admin" onSidebarToggle={setIsSidebarOpen} />
      <div className={`flex flex-col min-h-screen ${isSidebarOpen ? 'mr-64' : 'mr-16'} transition-all duration-300`}>
        <Header />
        <main className="flex-1 px-6 py-4">{children}</main>
      </div>
    </div>
  );
};

export default MainLayout;
