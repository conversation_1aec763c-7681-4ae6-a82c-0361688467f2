
import React from "react";
import AppSidebar from "./AppSidebar";
import Header from "./Header";

interface MainLayoutProps {
  children: React.ReactNode;
}

const MainLayout: React.FC<MainLayoutProps> = ({ children }) => {
  return (
    <div dir="rtl" className="min-h-screen bg-muted/30">
      <AppSidebar />
      <div className="flex flex-col min-h-screen">
        <Header />
        <main className="flex-1 px-6 py-4">{children}</main>
      </div>
    </div>
  );
};

export default MainLayout;
