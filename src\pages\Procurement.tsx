import React, { useState, useEffect } from "react";
import MainLayout from "@/components/layout/MainLayout";
import { But<PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Badge } from "@/components/ui/badge";
import { Label } from "@/components/ui/label";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import {
  Dialog,
  DialogContent,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog";
import { Plus, Edit, Trash2, Package, Save, X } from "lucide-react";
import { useToast } from "@/components/ui/use-toast";
import { DataService, ProcurementItem } from "@/lib/storage";

const Procurement = () => {
  const { toast } = useToast();
  const [procurement, setProcurement] = useState<ProcurementItem[]>([]);
  const [isAddDialogOpen, setIsAddDialogOpen] = useState(false);
  const [isEditDialogOpen, setIsEditDialogOpen] = useState(false);
  const [itemToEdit, setItemToEdit] = useState<ProcurementItem | null>(null);
  const [isLoading, setIsLoading] = useState(true);

  // بيانات النموذج
  const [formData, setFormData] = useState({
    itemName: "",
    category: "",
    supplier: "",
    quantity: "",
    unitPrice: "",
    status: "معلق",
    requestDate: new Date().toISOString().split('T')[0],
    deliveryDate: ""
  });

  // تحميل البيانات عند بدء التطبيق
  useEffect(() => {
    loadProcurement();
  }, []);

  const loadProcurement = () => {
    try {
      setIsLoading(true);
      console.log('🔄 جاري تحميل المشتريات...');
      const loadedProcurement = DataService.getProcurement();
      setProcurement(loadedProcurement);
      console.log('✅ تم تحميل المشتريات بنجاح:', loadedProcurement);
    } catch (error) {
      console.error('❌ خطأ في تحميل المشتريات:', error);
      toast({
        title: "خطأ في التحميل",
        description: "حدث خطأ أثناء تحميل بيانات المشتريات",
        variant: "destructive",
      });
    } finally {
      setIsLoading(false);
    }
  };

  // إضافة عنصر جديد
  const handleAddItem = () => {
    console.log('🔄 محاولة إضافة عنصر:', formData);

    if (!formData.itemName.trim() || !formData.category.trim() || !formData.quantity.trim()) {
      toast({
        title: "خطأ في البيانات",
        description: "يرجى ملء اسم العنصر والفئة والكمية على الأقل",
        variant: "destructive",
      });
      return;
    }

    try {
      const newItemId = DataService.generateProcurementId();
      const quantity = parseInt(formData.quantity) || 0;
      const unitPrice = parseFloat(formData.unitPrice) || 0;
      const totalPrice = (quantity * unitPrice).toString();

      const newItem = DataService.addProcurementItem({
        id: newItemId,
        itemName: formData.itemName.trim(),
        category: formData.category.trim(),
        supplier: formData.supplier.trim() || "غير محدد",
        quantity: quantity,
        unitPrice: formData.unitPrice.trim() || "0",
        totalPrice: totalPrice,
        status: formData.status,
        requestDate: formData.requestDate,
        deliveryDate: formData.deliveryDate || ""
      });

      console.log('✅ تم إضافة العنصر:', newItem);

      // إعادة تحميل البيانات
      loadProcurement();

      // إعادة تعيين النموذج
      setFormData({
        itemName: "",
        category: "",
        supplier: "",
        quantity: "",
        unitPrice: "",
        status: "معلق",
        requestDate: new Date().toISOString().split('T')[0],
        deliveryDate: ""
      });
      setIsAddDialogOpen(false);

      toast({
        title: "تم بنجاح",
        description: `تم إضافة العنصر ${newItem.itemName} بنجاح`,
      });
    } catch (error) {
      console.error('❌ خطأ في إضافة العنصر:', error);
      toast({
        title: "خطأ في الإضافة",
        description: "حدث خطأ أثناء إضافة العنصر",
        variant: "destructive",
      });
    }
  };

  // تعديل عنصر
  const handleEditItem = (item: ProcurementItem) => {
    console.log('🔄 فتح نافذة تعديل العنصر:', item);
    setItemToEdit(item);
    setFormData({
      itemName: item.itemName,
      category: item.category,
      supplier: item.supplier,
      quantity: item.quantity.toString(),
      unitPrice: item.unitPrice,
      status: item.status,
      requestDate: item.requestDate,
      deliveryDate: item.deliveryDate
    });
    setIsEditDialogOpen(true);
  };

  // حفظ التعديل
  const handleSaveEdit = () => {
    if (!itemToEdit) return;

    console.log('🔄 محاولة حفظ تعديل العنصر:', formData);

    if (!formData.itemName.trim() || !formData.category.trim() || !formData.quantity.trim()) {
      toast({
        title: "خطأ في البيانات",
        description: "يرجى ملء اسم العنصر والفئة والكمية على الأقل",
        variant: "destructive",
      });
      return;
    }

    try {
      const quantity = parseInt(formData.quantity) || 0;
      const unitPrice = parseFloat(formData.unitPrice) || 0;
      const totalPrice = (quantity * unitPrice).toString();

      const updatedItem: ProcurementItem = {
        ...itemToEdit,
        itemName: formData.itemName.trim(),
        category: formData.category.trim(),
        supplier: formData.supplier.trim() || "غير محدد",
        quantity: quantity,
        unitPrice: formData.unitPrice.trim() || "0",
        totalPrice: totalPrice,
        status: formData.status,
        requestDate: formData.requestDate,
        deliveryDate: formData.deliveryDate
      };

      DataService.updateProcurementItem(updatedItem);
      console.log('✅ تم تعديل العنصر:', updatedItem);

      // إعادة تحميل البيانات
      loadProcurement();

      setIsEditDialogOpen(false);
      setItemToEdit(null);
      setFormData({
        itemName: "",
        category: "",
        supplier: "",
        quantity: "",
        unitPrice: "",
        status: "معلق",
        requestDate: new Date().toISOString().split('T')[0],
        deliveryDate: ""
      });

      toast({
        title: "تم التعديل",
        description: `تم تعديل بيانات العنصر ${updatedItem.itemName} بنجاح`,
      });
    } catch (error) {
      console.error('❌ خطأ في تعديل العنصر:', error);
      toast({
        title: "خطأ في التعديل",
        description: "حدث خطأ أثناء تعديل العنصر",
        variant: "destructive",
      });
    }
  };

  // حذف عنصر
  const handleDeleteItem = (itemId: string, itemName: string) => {
    console.log('🔄 محاولة حذف العنصر:', itemId, itemName);

    if (confirm(`هل أنت متأكد من حذف العنصر "${itemName}"؟`)) {
      try {
        DataService.deleteProcurementItem(itemId);
        console.log('✅ تم حذف العنصر:', itemId);

        // إعادة تحميل البيانات
        loadProcurement();

        toast({
          title: "تم الحذف",
          description: `تم حذف العنصر ${itemName} بنجاح`,
        });
      } catch (error) {
        console.error('❌ خطأ في حذف العنصر:', error);
        toast({
          title: "خطأ في الحذف",
          description: "حدث خطأ أثناء حذف العنصر",
          variant: "destructive",
        });
      }
    }
  };

  // إحصائيات
  const pendingItems = procurement.filter(p => p.status === "معلق").length;
  const inProgressItems = procurement.filter(p => p.status === "قيد التنفيذ").length;
  const deliveredItems = procurement.filter(p => p.status === "تم التسليم").length;

  const totalValue = procurement.reduce((sum, item) => {
    const value = parseFloat(item.totalPrice.replace(/,/g, '')) || 0;
    return sum + value;
  }, 0);

  if (isLoading) {
    return (
      <MainLayout>
        <div className="flex items-center justify-center h-64">
          <div className="text-center">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto mb-4"></div>
            <p>جاري تحميل البيانات...</p>
          </div>
        </div>
      </MainLayout>
    );
  }

  return (
    <MainLayout>
      <div className="space-y-6">
        {/* العنوان وزر الإضافة */}
        <div className="flex justify-between items-center">
          <h1 className="text-2xl font-bold">إدارة المشتريات</h1>
          <Dialog open={isAddDialogOpen} onOpenChange={setIsAddDialogOpen}>
            <DialogTrigger asChild>
              <Button onClick={() => {
                console.log('🔄 فتح نافذة إضافة عنصر');
                setFormData({
                  itemName: "",
                  category: "",
                  supplier: "",
                  quantity: "",
                  unitPrice: "",
                  status: "معلق",
                  requestDate: new Date().toISOString().split('T')[0],
                  deliveryDate: ""
                });
              }}>
                <Plus className="mr-2 h-4 w-4" />
                إضافة عنصر
              </Button>
            </DialogTrigger>
            <DialogContent className="max-w-md">
              <DialogHeader>
                <DialogTitle>إضافة عنصر جديد</DialogTitle>
              </DialogHeader>
              <div className="space-y-4 max-h-96 overflow-y-auto">
                <div>
                  <Label htmlFor="itemName">اسم العنصر *</Label>
                  <Input
                    id="itemName"
                    value={formData.itemName}
                    onChange={(e) => {
                      console.log('📝 تغيير اسم العنصر:', e.target.value);
                      setFormData({...formData, itemName: e.target.value});
                    }}
                    placeholder="أدخل اسم العنصر"
                  />
                </div>
                <div>
                  <Label htmlFor="category">الفئة *</Label>
                  <select
                    id="category"
                    value={formData.category}
                    onChange={(e) => {
                      console.log('📝 تغيير الفئة:', e.target.value);
                      setFormData({...formData, category: e.target.value});
                    }}
                    className="w-full p-2 border rounded-md"
                  >
                    <option value="">اختر الفئة</option>
                    <option value="مواد البناء">مواد البناء</option>
                    <option value="كهرباء">كهرباء</option>
                    <option value="سباكة">سباكة</option>
                    <option value="دهانات">دهانات</option>
                    <option value="أدوات">أدوات</option>
                    <option value="أخرى">أخرى</option>
                  </select>
                </div>
                <div>
                  <Label htmlFor="supplier">المورد</Label>
                  <Input
                    id="supplier"
                    value={formData.supplier}
                    onChange={(e) => {
                      console.log('📝 تغيير المورد:', e.target.value);
                      setFormData({...formData, supplier: e.target.value});
                    }}
                    placeholder="اسم المورد"
                  />
                </div>
                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <Label htmlFor="quantity">الكمية *</Label>
                    <Input
                      id="quantity"
                      type="number"
                      value={formData.quantity}
                      onChange={(e) => {
                        console.log('📝 تغيير الكمية:', e.target.value);
                        setFormData({...formData, quantity: e.target.value});
                      }}
                      placeholder="0"
                    />
                  </div>
                  <div>
                    <Label htmlFor="unitPrice">سعر الوحدة</Label>
                    <Input
                      id="unitPrice"
                      type="number"
                      step="0.01"
                      value={formData.unitPrice}
                      onChange={(e) => {
                        console.log('📝 تغيير سعر الوحدة:', e.target.value);
                        setFormData({...formData, unitPrice: e.target.value});
                      }}
                      placeholder="0.00"
                    />
                  </div>
                </div>
                <div>
                  <Label htmlFor="status">الحالة</Label>
                  <select
                    id="status"
                    value={formData.status}
                    onChange={(e) => {
                      console.log('📝 تغيير الحالة:', e.target.value);
                      setFormData({...formData, status: e.target.value});
                    }}
                    className="w-full p-2 border rounded-md"
                  >
                    <option value="معلق">معلق</option>
                    <option value="قيد التنفيذ">قيد التنفيذ</option>
                    <option value="تم التسليم">تم التسليم</option>
                  </select>
                </div>
              </div>
              <DialogFooter>
                <Button variant="outline" onClick={() => setIsAddDialogOpen(false)}>
                  <X className="mr-2 h-4 w-4" />
                  إلغاء
                </Button>
                <Button onClick={handleAddItem}>
                  <Save className="mr-2 h-4 w-4" />
                  إضافة
                </Button>
              </DialogFooter>
            </DialogContent>
          </Dialog>
        </div>

        {/* الإحصائيات */}
        <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
          <Card>
            <CardHeader>
              <CardTitle className="text-sm">إجمالي العناصر</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{procurement.length}</div>
              <p className="text-xs text-muted-foreground">{totalValue.toLocaleString()} ريال</p>
            </CardContent>
          </Card>
          <Card>
            <CardHeader>
              <CardTitle className="text-sm">معلق</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold text-orange-600">{pendingItems}</div>
            </CardContent>
          </Card>
          <Card>
            <CardHeader>
              <CardTitle className="text-sm">قيد التنفيذ</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold text-blue-600">{inProgressItems}</div>
            </CardContent>
          </Card>
          <Card>
            <CardHeader>
              <CardTitle className="text-sm">تم التسليم</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold text-green-600">{deliveredItems}</div>
            </CardContent>
          </Card>
        </div>

        {/* جدول المشتريات */}
        <Card>
          <CardHeader>
            <CardTitle>قائمة المشتريات ({procurement.length})</CardTitle>
          </CardHeader>
          <CardContent>
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>المعرف</TableHead>
                  <TableHead>العنصر</TableHead>
                  <TableHead>الفئة</TableHead>
                  <TableHead>المورد</TableHead>
                  <TableHead>الكمية</TableHead>
                  <TableHead>السعر الإجمالي</TableHead>
                  <TableHead>الحالة</TableHead>
                  <TableHead>الإجراءات</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {procurement.length === 0 ? (
                  <TableRow>
                    <TableCell colSpan={8} className="text-center py-8">
                      لا توجد بيانات
                    </TableCell>
                  </TableRow>
                ) : (
                  procurement.map((item) => (
                    <TableRow key={item.id}>
                      <TableCell>{item.id}</TableCell>
                      <TableCell>
                        <div className="flex items-center gap-2">
                          <Package className="h-4 w-4" />
                          {item.itemName}
                        </div>
                      </TableCell>
                      <TableCell>{item.category}</TableCell>
                      <TableCell>{item.supplier}</TableCell>
                      <TableCell>{item.quantity}</TableCell>
                      <TableCell>{item.totalPrice} ريال</TableCell>
                      <TableCell>
                        <Badge
                          variant={
                            item.status === "تم التسليم"
                              ? "default"
                              : item.status === "قيد التنفيذ"
                              ? "secondary"
                              : "outline"
                          }
                        >
                          {item.status}
                        </Badge>
                      </TableCell>
                      <TableCell>
                        <div className="flex gap-2">
                          <Button
                            variant="ghost"
                            size="sm"
                            onClick={() => handleEditItem(item)}
                          >
                            <Edit className="h-4 w-4" />
                          </Button>
                          <Button
                            variant="ghost"
                            size="sm"
                            onClick={() => handleDeleteItem(item.id, item.itemName)}
                          >
                            <Trash2 className="h-4 w-4" />
                          </Button>
                        </div>
                      </TableCell>
                    </TableRow>
                  ))
                )}
              </TableBody>
            </Table>
          </CardContent>
        </Card>

        {/* نافذة التعديل */}
        <Dialog open={isEditDialogOpen} onOpenChange={setIsEditDialogOpen}>
          <DialogContent className="max-w-md">
            <DialogHeader>
              <DialogTitle>تعديل العنصر</DialogTitle>
            </DialogHeader>
            <div className="space-y-4 max-h-96 overflow-y-auto">
              <div>
                <Label htmlFor="edit-itemName">اسم العنصر *</Label>
                <Input
                  id="edit-itemName"
                  value={formData.itemName}
                  onChange={(e) => {
                    console.log('📝 تعديل اسم العنصر:', e.target.value);
                    setFormData({...formData, itemName: e.target.value});
                  }}
                  placeholder="أدخل اسم العنصر"
                />
              </div>
              <div>
                <Label htmlFor="edit-category">الفئة *</Label>
                <select
                  id="edit-category"
                  value={formData.category}
                  onChange={(e) => {
                    console.log('📝 تعديل الفئة:', e.target.value);
                    setFormData({...formData, category: e.target.value});
                  }}
                  className="w-full p-2 border rounded-md"
                >
                  <option value="">اختر الفئة</option>
                  <option value="مواد البناء">مواد البناء</option>
                  <option value="كهرباء">كهرباء</option>
                  <option value="سباكة">سباكة</option>
                  <option value="دهانات">دهانات</option>
                  <option value="أدوات">أدوات</option>
                  <option value="أخرى">أخرى</option>
                </select>
              </div>
              <div>
                <Label htmlFor="edit-supplier">المورد</Label>
                <Input
                  id="edit-supplier"
                  value={formData.supplier}
                  onChange={(e) => {
                    console.log('📝 تعديل المورد:', e.target.value);
                    setFormData({...formData, supplier: e.target.value});
                  }}
                  placeholder="اسم المورد"
                />
              </div>
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <Label htmlFor="edit-quantity">الكمية *</Label>
                  <Input
                    id="edit-quantity"
                    type="number"
                    value={formData.quantity}
                    onChange={(e) => {
                      console.log('📝 تعديل الكمية:', e.target.value);
                      setFormData({...formData, quantity: e.target.value});
                    }}
                    placeholder="0"
                  />
                </div>
                <div>
                  <Label htmlFor="edit-unitPrice">سعر الوحدة</Label>
                  <Input
                    id="edit-unitPrice"
                    type="number"
                    step="0.01"
                    value={formData.unitPrice}
                    onChange={(e) => {
                      console.log('📝 تعديل سعر الوحدة:', e.target.value);
                      setFormData({...formData, unitPrice: e.target.value});
                    }}
                    placeholder="0.00"
                  />
                </div>
              </div>
              <div>
                <Label htmlFor="edit-status">الحالة</Label>
                <select
                  id="edit-status"
                  value={formData.status}
                  onChange={(e) => {
                    console.log('📝 تعديل الحالة:', e.target.value);
                    setFormData({...formData, status: e.target.value});
                  }}
                  className="w-full p-2 border rounded-md"
                >
                  <option value="معلق">معلق</option>
                  <option value="قيد التنفيذ">قيد التنفيذ</option>
                  <option value="تم التسليم">تم التسليم</option>
                </select>
              </div>
            </div>
            <DialogFooter>
              <Button variant="outline" onClick={() => {
                setIsEditDialogOpen(false);
                setItemToEdit(null);
              }}>
                <X className="mr-2 h-4 w-4" />
                إلغاء
              </Button>
              <Button onClick={handleSaveEdit}>
                <Save className="mr-2 h-4 w-4" />
                حفظ التغييرات
              </Button>
            </DialogFooter>
          </DialogContent>
        </Dialog>
      </div>
    </MainLayout>
  );
};

export default Procurement;
