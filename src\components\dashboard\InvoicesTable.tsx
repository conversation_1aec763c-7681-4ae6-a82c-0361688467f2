
import React from "react";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { Badge } from "@/components/ui/badge";

interface Invoice {
  id: string;
  contractor: string;
  project: string;
  amount: number;
  date: string;
  status: "pending" | "approved" | "paid";
}

const invoices: Invoice[] = [
  {
    id: "INV2025-001",
    contractor: "شركة البناء المتطورة",
    project: "واحة النخيل",
    amount: 240000,
    date: "2025-05-10",
    status: "pending",
  },
  {
    id: "INV2025-002",
    contractor: "مؤسسة الإنشاءات العصرية",
    project: "أبراج الروضة",
    amount: 185000,
    date: "2025-05-08",
    status: "approved",
  },
  {
    id: "INV2025-003",
    contractor: "شركة البناء المتطورة",
    project: "واحة النخيل",
    amount: 320000,
    date: "2025-05-05",
    status: "paid",
  },
  {
    id: "INV2025-004",
    contractor: "مؤسسة النجاح للمقاولات",
    project: "إشراق الحرم",
    amount: 95000,
    date: "2025-05-02",
    status: "paid",
  },
];

const InvoicesTable: React.FC = () => {
  return (
    <div className="rounded-md border bg-white">
      <Table>
        <TableHeader>
          <TableRow>
            <TableHead>رقم المستخلص</TableHead>
            <TableHead>المقاول</TableHead>
            <TableHead className="hidden md:table-cell">المشروع</TableHead>
            <TableHead>المبلغ</TableHead>
            <TableHead className="hidden sm:table-cell">التاريخ</TableHead>
            <TableHead>الحالة</TableHead>
          </TableRow>
        </TableHeader>
        <TableBody>
          {invoices.map((invoice) => (
            <TableRow key={invoice.id} className="hover:bg-muted/50">
              <TableCell className="font-medium">{invoice.id}</TableCell>
              <TableCell>{invoice.contractor}</TableCell>
              <TableCell className="hidden md:table-cell">
                {invoice.project}
              </TableCell>
              <TableCell>
                {invoice.amount.toLocaleString("ar-SA")} ريال
              </TableCell>
              <TableCell className="hidden sm:table-cell">
                {new Date(invoice.date).toLocaleDateString("ar-SA")}
              </TableCell>
              <TableCell>
                <Badge
                  variant="outline"
                  className={
                    invoice.status === "pending"
                      ? "bg-yellow-50 text-yellow-700 border-yellow-200"
                      : invoice.status === "approved"
                      ? "bg-blue-50 text-blue-700 border-blue-200"
                      : "bg-green-50 text-green-700 border-green-200"
                  }
                >
                  {invoice.status === "pending"
                    ? "قيد الانتظار"
                    : invoice.status === "approved"
                    ? "تمت الموافقة"
                    : "مدفوع"}
                </Badge>
              </TableCell>
            </TableRow>
          ))}
        </TableBody>
      </Table>
    </div>
  );
};

export default InvoicesTable;
