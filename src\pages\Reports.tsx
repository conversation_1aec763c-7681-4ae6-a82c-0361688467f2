
import React, { useState } from "react";
import MainLayout from "@/components/layout/MainLayout";
import { Button } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Tabs, Ta<PERSON>Content, Ta<PERSON>List, TabsTrigger } from "@/components/ui/tabs";
import { AreaChart, BarChart, PieChart } from "@/components/ui/chart";
import { Download, Filter } from "lucide-react";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";

const monthlyData = [
  {
    name: "يناير",
    sales: 450000,
    expenses: 320000,
  },
  {
    name: "فبراير",
    sales: 550000,
    expenses: 350000,
  },
  {
    name: "مارس",
    sales: 600000,
    expenses: 370000,
  },
  {
    name: "أبريل",
    sales: 750000,
    expenses: 430000,
  },
  {
    name: "ماي<PERSON>",
    sales: 800000,
    expenses: 450000,
  },
  {
    name: "يونيو",
    sales: 850000,
    expenses: 470000,
  },
];

const propertyTypeData = [
  { name: "شقق", value: 45 },
  { name: "فلل", value: 30 },
  { name: "أراضي", value: 15 },
  { name: "محلات تجارية", value: 10 },
];

const areaData = [
  { name: "المدينة المنورة", value: 50 },
  { name: "مكة المكرمة", value: 25 },
  { name: "الرياض", value: 15 },
  { name: "جدة", value: 10 },
];

const COLORS = ["#0088FE", "#00C49F", "#FFBB28", "#FF8042", "#8884d8"];

const Reports: React.FC = () => {
  const [yearFilter, setYearFilter] = useState("2024");

  return (
    <MainLayout>
      <div className="space-y-6">
        <div className="flex justify-between items-center">
          <h1 className="text-2xl font-bold">التقارير</h1>
          <div className="flex gap-3">
            <Button variant="outline" className="gap-2">
              <Download size={18} />
              تصدير التقارير
            </Button>
            <Button variant="outline" className="gap-2">
              <Filter size={18} />
              تصفية
            </Button>
          </div>
        </div>

        <div className="flex justify-end">
          <div className="w-[200px]">
            <Select value={yearFilter} onValueChange={setYearFilter}>
              <SelectTrigger>
                <SelectValue placeholder="اختر السنة" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="2022">2022</SelectItem>
                <SelectItem value="2023">2023</SelectItem>
                <SelectItem value="2024">2024</SelectItem>
              </SelectContent>
            </Select>
          </div>
        </div>

        <Tabs defaultValue="financial">
          <TabsList className="grid w-full grid-cols-3">
            <TabsTrigger value="financial">التقارير المالية</TabsTrigger>
            <TabsTrigger value="sales">تقارير المبيعات</TabsTrigger>
            <TabsTrigger value="properties">تقارير العقارات</TabsTrigger>
          </TabsList>

          <TabsContent value="financial" className="pt-4 space-y-6">
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <Card>
                <CardHeader className="pb-2">
                  <CardTitle className="text-sm font-medium text-muted-foreground">
                    إجمالي المبيعات
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="text-2xl font-bold">4,000,000 ريال</div>
                  <p className="text-xs text-muted-foreground mt-1">
                    <span className="text-emerald-500 font-medium">↗ 15%</span> من العام
                    الماضي
                  </p>
                </CardContent>
              </Card>
              <Card>
                <CardHeader className="pb-2">
                  <CardTitle className="text-sm font-medium text-muted-foreground">
                    إجمالي المصروفات
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="text-2xl font-bold">2,390,000 ريال</div>
                  <p className="text-xs text-muted-foreground mt-1">
                    <span className="text-red-500 font-medium">↗ 8%</span> من العام
                    الماضي
                  </p>
                </CardContent>
              </Card>
              <Card>
                <CardHeader className="pb-2">
                  <CardTitle className="text-sm font-medium text-muted-foreground">
                    صافي الربح
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="text-2xl font-bold">1,610,000 ريال</div>
                  <p className="text-xs text-muted-foreground mt-1">
                    <span className="text-emerald-500 font-medium">↗ 20%</span> من العام
                    الماضي
                  </p>
                </CardContent>
              </Card>
            </div>

            <Card>
              <CardHeader>
                <CardTitle>الأداء المالي الشهري</CardTitle>
              </CardHeader>
              <CardContent>
                <AreaChart
                  data={monthlyData}
                  categories={["sales", "expenses"]}
                  index="name"
                  colors={["emerald", "red"]}
                  valueFormatter={(value) => `${(value / 1000).toFixed(0)}ك ريال`}
                  yAxisWidth={60}
                  className="h-80"
                />
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="sales" className="pt-4 space-y-6">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <Card>
                <CardHeader>
                  <CardTitle>توزيع المبيعات حسب نوع العقار</CardTitle>
                </CardHeader>
                <CardContent>
                  <PieChart
                    data={propertyTypeData}
                    index="name"
                    valueFormatter={(value) => `${value}%`}
                    category="value"
                    colors={COLORS}
                    className="h-80"
                  />
                </CardContent>
              </Card>
              <Card>
                <CardHeader>
                  <CardTitle>توزيع المبيعات حسب المنطقة</CardTitle>
                </CardHeader>
                <CardContent>
                  <PieChart
                    data={areaData}
                    index="name"
                    valueFormatter={(value) => `${value}%`}
                    category="value"
                    colors={COLORS}
                    className="h-80"
                  />
                </CardContent>
              </Card>
            </div>

            <Card>
              <CardHeader>
                <CardTitle>متوسط سعر المتر المربع حسب المنطقة</CardTitle>
              </CardHeader>
              <CardContent>
                <BarChart
                  data={[
                    { area: "المدينة المنورة", price: 5000 },
                    { area: "مكة المكرمة", price: 8000 },
                    { area: "الرياض", price: 7200 },
                    { area: "جدة", price: 6500 },
                    { area: "الدمام", price: 4800 },
                  ]}
                  index="area"
                  categories={["price"]}
                  colors={["blue"]}
                  valueFormatter={(value) => `${value} ريال`}
                  className="h-80"
                />
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="properties" className="pt-4 space-y-6">
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <Card>
                <CardHeader className="pb-2">
                  <CardTitle className="text-sm font-medium text-muted-foreground">
                    إجمالي العقارات
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="text-2xl font-bold">124 عقار</div>
                </CardContent>
              </Card>
              <Card>
                <CardHeader className="pb-2">
                  <CardTitle className="text-sm font-medium text-muted-foreground">
                    العقارات المباعة
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="text-2xl font-bold">78 عقار</div>
                  <p className="text-xs text-muted-foreground mt-1">
                    62.9% من إجمالي العقارات
                  </p>
                </CardContent>
              </Card>
              <Card>
                <CardHeader className="pb-2">
                  <CardTitle className="text-sm font-medium text-muted-foreground">
                    العقارات المتاحة
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="text-2xl font-bold">46 عقار</div>
                  <p className="text-xs text-muted-foreground mt-1">
                    37.1% من إجمالي العقارات
                  </p>
                </CardContent>
              </Card>
            </div>

            <Card>
              <CardHeader>
                <CardTitle>نسب أنواع العقارات</CardTitle>
              </CardHeader>
              <CardContent className="flex justify-center items-center">
                <PieChart
                  data={[
                    { type: "شقق", count: 65 },
                    { type: "فلل", count: 25 },
                    { type: "أراضي", count: 20 },
                    { type: "محلات تجارية", count: 14 },
                  ]}
                  index="type"
                  valueFormatter={(value) => `${value} عقار`}
                  category="count"
                  colors={COLORS}
                  className="h-80 w-full max-w-md"
                />
              </CardContent>
            </Card>
          </TabsContent>
        </Tabs>
      </div>
    </MainLayout>
  );
};

export default Reports;
