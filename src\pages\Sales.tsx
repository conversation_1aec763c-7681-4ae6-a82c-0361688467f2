
import React, { useState } from "react";
import MainLayout from "@/components/layout/MainLayout";
import { Button } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Ta<PERSON>, Ta<PERSON><PERSON>ontent, Ta<PERSON>List, TabsTrigger } from "@/components/ui/tabs";
import { Badge } from "@/components/ui/badge";
import {
  Table,
  TableBody,
  TableCaption,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { Search, Plus, FileText, ArrowUpDown } from "lucide-react";

const salesData = [
  {
    id: "SL-001",
    propertyName: "برج الضيافة",
    clientName: "شركة الأفق للاستثمار",
    value: "2,500,000",
    date: "2023-12-15",
    status: "مكتملة",
  },
  {
    id: "SL-002",
    propertyName: "فيلا الروضة",
    clientName: "أحمد محمد",
    value: "1,800,000",
    date: "2024-01-20",
    status: "قيد التنفيذ",
  },
  {
    id: "SL-003",
    propertyName: "شقة الياسمين",
    clientName: "سارة خالد",
    value: "950,000",
    date: "2024-02-05",
    status: "قيد التنفيذ",
  },
  {
    id: "SL-004",
    propertyName: "مجمع النرجس التجاري",
    clientName: "مؤسسة الخليج",
    value: "3,200,000",
    date: "2023-11-10",
    status: "مكتملة",
  },
  {
    id: "SL-005",
    propertyName: "فيلا المروج",
    clientName: "محمد عبدالله",
    value: "2,100,000",
    date: "2024-03-01",
    status: "تحت الدراسة",
  },
];

const Sales: React.FC = () => {
  const [activeTab, setActiveTab] = useState("all");

  return (
    <MainLayout>
      <div className="space-y-6">
        <div className="flex justify-between items-center">
          <h1 className="text-2xl font-bold">المبيعات</h1>
          <div className="flex gap-3">
            <Button variant="outline" className="gap-2">
              <FileText size={18} />
              تصدير التقرير
            </Button>
            <Button className="gap-2">
              <Plus size={18} />
              إضافة عملية بيع جديدة
            </Button>
          </div>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          <Card>
            <CardHeader className="pb-2">
              <CardTitle className="text-sm font-medium text-muted-foreground">
                إجمالي المبيعات
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">10,550,000 ريال</div>
              <p className="text-xs text-muted-foreground mt-1">
                <span className="text-emerald-500 font-medium">↗ 12.5%</span> من
                الشهر الماضي
              </p>
            </CardContent>
          </Card>
          <Card>
            <CardHeader className="pb-2">
              <CardTitle className="text-sm font-medium text-muted-foreground">
                عدد الصفقات
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">15 صفقة</div>
              <p className="text-xs text-muted-foreground mt-1">
                <span className="text-emerald-500 font-medium">↗ 5%</span> من
                الشهر الماضي
              </p>
            </CardContent>
          </Card>
          <Card>
            <CardHeader className="pb-2">
              <CardTitle className="text-sm font-medium text-muted-foreground">
                متوسط قيمة الصفقة
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">1,750,000 ريال</div>
              <p className="text-xs text-muted-foreground mt-1">
                <span className="text-red-500 font-medium">↘ 2.3%</span> من
                الشهر الماضي
              </p>
            </CardContent>
          </Card>
        </div>

        <Card>
          <CardHeader className="pb-0">
            <div className="flex flex-col gap-4 md:flex-row md:items-center md:justify-between">
              <CardTitle>سجل المبيعات</CardTitle>
              <div className="flex flex-col gap-4 md:flex-row md:items-center">
                <div className="relative">
                  <Search className="absolute right-3 top-1/2 h-4 w-4 -translate-y-1/2 text-muted-foreground" />
                  <Input
                    placeholder="بحث..."
                    className="pr-9 max-w-[250px]"
                  />
                </div>
              </div>
            </div>
          </CardHeader>
          <CardContent className="pt-4">
            <Tabs defaultValue="all" onValueChange={setActiveTab} value={activeTab}>
              <TabsList>
                <TabsTrigger value="all">جميع المبيعات</TabsTrigger>
                <TabsTrigger value="completed">مكتملة</TabsTrigger>
                <TabsTrigger value="pending">قيد التنفيذ</TabsTrigger>
                <TabsTrigger value="review">تحت الدراسة</TabsTrigger>
              </TabsList>
              <TabsContent value="all" className="mt-4">
                <Table>
                  <TableCaption>قائمة بجميع المبيعات</TableCaption>
                  <TableHeader>
                    <TableRow>
                      <TableHead>رقم المعاملة</TableHead>
                      <TableHead>اسم العقار</TableHead>
                      <TableHead>اسم العميل</TableHead>
                      <TableHead>
                        <div className="flex items-center gap-1">
                          قيمة الصفقة
                          <ArrowUpDown size={16} className="cursor-pointer" />
                        </div>
                      </TableHead>
                      <TableHead>
                        <div className="flex items-center gap-1">
                          التاريخ
                          <ArrowUpDown size={16} className="cursor-pointer" />
                        </div>
                      </TableHead>
                      <TableHead>الحالة</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {salesData.map((sale) => (
                      <TableRow key={sale.id} className="cursor-pointer hover:bg-muted/50">
                        <TableCell>{sale.id}</TableCell>
                        <TableCell>{sale.propertyName}</TableCell>
                        <TableCell>{sale.clientName}</TableCell>
                        <TableCell>{sale.value} ريال</TableCell>
                        <TableCell>
                          {new Date(sale.date).toLocaleDateString("ar-SA")}
                        </TableCell>
                        <TableCell>
                          <Badge
                            variant={
                              sale.status === "مكتملة"
                                ? "default"
                                : sale.status === "قيد التنفيذ"
                                ? "secondary"
                                : "outline"
                            }
                          >
                            {sale.status}
                          </Badge>
                        </TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
              </TabsContent>
              <TabsContent value="completed">
                <Table>
                  <TableCaption>قائمة بالمبيعات المكتملة</TableCaption>
                  <TableHeader>
                    <TableRow>
                      <TableHead>رقم المعاملة</TableHead>
                      <TableHead>اسم العقار</TableHead>
                      <TableHead>اسم العميل</TableHead>
                      <TableHead>قيمة الصفقة</TableHead>
                      <TableHead>التاريخ</TableHead>
                      <TableHead>الحالة</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {salesData
                      .filter((sale) => sale.status === "مكتملة")
                      .map((sale) => (
                        <TableRow key={sale.id} className="cursor-pointer hover:bg-muted/50">
                          <TableCell>{sale.id}</TableCell>
                          <TableCell>{sale.propertyName}</TableCell>
                          <TableCell>{sale.clientName}</TableCell>
                          <TableCell>{sale.value} ريال</TableCell>
                          <TableCell>
                            {new Date(sale.date).toLocaleDateString("ar-SA")}
                          </TableCell>
                          <TableCell>
                            <Badge>
                              {sale.status}
                            </Badge>
                          </TableCell>
                        </TableRow>
                      ))}
                  </TableBody>
                </Table>
              </TabsContent>
              {/* التبويبات الأخرى تكون مماثلة لكن مع فلترة البيانات */}
              <TabsContent value="pending" className="mt-4">
                <Table>
                  <TableCaption>قائمة بالمبيعات قيد التنفيذ</TableCaption>
                  <TableHeader>
                    <TableRow>
                      <TableHead>رقم المعاملة</TableHead>
                      <TableHead>اسم العقار</TableHead>
                      <TableHead>اسم العميل</TableHead>
                      <TableHead>قيمة الصفقة</TableHead>
                      <TableHead>التاريخ</TableHead>
                      <TableHead>الحالة</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {salesData
                      .filter((sale) => sale.status === "قيد التنفيذ")
                      .map((sale) => (
                        <TableRow key={sale.id} className="cursor-pointer hover:bg-muted/50">
                          <TableCell>{sale.id}</TableCell>
                          <TableCell>{sale.propertyName}</TableCell>
                          <TableCell>{sale.clientName}</TableCell>
                          <TableCell>{sale.value} ريال</TableCell>
                          <TableCell>
                            {new Date(sale.date).toLocaleDateString("ar-SA")}
                          </TableCell>
                          <TableCell>
                            <Badge variant="secondary">
                              {sale.status}
                            </Badge>
                          </TableCell>
                        </TableRow>
                      ))}
                  </TableBody>
                </Table>
              </TabsContent>
              <TabsContent value="review" className="mt-4">
                <Table>
                  <TableCaption>قائمة بالمبيعات تحت الدراسة</TableCaption>
                  <TableHeader>
                    <TableRow>
                      <TableHead>رقم المعاملة</TableHead>
                      <TableHead>اسم العقار</TableHead>
                      <TableHead>اسم العميل</TableHead>
                      <TableHead>قيمة الصفقة</TableHead>
                      <TableHead>التاريخ</TableHead>
                      <TableHead>الحالة</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {salesData
                      .filter((sale) => sale.status === "تحت الدراسة")
                      .map((sale) => (
                        <TableRow key={sale.id} className="cursor-pointer hover:bg-muted/50">
                          <TableCell>{sale.id}</TableCell>
                          <TableCell>{sale.propertyName}</TableCell>
                          <TableCell>{sale.clientName}</TableCell>
                          <TableCell>{sale.value} ريال</TableCell>
                          <TableCell>
                            {new Date(sale.date).toLocaleDateString("ar-SA")}
                          </TableCell>
                          <TableCell>
                            <Badge variant="outline">
                              {sale.status}
                            </Badge>
                          </TableCell>
                        </TableRow>
                      ))}
                  </TableBody>
                </Table>
              </TabsContent>
            </Tabs>
          </CardContent>
        </Card>
      </div>
    </MainLayout>
  );
};

export default Sales;
