
import React, { useState } from "react";
import MainLayout from "@/components/layout/MainLayout";
import { But<PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Badge } from "@/components/ui/badge";
import {
  Table,
  TableBody,
  TableCaption,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog";
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import { useForm } from "react-hook-form";
import { 
  Search, 
  Plus, 
  Shield, 
  Check, 
  X, 
  Users, 
  UserCog, 
  Building2,
  FileText,
  Wrench,
  DollarSign,
  ShoppingBag,
  BarChart3
} from "lucide-react";
import { useToast } from "@/components/ui/use-toast";

// بيانات الأدوار
const rolesData = [
  {
    id: 1,
    name: "مدير",
    description: "صلاحيات كاملة على النظام",
    usersCount: 4,
    createdAt: "2024-01-10",
  },
  {
    id: 2,
    name: "مسؤول",
    description: "يمكنه إدارة معظم الأقسام باستثناء الإعدادات والصلاحيات",
    usersCount: 3,
    createdAt: "2024-01-15",
  },
  {
    id: 3,
    name: "موظف",
    description: "صلاحيات محدودة للعرض وبعض التعديلات",
    usersCount: 8,
    createdAt: "2024-01-20",
  },
  {
    id: 4,
    name: "مبيعات",
    description: "صلاحيات خاصة بقسم المبيعات",
    usersCount: 5,
    createdAt: "2024-02-10",
  },
  {
    id: 5,
    name: "مالية",
    description: "إدارة المالية والفواتير والمستخلصات",
    usersCount: 3,
    createdAt: "2024-02-15",
  },
  {
    id: 6,
    name: "صيانة",
    description: "إدارة طلبات وعمليات الصيانة",
    usersCount: 4,
    createdAt: "2024-03-01",
  },
];

// بيانات صلاحيات النظام
const permissionsData = [
  {
    module: "المستخدمين",
    icon: <Users size={16} />,
    permissions: [
      { name: "عرض المستخدمين", admin: true, manager: true, employee: false },
      { name: "إضافة مستخدم", admin: true, manager: false, employee: false },
      { name: "تعديل مستخدم", admin: true, manager: true, employee: false },
      { name: "حذف مستخدم", admin: true, manager: false, employee: false },
    ],
  },
  {
    module: "الأدوار والصلاحيات",
    icon: <UserCog size={16} />,
    permissions: [
      { name: "عرض الأدوار", admin: true, manager: true, employee: false },
      { name: "إضافة دور", admin: true, manager: false, employee: false },
      { name: "تعديل دور", admin: true, manager: false, employee: false },
      { name: "حذف دور", admin: true, manager: false, employee: false },
    ],
  },
  {
    module: "العقارات",
    icon: <Building2 size={16} />,
    permissions: [
      { name: "عرض العقارات", admin: true, manager: true, employee: true },
      { name: "إضافة عقار", admin: true, manager: true, employee: false },
      { name: "تعديل عقار", admin: true, manager: true, employee: false },
      { name: "حذف عقار", admin: true, manager: false, employee: false },
    ],
  },
  {
    module: "المبيعات",
    icon: <DollarSign size={16} />,
    permissions: [
      { name: "عرض المبيعات", admin: true, manager: true, employee: true },
      { name: "إضافة عملية بيع", admin: true, manager: true, employee: false },
      { name: "تعديل عملية بيع", admin: true, manager: true, employee: false },
      { name: "حذف عملية بيع", admin: true, manager: false, employee: false },
    ],
  },
  {
    module: "المشتريات",
    icon: <ShoppingBag size={16} />,
    permissions: [
      { name: "عرض المشتريات", admin: true, manager: true, employee: true },
      { name: "إضافة مشتريات", admin: true, manager: true, employee: false },
      { name: "تعديل مشتريات", admin: true, manager: true, employee: false },
      { name: "حذف مشتريات", admin: true, manager: false, employee: false },
    ],
  },
  {
    module: "المستخلصات",
    icon: <FileText size={16} />,
    permissions: [
      { name: "عرض المستخلصات", admin: true, manager: true, employee: true },
      { name: "إضافة مستخلص", admin: true, manager: true, employee: false },
      { name: "تعديل مستخلص", admin: true, manager: true, employee: false },
      { name: "حذف مستخلص", admin: true, manager: false, employee: false },
    ],
  },
  {
    module: "الصيانة",
    icon: <Wrench size={16} />,
    permissions: [
      { name: "عرض طلبات الصيانة", admin: true, manager: true, employee: true },
      { name: "إضافة طلب صيانة", admin: true, manager: true, employee: true },
      { name: "تعديل طلب صيانة", admin: true, manager: true, employee: false },
      { name: "حذف طلب صيانة", admin: true, manager: false, employee: false },
    ],
  },
  {
    module: "التقارير",
    icon: <BarChart3 size={16} />,
    permissions: [
      { name: "عرض التقارير", admin: true, manager: true, employee: false },
      { name: "تصدير تقارير", admin: true, manager: true, employee: false },
    ],
  },
];

const Permissions: React.FC = () => {
  const { toast } = useToast();
  const [activeTab, setActiveTab] = useState("roles");
  const [searchQuery, setSearchQuery] = useState("");
  
  // نموذج إضافة دور جديد
  const form = useForm({
    defaultValues: {
      name: "",
      description: "",
    },
  });

  const onSubmit = (data: any) => {
    toast({
      title: "تمت الإضافة بنجاح",
      description: `تم إضافة الدور ${data.name} بنجاح.`,
    });
    console.log(data);
  };

  // تصفية الأدوار حسب البحث
  const filteredRoles = rolesData.filter((role) => {
    if (!searchQuery) return true;
    return (
      role.name.includes(searchQuery) ||
      role.description.includes(searchQuery)
    );
  });

  return (
    <MainLayout>
      <div className="space-y-6">
        <div className="flex justify-between items-center">
          <h1 className="text-2xl font-bold">إدارة الصلاحيات</h1>
          <Dialog>
            <DialogTrigger asChild>
              <Button className="gap-2">
                <Plus size={18} />
                إضافة دور جديد
              </Button>
            </DialogTrigger>
            <DialogContent className="sm:max-w-[425px]">
              <DialogHeader>
                <DialogTitle>إضافة دور جديد</DialogTitle>
                <DialogDescription>
                  قم بإدخال اسم ووصف الدور الجديد. اضغط على "إضافة" عند الانتهاء.
                </DialogDescription>
              </DialogHeader>
              <Form {...form}>
                <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-4">
                  <FormField
                    control={form.control}
                    name="name"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>اسم الدور</FormLabel>
                        <FormControl>
                          <Input placeholder="أدخل اسم الدور" {...field} />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                  <FormField
                    control={form.control}
                    name="description"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>وصف الدور</FormLabel>
                        <FormControl>
                          <Input placeholder="أدخل وصف الدور وصلاحياته" {...field} />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                  <DialogFooter>
                    <Button type="submit">إضافة</Button>
                  </DialogFooter>
                </form>
              </Form>
            </DialogContent>
          </Dialog>
        </div>

        <Card>
          <CardHeader>
            <div className="flex flex-col gap-4 md:flex-row md:items-center md:justify-between">
              <CardTitle>الأدوار والصلاحيات</CardTitle>
              <div className="relative">
                <Search className="absolute right-3 top-1/2 h-4 w-4 -translate-y-1/2 text-muted-foreground" />
                <Input
                  placeholder="بحث..."
                  className="pr-9 max-w-[250px]"
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                />
              </div>
            </div>
          </CardHeader>
          <CardContent>
            <Tabs defaultValue="roles" onValueChange={setActiveTab} value={activeTab}>
              <TabsList className="grid w-full grid-cols-2">
                <TabsTrigger value="roles">الأدوار</TabsTrigger>
                <TabsTrigger value="permissions">تفاصيل الصلاحيات</TabsTrigger>
              </TabsList>
              <TabsContent value="roles" className="pt-4">
                <RolesTable roles={filteredRoles} />
              </TabsContent>
              <TabsContent value="permissions" className="pt-4">
                <PermissionsTable permissions={permissionsData} />
              </TabsContent>
            </Tabs>
          </CardContent>
        </Card>
      </div>
    </MainLayout>
  );
};

interface RolesTableProps {
  roles: {
    id: number;
    name: string;
    description: string;
    usersCount: number;
    createdAt: string;
  }[];
}

const RolesTable: React.FC<RolesTableProps> = ({ roles }) => {
  return (
    <Table>
      <TableCaption>قائمة الأدوار المتاحة في النظام</TableCaption>
      <TableHeader>
        <TableRow>
          <TableHead className="w-[100px]">المعرف</TableHead>
          <TableHead>الدور</TableHead>
          <TableHead>الوصف</TableHead>
          <TableHead>عدد المستخدمين</TableHead>
          <TableHead>تاريخ الإنشاء</TableHead>
          <TableHead>خيارات</TableHead>
        </TableRow>
      </TableHeader>
      <TableBody>
        {roles.map((role) => (
          <TableRow key={role.id} className="cursor-pointer hover:bg-muted/50">
            <TableCell className="font-medium">#{role.id}</TableCell>
            <TableCell>
              <div className="flex items-center gap-2">
                <div className="w-8 h-8 rounded-full bg-primary/10 flex items-center justify-center">
                  <Shield size={16} className="text-primary" />
                </div>
                {role.name}
              </div>
            </TableCell>
            <TableCell>{role.description}</TableCell>
            <TableCell>
              <div className="flex items-center gap-1">
                <Users size={16} className="text-muted-foreground" />
                {role.usersCount}
              </div>
            </TableCell>
            <TableCell>{new Date(role.createdAt).toLocaleDateString("ar-SA")}</TableCell>
            <TableCell>
              <div className="flex items-center gap-2">
                <Button variant="outline" size="sm">
                  تعديل
                </Button>
                <Button variant="ghost" size="sm" className="text-destructive">
                  حذف
                </Button>
              </div>
            </TableCell>
          </TableRow>
        ))}
      </TableBody>
    </Table>
  );
};

interface PermissionsTableProps {
  permissions: {
    module: string;
    icon: React.ReactNode;
    permissions: {
      name: string;
      admin: boolean;
      manager: boolean;
      employee: boolean;
    }[];
  }[];
}

const PermissionsTable: React.FC<PermissionsTableProps> = ({ permissions }) => {
  return (
    <Table>
      <TableCaption>تفاصيل الصلاحيات لكل دور في النظام</TableCaption>
      <TableHeader>
        <TableRow>
          <TableHead className="w-[200px]">الوحدة</TableHead>
          <TableHead>الصلاحية</TableHead>
          <TableHead className="w-[100px]">مدير</TableHead>
          <TableHead className="w-[100px]">مسؤول</TableHead>
          <TableHead className="w-[100px]">موظف</TableHead>
        </TableRow>
      </TableHeader>
      <TableBody>
        {permissions.flatMap((module) =>
          module.permissions.map((permission, index) => (
            <TableRow key={`${module.module}-${permission.name}`} className="hover:bg-muted/50">
              {index === 0 && (
                <TableCell rowSpan={module.permissions.length} className="font-medium border-l">
                  <div className="flex items-center gap-2">
                    {module.icon}
                    {module.module}
                  </div>
                </TableCell>
              )}
              <TableCell>{permission.name}</TableCell>
              <TableCell>
                {permission.admin ? (
                  <Check size={18} className="text-green-600" />
                ) : (
                  <X size={18} className="text-destructive" />
                )}
              </TableCell>
              <TableCell>
                {permission.manager ? (
                  <Check size={18} className="text-green-600" />
                ) : (
                  <X size={18} className="text-destructive" />
                )}
              </TableCell>
              <TableCell>
                {permission.employee ? (
                  <Check size={18} className="text-green-600" />
                ) : (
                  <X size={18} className="text-destructive" />
                )}
              </TableCell>
            </TableRow>
          ))
        )}
      </TableBody>
    </Table>
  );
};

export default Permissions;
