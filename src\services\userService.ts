import { database, User } from '@/lib/database';

export class UserService {
  private storeName = 'users';

  // إضافة مستخدم جديد
  async addUser(userData: Omit<User, 'id' | 'createdAt'>): Promise<number> {
    const user: User = {
      ...userData,
      createdAt: new Date().toISOString(),
    };
    
    return await database.add(this.storeName, user);
  }

  // تحديث مستخدم
  async updateUser(user: User): Promise<void> {
    await database.update(this.storeName, user);
  }

  // حذف مستخدم
  async deleteUser(id: number): Promise<void> {
    await database.delete(this.storeName, id);
  }

  // الحصول على مستخدم واحد
  async getUser(id: number): Promise<User | null> {
    return await database.get(this.storeName, id);
  }

  // الحصول على جميع المستخدمين
  async getAllUsers(): Promise<User[]> {
    return await database.getAll(this.storeName);
  }

  // البحث عن مستخدم بالبريد الإلكتروني
  async getUserByEmail(email: string): Promise<User[]> {
    return await database.searchByIndex(this.storeName, 'email', email);
  }

  // البحث عن المستخدمين بالاسم
  async getUsersByName(name: string): Promise<User[]> {
    return await database.searchByIndex(this.storeName, 'name', name);
  }

  // الحصول على المستخدمين النشطين
  async getActiveUsers(): Promise<User[]> {
    const allUsers = await this.getAllUsers();
    return allUsers.filter(user => user.status === 'نشط');
  }

  // الحصول على المستخدمين حسب الدور
  async getUsersByRole(role: string): Promise<User[]> {
    const allUsers = await this.getAllUsers();
    return allUsers.filter(user => user.role === role);
  }

  // الحصول على المستخدمين حسب القسم
  async getUsersByDepartment(department: string): Promise<User[]> {
    const allUsers = await this.getAllUsers();
    return allUsers.filter(user => user.department === department);
  }

  // إحصائيات المستخدمين
  async getUserStats(): Promise<{
    total: number;
    active: number;
    inactive: number;
    byRole: Record<string, number>;
    byDepartment: Record<string, number>;
  }> {
    const users = await this.getAllUsers();
    
    const stats = {
      total: users.length,
      active: users.filter(u => u.status === 'نشط').length,
      inactive: users.filter(u => u.status === 'غير نشط').length,
      byRole: {} as Record<string, number>,
      byDepartment: {} as Record<string, number>,
    };

    // إحصائيات حسب الدور
    users.forEach(user => {
      stats.byRole[user.role] = (stats.byRole[user.role] || 0) + 1;
    });

    // إحصائيات حسب القسم
    users.forEach(user => {
      stats.byDepartment[user.department] = (stats.byDepartment[user.department] || 0) + 1;
    });

    return stats;
  }

  // تهيئة البيانات الأولية
  async initializeDefaultUsers(): Promise<void> {
    const existingUsers = await this.getAllUsers();
    
    if (existingUsers.length === 0) {
      const defaultUsers: Omit<User, 'id' | 'createdAt'>[] = [
        {
          name: "أحمد محمد",
          email: "<EMAIL>",
          role: "مدير",
          department: "المبيعات",
          status: "نشط",
          lastActive: "2024-05-20",
        },
        {
          name: "سارة عبدالله",
          email: "<EMAIL>",
          role: "مسؤول",
          department: "المالية",
          status: "نشط",
          lastActive: "2024-05-22",
        },
        {
          name: "خالد العمري",
          email: "<EMAIL>",
          role: "موظف",
          department: "المبيعات",
          status: "غير نشط",
          lastActive: "2024-04-15",
        },
        {
          name: "نورة الشمري",
          email: "<EMAIL>",
          role: "مدير",
          department: "الصيانة",
          status: "نشط",
          lastActive: "2024-05-21",
        },
        {
          name: "فيصل السعيد",
          email: "<EMAIL>",
          role: "موظف",
          department: "المشتريات",
          status: "نشط",
          lastActive: "2024-05-19",
        },
      ];

      for (const user of defaultUsers) {
        await this.addUser(user);
      }
    }
  }
}

export const userService = new UserService();
