[build]
  # Directory to change to before starting a build
  base = "."
  
  # Directory that contains the deploy-ready HTML files and assets
  publish = "dist"
  
  # Default build command
  command = "npm run build"

[build.environment]
  # Node.js version
  NODE_VERSION = "18"
  
  # NPM version
  NPM_VERSION = "9"

# Redirect rules for SPA (Single Page Application)
[[redirects]]
  from = "/*"
  to = "/index.html"
  status = 200

# Headers for security and performance
[[headers]]
  for = "/*"
  [headers.values]
    # Security headers
    X-Frame-Options = "DENY"
    X-XSS-Protection = "1; mode=block"
    X-Content-Type-Options = "nosniff"
    Referrer-Policy = "strict-origin-when-cross-origin"
    
    # Performance headers
    Cache-Control = "public, max-age=31536000"

# Specific headers for HTML files
[[headers]]
  for = "/*.html"
  [headers.values]
    Cache-Control = "public, max-age=0, must-revalidate"

# Headers for static assets
[[headers]]
  for = "/assets/*"
  [headers.values]
    Cache-Control = "public, max-age=31536000, immutable"

# Headers for manifest and service worker
[[headers]]
  for = "/manifest.json"
  [headers.values]
    Cache-Control = "public, max-age=86400"
    Content-Type = "application/manifest+json"

[[headers]]
  for = "/sw.js"
  [headers.values]
    Cache-Control = "public, max-age=0, must-revalidate"

# Error pages
[[redirects]]
  from = "/404"
  to = "/index.html"
  status = 404

# API redirects (if needed in future)
# [[redirects]]
#   from = "/api/*"
#   to = "/.netlify/functions/:splat"
#   status = 200

# Form handling (if needed)
# [build.processing]
#   skip_processing = false
# [build.processing.css]
#   bundle = true
#   minify = true
# [build.processing.js]
#   bundle = true
#   minify = true
# [build.processing.html]
#   pretty_urls = true

# Plugin configuration
# [[plugins]]
#   package = "@netlify/plugin-lighthouse"

# Environment-specific settings
[context.production.environment]
  NODE_ENV = "production"

[context.deploy-preview.environment]
  NODE_ENV = "development"

[context.branch-deploy.environment]
  NODE_ENV = "development"
