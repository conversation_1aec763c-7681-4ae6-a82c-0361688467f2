# نظام إدارة العقارات - رافعة المدينة

نظام شامل لإدارة العقارات والمبيعات والمستخلصات والصيانة مع قاعدة بيانات محلية موثوقة.

## المميزات الجديدة ✨

### 🗄️ نظام تخزين البيانات المحلي
- قاعدة بيانات محلية باستخدام localStorage
- نسخ احتياطي تلقائي للبيانات
- استيراد وتصدير البيانات
- حماية من فقدان البيانات

### 📊 إدارة شاملة للبيانات
- **العقارات**: إضافة، تعديل، حذف العقارات مع تفاصيل كاملة
- **المستخدمين**: إدارة المستخدمين والصلاحيات
- **المستخلصات**: إدارة الفواتير والمدفوعات
- **الموردين**: إدارة قاعدة بيانات الموردين
- **المشتريات**: تتبع المشتريات والطلبات

### 🎨 واجهة مستخدم محسنة
- تصميم عربي متجاوب
- رسائل تأكيد وتنبيهات
- جداول تفاعلية مع البحث والفلترة
- نوافذ حوار لإدخال البيانات

## المميزات الأساسية

- إدارة العقارات والوحدات
- إدارة المبيعات والعملاء
- إدارة المستخلصات والفواتير
- إدارة الصيانة والطلبات
- إدارة المستخدمين والصلاحيات
- إدارة الموردين والمشتريات
- تقارير شاملة
- واجهة مستخدم عربية

## التقنيات المستخدمة

- React 18
- TypeScript
- Vite
- Tailwind CSS
- shadcn/ui
- React Router
- Lucide Icons
- localStorage للتخزين المحلي

## التشغيل

```bash
npm install
npm run dev
```

## البناء للإنتاج

```bash
npm run build
```

## هيكل البيانات

### العقارات (Properties)
```typescript
interface Property {
  id: number;
  title: string;
  type: string;
  location: string;
  area: string;
  price: string;
  status: string;
  description: string;
  createdAt: string;
}
```

### المستخدمين (Users)
```typescript
interface User {
  id: number;
  name: string;
  email: string;
  phone: string;
  role: string;
  status: string;
  createdAt: string;
}
```

### المستخلصات (Invoices)
```typescript
interface Invoice {
  id: string;
  customerName: string;
  customerPhone: string;
  customerEmail: string;
  propertyId: string;
  propertyTitle: string;
  amount: string;
  status: string;
  date: string;
  dueDate: string;
}
```

### الموردين (Suppliers)
```typescript
interface Supplier {
  id: number;
  name: string;
  email: string;
  phone: string;
  address: string;
  category: string;
  status: string;
  createdAt: string;
}
```

### المشتريات (Procurement)
```typescript
interface ProcurementItem {
  id: string;
  itemName: string;
  category: string;
  supplier: string;
  quantity: number;
  unitPrice: string;
  totalPrice: string;
  status: string;
  requestDate: string;
  deliveryDate: string;
}
```

## إدارة البيانات

### النسخ الاحتياطي
- يتم إنشاء نسخة احتياطية تلقائياً كل 5 دقائق
- يمكن إنشاء نسخة احتياطية يدوياً من إعدادات النظام
- يتم حفظ النسخ الاحتياطية في localStorage

### الاستيراد والتصدير
- تصدير البيانات بصيغة JSON
- استيراد البيانات من ملفات JSON
- دمج البيانات المستوردة مع البيانات الموجودة

### الأمان
- تشفير البيانات الحساسة
- التحقق من صحة البيانات قبل الحفظ
- حماية من الكتابة فوق البيانات الموجودة

## الاستخدام

1. **إضافة البيانات**: استخدم أزرار "إضافة" في كل صفحة
2. **تعديل البيانات**: انقر على أيقونة التعديل في الجداول
3. **حذف البيانات**: انقر على أيقونة الحذف مع تأكيد العملية
4. **البحث والفلترة**: استخدم مربعات البحث في الجداول
5. **النسخ الاحتياطي**: من قائمة الإعدادات

## الصفحات المتاحة

- **الرئيسية**: لوحة تحكم مع إحصائيات عامة
- **العقارات**: إدارة العقارات والوحدات
- **المستخدمين**: إدارة المستخدمين والصلاحيات
- **المستخلصات**: إدارة الفواتير والمدفوعات
- **الموردين**: إدارة قاعدة بيانات الموردين
- **المشتريات**: تتبع المشتريات والطلبات
- **المبيعات**: إدارة عمليات البيع
- **الصيانة**: إدارة طلبات الصيانة
- **التقارير**: تقارير شاملة
- **الصلاحيات**: إدارة صلاحيات المستخدمين

## المساهمة

نرحب بالمساهمات! يرجى إنشاء Pull Request أو فتح Issue للمناقشة.

## الترخيص

هذا المشروع مرخص تحت رخصة MIT.

## ملاحظات التطوير

- تم تطوير النظام باستخدام أحدث التقنيات
- يدعم النظام اللغة العربية بالكامل
- تم تصميم النظام ليكون قابلاً للتوسع
- يمكن إضافة المزيد من الوحدات بسهولة

## الدعم الفني

للحصول على الدعم الفني أو الإبلاغ عن مشاكل، يرجى فتح Issue في المستودع.
