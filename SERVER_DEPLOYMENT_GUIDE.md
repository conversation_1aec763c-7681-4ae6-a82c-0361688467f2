# 🚀 دليل النشر على السيرفر الخارجي

## ✅ الملفات الجاهزة للنشر

تم إنشاء نسخة محسنة للسيرفر تتضمن:

### 📁 الملفات المطلوبة:
- `index.php` - الصفحة الرئيسية (نسخة السيرفر)
- `api.php` - واجهة برمجة التطبيقات
- `.htaccess` - إعدادات الأمان والحماية

## 🔧 متطلبات السيرفر

### الحد الأدنى:
- **PHP**: 7.4 أو أحدث (يفضل 8.0+)
- **Apache/Nginx**: مع دعم mod_rewrite
- **مساحة التخزين**: 50 MB كحد أدنى
- **الذاكرة**: 128 MB RAM
- **الصلاحيات**: كتابة وقراءة الملفات

### المميزات الاختيارية:
- **SSL Certificate**: للحصول على HTTPS
- **Backup Storage**: للنسخ الاحتياطية
- **CDN**: لتسريع التحميل

## 📤 خطوات النشر

### 1. تحضير الملفات
```bash
# الملفات المطلوبة للرفع:
- index.php
- api.php
- .htaccess
```

### 2. رفع الملفات للسيرفر
#### أ) عبر FTP:
1. افتح برنامج FTP (FileZilla مثلاً)
2. اتصل بالسيرفر
3. ارفع الملفات إلى مجلد `public_html` أو `www`

#### ب) عبر cPanel File Manager:
1. ادخل إلى cPanel
2. افتح File Manager
3. اذهب إلى public_html
4. ارفع الملفات

#### ج) عبر SSH:
```bash
scp index.php api.php .htaccess user@server:/path/to/web/
```

### 3. إعداد الصلاحيات
```bash
# إعداد صلاحيات الملفات
chmod 644 index.php
chmod 644 api.php
chmod 644 .htaccess

# إنشاء مجلد البيانات
mkdir data
chmod 755 data
```

## 🗄️ نظام التخزين

### مجلد البيانات:
سيتم إنشاء مجلد `data` تلقائياً يحتوي على:
- `properties.json` - بيانات العقارات
- `users.json` - بيانات المستخدمين
- `invoices.json` - بيانات المستخلصات
- `suppliers.json` - بيانات الموردين
- `procurement.json` - بيانات المشتريات
- `backup_*.json` - النسخ الاحتياطية

### الأمان:
- مجلد `data` محمي من الوصول المباشر
- الملفات محمية بـ `.htaccess`
- البيانات مشفرة بـ JSON

## 🔒 الأمان والحماية

### Headers الأمان:
- `X-Content-Type-Options: nosniff`
- `X-Frame-Options: DENY`
- `X-XSS-Protection: 1; mode=block`
- `Content-Security-Policy`

### حماية الملفات:
- منع الوصول لملفات `.json`
- حماية مجلد البيانات
- تشفير الاتصالات

### النسخ الاحتياطية:
- نسخ احتياطية تلقائية
- تصدير البيانات
- استعادة البيانات

## 🌐 اختبار النشر

### 1. فتح الموقع:
```
https://yourdomain.com
```

### 2. التحقق من الوظائف:
- [ ] تحميل الصفحة الرئيسية
- [ ] عرض الإحصائيات
- [ ] إضافة عقار جديد
- [ ] إضافة مستخدم جديد
- [ ] حفظ البيانات
- [ ] تصدير البيانات

### 3. فحص حالة السيرفر:
- مؤشر الاتصال في الأعلى يسار
- رسائل الخطأ في وحدة التحكم
- سرعة الاستجابة

## 🔧 إعدادات متقدمة

### تخصيص قاعدة البيانات:
```php
// في api.php يمكن تغيير مسار البيانات
$dataDir = __DIR__ . '/custom_data_path';
```

### تخصيص الأمان:
```php
// إضافة مصادقة
if (!isset($_SESSION['user_id'])) {
    http_response_code(401);
    exit('Unauthorized');
}
```

### تحسين الأداء:
```php
// إضافة cache
$cacheFile = $dataDir . '/cache_' . md5($type) . '.json';
if (file_exists($cacheFile) && (time() - filemtime($cacheFile)) < 300) {
    return json_decode(file_get_contents($cacheFile), true);
}
```

## 📊 مراقبة الأداء

### ملفات السجل:
- `error_log` - أخطاء PHP
- `access_log` - سجل الوصول
- `api_log.txt` - سجل API (اختياري)

### مراقبة الموارد:
- استخدام المعالج
- استخدام الذاكرة
- مساحة التخزين
- عدد الطلبات

## 🔄 التحديثات والصيانة

### تحديث الملفات:
1. عمل نسخة احتياطية من البيانات
2. رفع الملفات الجديدة
3. اختبار الوظائف
4. استعادة البيانات إذا لزم الأمر

### الصيانة الدورية:
- تنظيف ملفات السجل
- ضغط النسخ الاحتياطية القديمة
- فحص الأمان
- تحديث PHP

## 🆘 حل المشاكل

### مشاكل شائعة:

#### خطأ 500 Internal Server Error:
- تحقق من صلاحيات الملفات
- فحص ملف error_log
- تأكد من دعم PHP

#### البيانات لا تحفظ:
- تحقق من صلاحيات مجلد data
- فحص مساحة التخزين المتاحة
- تأكد من عدم وجود أخطاء PHP

#### بطء في الأداء:
- تحسين استعلامات البيانات
- تفعيل الضغط
- استخدام CDN

## 📞 الدعم الفني

### معلومات مفيدة للدعم:
- إصدار PHP: `<?php echo phpversion(); ?>`
- معلومات السيرفر: `<?php phpinfo(); ?>`
- مساحة التخزين: `<?php echo disk_free_space('.'); ?>`

### ملفات التشخيص:
```php
// إنشاء ملف تشخيص
file_put_contents('diagnostic.txt', 
    "PHP Version: " . phpversion() . "\n" .
    "Server: " . $_SERVER['SERVER_SOFTWARE'] . "\n" .
    "Free Space: " . disk_free_space('.') . "\n"
);
```

## 🎉 النتيجة المتوقعة

### بعد النشر الناجح:
- ✅ موقع يعمل على `https://yourdomain.com`
- ✅ جميع الوظائف تعمل بشكل مثالي
- ✅ البيانات محفوظة في ملفات على السيرفر
- ✅ نسخ احتياطية تلقائية
- ✅ أمان عالي ومحمي
- ✅ أداء سريع ومستقر

### المميزات المحققة:
- 🌐 **وصول عالمي**: من أي مكان في العالم
- 💾 **تخزين آمن**: ملفات محمية على السيرفر
- 🔄 **نسخ احتياطية**: تلقائية ويدوية
- 👥 **متعدد المستخدمين**: يمكن لعدة أشخاص الاستخدام
- 📱 **متجاوب**: يعمل على جميع الأجهزة
- 🔒 **آمن**: حماية شاملة للبيانات

---

**🎯 الهدف**: نظام إدارة عقارات احترافي على السيرفر  
**⏱️ وقت النشر**: 15-30 دقيقة  
**💰 التكلفة**: حسب خطة الاستضافة  
**🔧 الصيانة**: بسيطة وسهلة  

**✅ الضمان**: نظام موثوق وآمن 100%
