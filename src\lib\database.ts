// قاعدة البيانات المحلية باستخدام IndexedDB
class RafeaDatabase {
  private dbName = 'RafeaDB';
  private version = 1;
  private db: IDBDatabase | null = null;

  // فتح قاعدة البيانات
  async openDB(): Promise<IDBDatabase> {
    return new Promise((resolve, reject) => {
      const request = indexedDB.open(this.dbName, this.version);

      request.onerror = () => {
        reject(new Error('فشل في فتح قاعدة البيانات'));
      };

      request.onsuccess = () => {
        this.db = request.result;
        resolve(request.result);
      };

      request.onupgradeneeded = (event) => {
        const db = (event.target as IDBOpenDBRequest).result;

        // إنشاء جدول المستخدمين
        if (!db.objectStoreNames.contains('users')) {
          const usersStore = db.createObjectStore('users', { keyPath: 'id', autoIncrement: true });
          usersStore.createIndex('email', 'email', { unique: true });
          usersStore.createIndex('name', 'name', { unique: false });
        }

        // إنشاء جدول العقارات
        if (!db.objectStoreNames.contains('properties')) {
          const propertiesStore = db.createObjectStore('properties', { keyPath: 'id' });
          propertiesStore.createIndex('title', 'title', { unique: false });
          propertiesStore.createIndex('type', 'type', { unique: false });
          propertiesStore.createIndex('status', 'status', { unique: false });
        }

        // إنشاء جدول المستخلصات
        if (!db.objectStoreNames.contains('invoices')) {
          const invoicesStore = db.createObjectStore('invoices', { keyPath: 'id', autoIncrement: true });
          invoicesStore.createIndex('contractor', 'contractor', { unique: false });
          invoicesStore.createIndex('status', 'status', { unique: false });
        }

        // إنشاء جدول المشتريات
        if (!db.objectStoreNames.contains('procurement')) {
          const procurementStore = db.createObjectStore('procurement', { keyPath: 'id', autoIncrement: true });
          procurementStore.createIndex('title', 'title', { unique: false });
          procurementStore.createIndex('status', 'status', { unique: false });
        }

        // إنشاء جدول الموردين
        if (!db.objectStoreNames.contains('suppliers')) {
          const suppliersStore = db.createObjectStore('suppliers', { keyPath: 'id', autoIncrement: true });
          suppliersStore.createIndex('name', 'name', { unique: false });
          suppliersStore.createIndex('category', 'category', { unique: false });
        }

        // إنشاء جدول المبيعات
        if (!db.objectStoreNames.contains('sales')) {
          const salesStore = db.createObjectStore('sales', { keyPath: 'id', autoIncrement: true });
          salesStore.createIndex('propertyId', 'propertyId', { unique: false });
          salesStore.createIndex('status', 'status', { unique: false });
        }

        // إنشاء جدول الصيانة
        if (!db.objectStoreNames.contains('maintenance')) {
          const maintenanceStore = db.createObjectStore('maintenance', { keyPath: 'id', autoIncrement: true });
          maintenanceStore.createIndex('propertyId', 'propertyId', { unique: false });
          maintenanceStore.createIndex('status', 'status', { unique: false });
        }
      };
    });
  }

  // إضافة عنصر إلى جدول
  async add(storeName: string, data: any): Promise<any> {
    console.log(`محاولة إضافة بيانات إلى ${storeName}:`, data);
    const db = await this.openDB();
    return new Promise((resolve, reject) => {
      const transaction = db.transaction([storeName], 'readwrite');
      const store = transaction.objectStore(storeName);
      const request = store.add(data);

      request.onsuccess = () => {
        console.log(`تم إضافة البيانات بنجاح إلى ${storeName}، المعرف:`, request.result);
        resolve(request.result);
      };

      request.onerror = () => {
        console.error(`فشل في إضافة البيانات إلى ${storeName}:`, request.error);
        reject(new Error(`فشل في إضافة البيانات إلى ${storeName}: ${request.error}`));
      };
    });
  }

  // تحديث عنصر في جدول
  async update(storeName: string, data: any): Promise<any> {
    const db = await this.openDB();
    return new Promise((resolve, reject) => {
      const transaction = db.transaction([storeName], 'readwrite');
      const store = transaction.objectStore(storeName);
      const request = store.put(data);

      request.onsuccess = () => {
        resolve(request.result);
      };

      request.onerror = () => {
        reject(new Error(`فشل في تحديث البيانات في ${storeName}`));
      };
    });
  }

  // حذف عنصر من جدول
  async delete(storeName: string, id: any): Promise<void> {
    console.log(`محاولة حذف عنصر من ${storeName}، المعرف:`, id);
    const db = await this.openDB();
    return new Promise((resolve, reject) => {
      const transaction = db.transaction([storeName], 'readwrite');
      const store = transaction.objectStore(storeName);
      const request = store.delete(id);

      request.onsuccess = () => {
        console.log(`تم حذف العنصر بنجاح من ${storeName}، المعرف:`, id);
        resolve();
      };

      request.onerror = () => {
        console.error(`فشل في حذف البيانات من ${storeName}:`, request.error);
        reject(new Error(`فشل في حذف البيانات من ${storeName}: ${request.error}`));
      };
    });
  }

  // الحصول على عنصر واحد
  async get(storeName: string, id: any): Promise<any> {
    const db = await this.openDB();
    return new Promise((resolve, reject) => {
      const transaction = db.transaction([storeName], 'readonly');
      const store = transaction.objectStore(storeName);
      const request = store.get(id);

      request.onsuccess = () => {
        resolve(request.result);
      };

      request.onerror = () => {
        reject(new Error(`فشل في الحصول على البيانات من ${storeName}`));
      };
    });
  }

  // الحصول على جميع العناصر
  async getAll(storeName: string): Promise<any[]> {
    console.log(`محاولة الحصول على جميع البيانات من ${storeName}`);
    const db = await this.openDB();
    return new Promise((resolve, reject) => {
      const transaction = db.transaction([storeName], 'readonly');
      const store = transaction.objectStore(storeName);
      const request = store.getAll();

      request.onsuccess = () => {
        console.log(`تم الحصول على البيانات من ${storeName}:`, request.result);
        resolve(request.result);
      };

      request.onerror = () => {
        console.error(`فشل في الحصول على البيانات من ${storeName}:`, request.error);
        reject(new Error(`فشل في الحصول على البيانات من ${storeName}: ${request.error}`));
      };
    });
  }

  // البحث في جدول باستخدام فهرس
  async searchByIndex(storeName: string, indexName: string, value: any): Promise<any[]> {
    const db = await this.openDB();
    return new Promise((resolve, reject) => {
      const transaction = db.transaction([storeName], 'readonly');
      const store = transaction.objectStore(storeName);
      const index = store.index(indexName);
      const request = index.getAll(value);

      request.onsuccess = () => {
        resolve(request.result);
      };

      request.onerror = () => {
        reject(new Error(`فشل في البحث في ${storeName}`));
      };
    });
  }

  // مسح جدول بالكامل
  async clearStore(storeName: string): Promise<void> {
    const db = await this.openDB();
    return new Promise((resolve, reject) => {
      const transaction = db.transaction([storeName], 'readwrite');
      const store = transaction.objectStore(storeName);
      const request = store.clear();

      request.onsuccess = () => {
        resolve();
      };

      request.onerror = () => {
        reject(new Error(`فشل في مسح ${storeName}`));
      };
    });
  }

  // إنشاء نسخة احتياطية من قاعدة البيانات
  async createBackup(): Promise<string> {
    const backup: any = {};
    const storeNames = ['users', 'properties', 'invoices', 'procurement', 'suppliers', 'sales', 'maintenance'];

    for (const storeName of storeNames) {
      try {
        backup[storeName] = await this.getAll(storeName);
      } catch (error) {
        console.error(`خطأ في إنشاء نسخة احتياطية من ${storeName}:`, error);
        backup[storeName] = [];
      }
    }

    backup.timestamp = new Date().toISOString();
    backup.version = this.version;

    return JSON.stringify(backup, null, 2);
  }

  // استعادة النسخة الاحتياطية
  async restoreBackup(backupData: string): Promise<void> {
    try {
      const backup = JSON.parse(backupData);
      const storeNames = ['users', 'properties', 'invoices', 'procurement', 'suppliers', 'sales', 'maintenance'];

      for (const storeName of storeNames) {
        if (backup[storeName] && Array.isArray(backup[storeName])) {
          // مسح البيانات الحالية
          await this.clearStore(storeName);

          // إضافة البيانات المستعادة
          for (const item of backup[storeName]) {
            try {
              await this.add(storeName, item);
            } catch (error) {
              // في حالة وجود مفتاح مكرر، نحاول التحديث
              try {
                await this.update(storeName, item);
              } catch (updateError) {
                console.error(`خطأ في استعادة عنصر من ${storeName}:`, updateError);
              }
            }
          }
        }
      }
    } catch (error) {
      throw new Error('فشل في استعادة النسخة الاحتياطية: ' + error);
    }
  }

  // تنزيل النسخة الاحتياطية كملف
  async downloadBackup(): Promise<void> {
    try {
      const backupData = await this.createBackup();
      const blob = new Blob([backupData], { type: 'application/json' });
      const url = URL.createObjectURL(blob);

      const link = document.createElement('a');
      link.href = url;
      link.download = `rafea-backup-${new Date().toISOString().split('T')[0]}.json`;
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);

      URL.revokeObjectURL(url);
    } catch (error) {
      throw new Error('فشل في تنزيل النسخة الاحتياطية: ' + error);
    }
  }

  // رفع واستعادة النسخة الاحتياطية من ملف
  async uploadAndRestoreBackup(file: File): Promise<void> {
    return new Promise((resolve, reject) => {
      const reader = new FileReader();

      reader.onload = async (event) => {
        try {
          const backupData = event.target?.result as string;
          await this.restoreBackup(backupData);
          resolve();
        } catch (error) {
          reject(error);
        }
      };

      reader.onerror = () => {
        reject(new Error('فشل في قراءة الملف'));
      };

      reader.readAsText(file);
    });
  }
}

// إنشاء مثيل واحد من قاعدة البيانات
export const database = new RafeaDatabase();

// أنواع البيانات
export interface User {
  id?: number;
  name: string;
  email: string;
  role: string;
  department: string;
  status: string;
  lastActive: string;
  createdAt?: string;
}

export interface Property {
  id: string;
  title: string;
  type: string;
  location: string;
  area: number;
  price: string;
  bedrooms: number | null;
  bathrooms: number | null;
  status: string;
  createdAt?: string;
}

export interface Invoice {
  id?: number;
  contractor: string;
  project: string;
  amount: number;
  date: string;
  status: string;
  createdAt?: string;
}

export interface ProcurementItem {
  id?: number;
  title: string;
  category: string;
  supplier: string;
  amount: string;
  date: string;
  description: string;
  status: string;
  createdAt?: string;
}

export interface Supplier {
  id?: number;
  name: string;
  category: string;
  contact: string;
  email: string;
  address: string;
  status: string;
  createdAt?: string;
}

export interface Sale {
  id?: number;
  propertyId: string;
  customerName: string;
  customerPhone: string;
  customerEmail: string;
  salePrice: number;
  saleDate: string;
  status: string;
  createdAt?: string;
}

export interface MaintenanceRequest {
  id?: number;
  propertyId: string;
  title: string;
  description: string;
  priority: string;
  assignedTo: string;
  status: string;
  requestDate: string;
  completionDate?: string;
  createdAt?: string;
}
