<!DOCTYPE html>
<html lang="ar">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>نظام إدارة العقارات - حفظ في الملفات</title>
    <link href="https://fonts.googleapis.com/css2?family=Tajawal:wght@300;400;500;700&display=swap" rel="stylesheet">
    <style>
        * { margin: 0; padding: 0; box-sizing: border-box; }
        body { font-family: '<PERSON><PERSON><PERSON>', sans-serif; direction: rtl; background: #f5f5f5; color: #333; }
        .header { background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; padding: 2rem; text-align: center; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }
        .header h1 { font-size: 2.5rem; margin-bottom: 0.5rem; }
        .header p { font-size: 1.2rem; opacity: 0.9; }

        .storage-info { background: rgba(255,255,255,0.95); margin: 1rem; padding: 1.5rem; border-radius: 12px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }
        .storage-mode { display: flex; gap: 1rem; margin-bottom: 1rem; flex-wrap: wrap; }
        .mode-btn { padding: 0.8rem 1.5rem; border: 2px solid #667eea; background: white; color: #667eea; border-radius: 8px; cursor: pointer; transition: all 0.3s; font-family: inherit; }
        .mode-btn.active { background: #667eea; color: white; }
        .mode-btn:hover { background: #5a67d8; color: white; border-color: #5a67d8; }

        .storage-status { padding: 1rem; border-radius: 8px; margin-bottom: 1rem; }
        .status-success { background: #c6f6d5; color: #22543d; border: 1px solid #9ae6b4; }
        .status-warning { background: #fef5e7; color: #744210; border: 1px solid #f6e05e; }
        .status-error { background: #fed7d7; color: #742a2a; border: 1px solid #fc8181; }

        .nav-tabs { background: white; padding: 1rem; box-shadow: 0 2px 5px rgba(0,0,0,0.1); display: flex; gap: 1rem; overflow-x: auto; }
        .nav-tab { padding: 0.8rem 1.5rem; background: #f8f9fa; border: none; border-radius: 8px; cursor: pointer; transition: all 0.3s; white-space: nowrap; font-family: inherit; font-size: 1rem; }
        .nav-tab.active { background: #667eea; color: white; }
        .nav-tab:hover { background: #5a67d8; color: white; }

        .content { padding: 2rem; max-width: 1200px; margin: 0 auto; }
        .page { display: none; animation: fadeIn 0.3s ease-in; }
        .page.active { display: block; }
        @keyframes fadeIn { from { opacity: 0; transform: translateY(20px); } to { opacity: 1; transform: translateY(0); } }

        .stats-grid { display: grid; grid-template-columns: repeat(auto-fit, minmax(250px, 1fr)); gap: 1.5rem; margin-bottom: 2rem; }
        .stat-card { background: white; padding: 1.5rem; border-radius: 12px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); text-align: center; }
        .stat-card h3 { color: #667eea; font-size: 2rem; margin-bottom: 0.5rem; }
        .stat-card p { color: #666; font-size: 1rem; }

        .form-container { background: white; padding: 2rem; border-radius: 12px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); margin-bottom: 2rem; }
        .form-grid { display: grid; grid-template-columns: repeat(auto-fit, minmax(250px, 1fr)); gap: 1rem; margin-bottom: 1.5rem; }
        .form-group { display: flex; flex-direction: column; }
        .form-group label { margin-bottom: 0.5rem; font-weight: 500; color: #333; }
        .form-group input, .form-group select, .form-group textarea { padding: 0.8rem; border: 2px solid #e2e8f0; border-radius: 8px; font-family: inherit; font-size: 1rem; transition: border-color 0.3s; }
        .form-group input:focus, .form-group select:focus, .form-group textarea:focus { outline: none; border-color: #667eea; }

        .btn { padding: 0.8rem 1.5rem; border: none; border-radius: 8px; cursor: pointer; font-family: inherit; font-size: 1rem; transition: all 0.3s; }
        .btn-primary { background: #667eea; color: white; }
        .btn-primary:hover { background: #5a67d8; }
        .btn-success { background: #48bb78; color: white; }
        .btn-success:hover { background: #38a169; }
        .btn-danger { background: #e53e3e; color: white; }
        .btn-danger:hover { background: #c53030; }
        .btn-secondary { background: #718096; color: white; }
        .btn-secondary:hover { background: #4a5568; }

        .table-container { background: white; border-radius: 12px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); overflow: hidden; }
        .table { width: 100%; border-collapse: collapse; }
        .table th, .table td { padding: 1rem; text-align: right; border-bottom: 1px solid #e2e8f0; }
        .table th { background: #f8f9fa; font-weight: 600; color: #4a5568; }
        .table tr:hover { background: #f8f9fa; }

        .badge { padding: 0.3rem 0.8rem; border-radius: 20px; font-size: 0.8rem; font-weight: 500; }
        .badge-success { background: #c6f6d5; color: #22543d; }
        .badge-warning { background: #fef5e7; color: #744210; }
        .badge-danger { background: #fed7d7; color: #742a2a; }

        .alert { padding: 1rem; border-radius: 8px; margin-bottom: 1rem; }
        .alert-success { background: #c6f6d5; color: #22543d; border: 1px solid #9ae6b4; }
        .alert-info { background: #bee3f8; color: #2a4365; border: 1px solid #90cdf4; }
        .alert-warning { background: #fef5e7; color: #744210; border: 1px solid #f6e05e; }

        .file-drop-zone { border: 2px dashed #667eea; border-radius: 12px; padding: 2rem; text-align: center; margin: 1rem 0; transition: all 0.3s; cursor: pointer; }
        .file-drop-zone:hover { background: rgba(102, 126, 234, 0.05); }
        .file-drop-zone.dragover { background: rgba(102, 126, 234, 0.1); border-color: #5a67d8; }

        @media (max-width: 768px) {
            .header h1 { font-size: 2rem; }
            .nav-tabs { padding: 0.5rem; }
            .content { padding: 1rem; }
            .form-grid { grid-template-columns: 1fr; }
            .storage-mode { flex-direction: column; }
        }
    </style>
</head>
<body>
    <div class="header">
        <h1>🏢 نظام إدارة العقارات</h1>
        <p>شركة رافعة المدينة - نسخة الحفظ في الملفات</p>
    </div>

    <!-- Storage Mode Selection -->
    <div class="storage-info">
        <h3>📁 طريقة حفظ البيانات</h3>
        <div class="storage-mode">
            <button class="mode-btn active" onclick="setStorageMode('hybrid')" id="mode-hybrid">
                🔄 مختلط (ملف + متصفح)
            </button>
            <button class="mode-btn" onclick="setStorageMode('file')" id="mode-file">
                📁 ملف محلي فقط
            </button>
            <button class="mode-btn" onclick="setStorageMode('browser')" id="mode-browser">
                🌐 متصفح فقط
            </button>
        </div>
        <div id="storage-status" class="storage-status status-success">
            <strong>✅ الوضع النشط:</strong> حفظ مختلط - البيانات محفوظة في الملف والمتصفح معاً
        </div>
    </div>

    <div class="nav-tabs">
        <button class="nav-tab active" onclick="showPage('dashboard')">🏠 الرئيسية</button>
        <button class="nav-tab" onclick="showPage('properties')">🏢 العقارات</button>
        <button class="nav-tab" onclick="showPage('users')">👥 المستخدمين</button>
        <button class="nav-tab" onclick="showPage('invoices')">📄 المستخلصات</button>
        <button class="nav-tab" onclick="showPage('storage')">💾 إدارة التخزين</button>
    </div>

    <div class="content">
        <!-- Dashboard -->
        <div id="dashboard" class="page active">
            <div class="alert alert-success">
                <strong>مرحباً بك!</strong> نظام إدارة العقارات مع دعم الحفظ في الملفات المحلية.
            </div>
            <div class="stats-grid">
                <div class="stat-card"><h3 id="total-properties">0</h3><p>إجمالي العقارات</p></div>
                <div class="stat-card"><h3 id="total-users">0</h3><p>المستخدمين</p></div>
                <div class="stat-card"><h3 id="total-invoices">0</h3><p>المستخلصات</p></div>
                <div class="stat-card"><h3 id="file-size">0 KB</h3><p>حجم ملف البيانات</p></div>
            </div>
        </div>

        <!-- Properties -->
        <div id="properties" class="page">
            <h2>إدارة العقارات</h2>
            <div class="form-container">
                <h3>إضافة عقار جديد</h3>
                <form id="property-form">
                    <div class="form-grid">
                        <div class="form-group"><label>عنوان العقار *</label><input type="text" id="property-title" required></div>
                        <div class="form-group"><label>نوع العقار</label><select id="property-type"><option value="شقة">شقة</option><option value="فيلا">فيلا</option><option value="محل تجاري">محل تجاري</option><option value="مكتب">مكتب</option><option value="أرض">أرض</option></select></div>
                        <div class="form-group"><label>الموقع</label><input type="text" id="property-location"></div>
                        <div class="form-group"><label>المساحة (م²)</label><input type="number" id="property-area"></div>
                        <div class="form-group"><label>السعر (ريال)</label><input type="number" id="property-price"></div>
                        <div class="form-group"><label>الحالة</label><select id="property-status"><option value="متاح">متاح</option><option value="محجوز">محجوز</option><option value="مباع">مباع</option></select></div>
                    </div>
                    <div class="form-group"><label>الوصف</label><textarea id="property-description" rows="3"></textarea></div>
                    <button type="submit" class="btn btn-primary">إضافة العقار</button>
                </form>
            </div>
            <div class="table-container">
                <table class="table">
                    <thead><tr><th>العنوان</th><th>النوع</th><th>الموقع</th><th>المساحة</th><th>السعر</th><th>الحالة</th><th>الإجراءات</th></tr></thead>
                    <tbody id="properties-table"></tbody>
                </table>
            </div>
        </div>

        <!-- Users -->
        <div id="users" class="page">
            <h2>إدارة المستخدمين</h2>
            <div class="form-container">
                <h3>إضافة مستخدم جديد</h3>
                <form id="user-form">
                    <div class="form-grid">
                        <div class="form-group"><label>الاسم *</label><input type="text" id="user-name" required></div>
                        <div class="form-group"><label>البريد الإلكتروني *</label><input type="email" id="user-email" required></div>
                        <div class="form-group"><label>رقم الهاتف</label><input type="tel" id="user-phone"></div>
                        <div class="form-group"><label>الدور الوظيفي</label><select id="user-role"><option value="مدير">مدير</option><option value="موظف مبيعات">موظف مبيعات</option><option value="محاسب">محاسب</option><option value="مشرف">مشرف</option></select></div>
                        <div class="form-group"><label>القسم</label><input type="text" id="user-department"></div>
                        <div class="form-group"><label>الحالة</label><select id="user-status"><option value="نشط">نشط</option><option value="غير نشط">غير نشط</option></select></div>
                    </div>
                    <button type="submit" class="btn btn-primary">إضافة المستخدم</button>
                </form>
            </div>
            <div class="table-container">
                <table class="table">
                    <thead><tr><th>الاسم</th><th>البريد الإلكتروني</th><th>الهاتف</th><th>الدور</th><th>القسم</th><th>الحالة</th><th>الإجراءات</th></tr></thead>
                    <tbody id="users-table"></tbody>
                </table>
            </div>
        </div>

        <!-- Invoices -->
        <div id="invoices" class="page">
            <h2>إدارة المستخلصات</h2>
            <div class="form-container">
                <h3>إضافة مستخلص جديد</h3>
                <form id="invoice-form">
                    <div class="form-grid">
                        <div class="form-group"><label>اسم العميل *</label><input type="text" id="invoice-customer" required></div>
                        <div class="form-group"><label>هاتف العميل</label><input type="tel" id="invoice-phone"></div>
                        <div class="form-group"><label>العقار</label><input type="text" id="invoice-property"></div>
                        <div class="form-group"><label>المبلغ (ريال) *</label><input type="number" id="invoice-amount" required></div>
                        <div class="form-group"><label>تاريخ الاستحقاق</label><input type="date" id="invoice-due-date"></div>
                        <div class="form-group"><label>الحالة</label><select id="invoice-status"><option value="معلق">معلق</option><option value="مدفوع">مدفوع</option><option value="مرفوض">مرفوض</option></select></div>
                    </div>
                    <button type="submit" class="btn btn-primary">إضافة المستخلص</button>
                </form>
            </div>
            <div class="table-container">
                <table class="table">
                    <thead><tr><th>رقم المستخلص</th><th>العميل</th><th>العقار</th><th>المبلغ</th><th>تاريخ الاستحقاق</th><th>الحالة</th><th>الإجراءات</th></tr></thead>
                    <tbody id="invoices-table"></tbody>
                </table>
            </div>
        </div>

        <!-- Storage Management -->
        <div id="storage" class="page">
            <h2>💾 إدارة التخزين</h2>

            <div class="form-container">
                <h3>📁 حالة ملف البيانات</h3>
                <div id="file-status" class="alert alert-info">
                    <strong>📄 ملف البيانات:</strong> rafea-data.json<br>
                    <strong>📊 الحجم:</strong> <span id="current-file-size">0 KB</span><br>
                    <strong>🕒 آخر تحديث:</strong> <span id="last-save-time">لم يتم الحفظ بعد</span><br>
                    <strong>📍 المكان:</strong> مجلد التطبيق (نفس مكان الملف HTML)
                </div>

                <div class="form-grid">
                    <div class="form-group">
                        <label>حفظ يدوي للملف</label>
                        <button type="button" class="btn btn-primary" onclick="saveToFile()">
                            💾 حفظ البيانات في ملف
                        </button>
                    </div>
                    <div class="form-group">
                        <label>تحميل من ملف</label>
                        <input type="file" id="load-file" accept=".json" style="margin-bottom: 0.5rem;">
                        <button type="button" class="btn btn-success" onclick="loadFromFile()">
                            📂 تحميل البيانات من ملف
                        </button>
                    </div>
                </div>
            </div>

            <div class="form-container">
                <h3>🔄 مزامنة البيانات</h3>
                <div class="form-grid">
                    <div class="form-group">
                        <label>مزامنة تلقائية</label>
                        <label style="display: flex; align-items: center; gap: 0.5rem;">
                            <input type="checkbox" id="auto-sync" onchange="toggleAutoSync()" checked>
                            <span>تفعيل الحفظ التلقائي كل 30 ثانية</span>
                        </label>
                    </div>
                    <div class="form-group">
                        <label>مزامنة فورية</label>
                        <button type="button" class="btn btn-secondary" onclick="syncNow()">
                            🔄 مزامنة الآن
                        </button>
                    </div>
                </div>
                <div id="sync-status" class="alert alert-success" style="margin-top: 1rem;">
                    <strong>✅ حالة المزامنة:</strong> تعمل بشكل طبيعي
                </div>
            </div>

            <div class="form-container">
                <h3>📤 تصدير واستيراد</h3>
                <div class="file-drop-zone" onclick="document.getElementById('import-file').click()">
                    <h4>📁 اسحب ملف JSON هنا أو انقر للاختيار</h4>
                    <p>يدعم ملفات .json فقط</p>
                    <input type="file" id="import-file" accept=".json" style="display: none;" onchange="importFromFile()">
                </div>

                <div class="form-grid" style="margin-top: 1rem;">
                    <div class="form-group">
                        <label>تصدير نسخة احتياطية</label>
                        <button type="button" class="btn btn-primary" onclick="exportBackup()">
                            📥 تصدير نسخة احتياطية
                        </button>
                    </div>
                    <div class="form-group">
                        <label>مسح جميع البيانات</label>
                        <button type="button" class="btn btn-danger" onclick="clearAllData()">
                            🗑️ مسح البيانات
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        // Storage Configuration
        let storageMode = 'hybrid'; // hybrid, file, browser
        let autoSyncEnabled = true;
        let autoSyncInterval;

        // Data Storage
        let properties = [];
        let users = [];
        let invoices = [];

        // File Handle for File System Access API
        let fileHandle = null;

        // Storage Management Functions
        function initializeStorage() {
            // Check if File System Access API is supported
            if ('showSaveFilePicker' in window) {
                console.log('File System Access API supported');
            } else {
                console.log('File System Access API not supported, using fallback');
            }

            // Load storage mode from localStorage
            const savedMode = localStorage.getItem('rafea_storage_mode');
            if (savedMode) {
                setStorageMode(savedMode);
            }
        }

        function setStorageMode(mode) {
            storageMode = mode;
            localStorage.setItem('rafea_storage_mode', mode);

            // Update UI
            document.querySelectorAll('.mode-btn').forEach(btn => btn.classList.remove('active'));
            document.getElementById(`mode-${mode}`).classList.add('active');

            // Update status
            const statusDiv = document.getElementById('storage-status');
            switch(mode) {
                case 'hybrid':
                    statusDiv.className = 'storage-status status-success';
                    statusDiv.innerHTML = '<strong>✅ الوضع النشط:</strong> حفظ مختلط - البيانات محفوظة في الملف والمتصفح معاً';
                    break;
                case 'file':
                    statusDiv.className = 'storage-status status-warning';
                    statusDiv.innerHTML = '<strong>📁 الوضع النشط:</strong> ملف محلي فقط - البيانات محفوظة في ملف JSON';
                    break;
                case 'browser':
                    statusDiv.className = 'storage-status status-info';
                    statusDiv.innerHTML = '<strong>🌐 الوضع النشط:</strong> متصفح فقط - البيانات محفوظة في localStorage';
                    break;
            }

            showNotification(`تم تغيير وضع التخزين إلى: ${getStorageModeName(mode)}`, 'success');
        }

        function getStorageModeName(mode) {
            const names = {
                'hybrid': 'مختلط (ملف + متصفح)',
                'file': 'ملف محلي فقط',
                'browser': 'متصفح فقط'
            };
            return names[mode] || mode;
        }

        // Data Management Functions
        function saveData() {
            const data = {
                properties: properties,
                users: users,
                invoices: invoices,
                lastSaved: new Date().toISOString(),
                version: '1.0.0',
                storageMode: storageMode
            };

            if (storageMode === 'browser') {
                // Save to localStorage only
                localStorage.setItem('rafea_properties', JSON.stringify(properties));
                localStorage.setItem('rafea_users', JSON.stringify(users));
                localStorage.setItem('rafea_invoices', JSON.stringify(invoices));
            } else if (storageMode === 'file') {
                // Save to file only
                saveToFileSystem(data);
            } else {
                // Hybrid: save to both
                localStorage.setItem('rafea_properties', JSON.stringify(properties));
                localStorage.setItem('rafea_users', JSON.stringify(users));
                localStorage.setItem('rafea_invoices', JSON.stringify(invoices));
                saveToFileSystem(data);
            }

            updateFileStatus();
        }

        function loadAllData() {
            if (storageMode === 'file') {
                // Try to load from file first
                loadFromFileSystem();
            } else {
                // Load from localStorage
                properties = JSON.parse(localStorage.getItem('rafea_properties') || '[]');
                users = JSON.parse(localStorage.getItem('rafea_users') || '[]');
                invoices = JSON.parse(localStorage.getItem('rafea_invoices') || '[]');
            }

            updateDashboard();
            updateFileStatus();
        }

        async function saveToFileSystem(data) {
            try {
                if ('showSaveFilePicker' in window) {
                    // Use File System Access API
                    if (!fileHandle) {
                        fileHandle = await window.showSaveFilePicker({
                            suggestedName: 'rafea-data.json',
                            types: [{
                                description: 'JSON files',
                                accept: { 'application/json': ['.json'] }
                            }]
                        });
                    }

                    const writable = await fileHandle.createWritable();
                    await writable.write(JSON.stringify(data, null, 2));
                    await writable.close();

                    showNotification('تم حفظ البيانات في الملف بنجاح! 💾', 'success');
                } else {
                    // Fallback: download file
                    downloadFile(data, 'rafea-data.json');
                }
            } catch (error) {
                console.error('Error saving to file:', error);
                showNotification('خطأ في حفظ الملف: ' + error.message, 'error');
            }
        }

        function downloadFile(data, filename) {
            const dataStr = JSON.stringify(data, null, 2);
            const dataBlob = new Blob([dataStr], {type: 'application/json'});
            const url = URL.createObjectURL(dataBlob);
            const link = document.createElement('a');
            link.href = url;
            link.download = filename;
            link.click();
            URL.revokeObjectURL(url);
            showNotification('تم تحميل ملف البيانات! 📥', 'success');
        }

        async function loadFromFileSystem() {
            try {
                if ('showOpenFilePicker' in window) {
                    const [fileHandle] = await window.showOpenFilePicker({
                        types: [{
                            description: 'JSON files',
                            accept: { 'application/json': ['.json'] }
                        }]
                    });

                    const file = await fileHandle.getFile();
                    const contents = await file.text();
                    const data = JSON.parse(contents);

                    properties = data.properties || [];
                    users = data.users || [];
                    invoices = data.invoices || [];

                    updateDashboard();
                    showNotification('تم تحميل البيانات من الملف بنجاح! 📂', 'success');
                } else {
                    showNotification('يرجى استخدام زر "تحميل من ملف" في قسم إدارة التخزين', 'warning');
                }
            } catch (error) {
                console.error('Error loading from file:', error);
                if (error.name !== 'AbortError') {
                    showNotification('خطأ في تحميل الملف: ' + error.message, 'error');
                }
            }
        }

        function saveToFile() {
            const data = {
                properties: properties,
                users: users,
                invoices: invoices,
                lastSaved: new Date().toISOString(),
                version: '1.0.0',
                storageMode: storageMode
            };

            saveToFileSystem(data);
        }

        function loadFromFile() {
            const fileInput = document.getElementById('load-file');
            const file = fileInput.files[0];

            if (!file) {
                showNotification('يرجى اختيار ملف للتحميل', 'warning');
                return;
            }

            const reader = new FileReader();
            reader.onload = function(e) {
                try {
                    const data = JSON.parse(e.target.result);

                    if (confirm('هل أنت متأكد من تحميل البيانات؟ سيتم استبدال البيانات الحالية.')) {
                        properties = data.properties || [];
                        users = data.users || [];
                        invoices = data.invoices || [];

                        saveData(); // Save to current storage mode
                        updateDashboard();
                        loadPageData('dashboard');

                        showNotification('تم تحميل البيانات بنجاح! 📂', 'success');
                    }
                } catch (error) {
                    showNotification('خطأ في قراءة الملف: ' + error.message, 'error');
                }
            };
            reader.readAsText(file);
        }

        function importFromFile() {
            const fileInput = document.getElementById('import-file');
            const file = fileInput.files[0];

            if (file) {
                loadFromFile();
            }
        }

        function exportBackup() {
            const data = {
                properties: properties,
                users: users,
                invoices: invoices,
                exportDate: new Date().toISOString(),
                version: '1.0.0',
                storageMode: storageMode,
                metadata: {
                    totalRecords: properties.length + users.length + invoices.length,
                    createdBy: 'نظام إدارة العقارات - رافعة المدينة'
                }
            };

            downloadFile(data, `rafea-backup-${new Date().toISOString().split('T')[0]}.json`);
        }

        function syncNow() {
            saveData();
            showNotification('تمت المزامنة بنجاح! 🔄', 'success');
            updateSyncStatus('تمت المزامنة للتو');
        }

        function toggleAutoSync() {
            autoSyncEnabled = document.getElementById('auto-sync').checked;

            if (autoSyncEnabled) {
                startAutoSync();
                showNotification('تم تفعيل الحفظ التلقائي', 'success');
            } else {
                stopAutoSync();
                showNotification('تم إيقاف الحفظ التلقائي', 'warning');
            }
        }

        function startAutoSync() {
            if (autoSyncInterval) {
                clearInterval(autoSyncInterval);
            }

            autoSyncInterval = setInterval(() => {
                if (autoSyncEnabled) {
                    saveData();
                    updateSyncStatus('آخر حفظ تلقائي: ' + new Date().toLocaleTimeString('ar-SA'));
                }
            }, 30000); // Every 30 seconds
        }

        function stopAutoSync() {
            if (autoSyncInterval) {
                clearInterval(autoSyncInterval);
                autoSyncInterval = null;
            }
        }

        function updateSyncStatus(message) {
            const statusDiv = document.getElementById('sync-status');
            statusDiv.innerHTML = `<strong>🔄 حالة المزامنة:</strong> ${message}`;
        }

        function updateFileStatus() {
            const data = {
                properties: properties,
                users: users,
                invoices: invoices
            };

            const dataStr = JSON.stringify(data);
            const sizeInBytes = new Blob([dataStr]).size;
            const sizeFormatted = formatFileSize(sizeInBytes);

            document.getElementById('current-file-size').textContent = sizeFormatted;
            document.getElementById('file-size').textContent = sizeFormatted;
            document.getElementById('last-save-time').textContent = new Date().toLocaleString('ar-SA');
        }

        function formatFileSize(bytes) {
            if (bytes === 0) return '0 B';
            const k = 1024;
            const sizes = ['B', 'KB', 'MB', 'GB'];
            const i = Math.floor(Math.log(bytes) / Math.log(k));
            return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
        }

        function clearAllData() {
            const confirmMessage = `⚠️ تحذير: مسح جميع البيانات\n\nهذا الإجراء سيحذف:\n• جميع العقارات (${properties.length})\n• جميع المستخدمين (${users.length})\n• جميع المستخلصات (${invoices.length})\n\n❌ لا يمكن التراجع عن هذا الإجراء!\n\nهل أنت متأكد من المتابعة؟`;

            if (confirm(confirmMessage)) {
                properties = [];
                users = [];
                invoices = [];

                if (storageMode !== 'file') {
                    localStorage.clear();
                }

                saveData();
                updateDashboard();
                showNotification('تم مسح جميع البيانات 🗑️', 'success');
            }
        }

        // Navigation Functions
        function showPage(pageId) {
            document.querySelectorAll('.page').forEach(page => page.classList.remove('active'));
            document.querySelectorAll('.nav-tab').forEach(tab => tab.classList.remove('active'));
            document.getElementById(pageId).classList.add('active');
            event.target.classList.add('active');
            loadPageData(pageId);
        }

        function loadPageData(pageId) {
            switch(pageId) {
                case 'dashboard': updateDashboard(); break;
                case 'properties': loadProperties(); break;
                case 'users': loadUsers(); break;
                case 'invoices': loadInvoices(); break;
                case 'storage': updateFileStatus(); break;
            }
        }

        // Dashboard Functions
        function updateDashboard() {
            document.getElementById('total-properties').textContent = properties.length;
            document.getElementById('total-users').textContent = users.length;
            document.getElementById('total-invoices').textContent = invoices.length;
            updateFileStatus();
        }

        // Properties Functions
        function loadProperties() {
            const tbody = document.getElementById('properties-table');
            tbody.innerHTML = properties.map(property => `
                <tr>
                    <td>${property.title}</td>
                    <td>${property.type}</td>
                    <td>${property.location || 'غير محدد'}</td>
                    <td>${property.area || 'غير محدد'} م²</td>
                    <td>${property.price || 'غير محدد'} ريال</td>
                    <td><span class="badge badge-${property.status === 'متاح' ? 'success' : property.status === 'محجوز' ? 'warning' : 'danger'}">${property.status}</span></td>
                    <td><button class="btn btn-danger" onclick="deleteProperty('${property.id}')">حذف</button></td>
                </tr>
            `).join('');
        }

        document.getElementById('property-form').addEventListener('submit', function(e) {
            e.preventDefault();
            const property = {
                id: generateId(),
                title: document.getElementById('property-title').value,
                type: document.getElementById('property-type').value,
                location: document.getElementById('property-location').value,
                area: document.getElementById('property-area').value,
                price: document.getElementById('property-price').value,
                status: document.getElementById('property-status').value,
                description: document.getElementById('property-description').value,
                createdAt: new Date().toISOString()
            };
            properties.push(property);
            saveData();
            this.reset();
            loadProperties();
            showNotification('تم إضافة العقار بنجاح! 🏢', 'success');
        });

        function deleteProperty(id) {
            if (confirm('هل أنت متأكد من حذف هذا العقار؟')) {
                properties = properties.filter(p => p.id !== id);
                saveData();
                loadProperties();
                showNotification('تم حذف العقار بنجاح', 'success');
            }
        }

        // Users Functions
        function loadUsers() {
            const tbody = document.getElementById('users-table');
            tbody.innerHTML = users.map(user => `
                <tr>
                    <td>${user.name}</td>
                    <td>${user.email}</td>
                    <td>${user.phone || 'غير محدد'}</td>
                    <td>${user.role}</td>
                    <td>${user.department || 'غير محدد'}</td>
                    <td><span class="badge badge-${user.status === 'نشط' ? 'success' : 'danger'}">${user.status}</span></td>
                    <td><button class="btn btn-danger" onclick="deleteUser('${user.id}')">حذف</button></td>
                </tr>
            `).join('');
        }

        document.getElementById('user-form').addEventListener('submit', function(e) {
            e.preventDefault();
            const user = {
                id: generateId(),
                name: document.getElementById('user-name').value,
                email: document.getElementById('user-email').value,
                phone: document.getElementById('user-phone').value,
                role: document.getElementById('user-role').value,
                department: document.getElementById('user-department').value,
                status: document.getElementById('user-status').value,
                createdAt: new Date().toISOString()
            };
            users.push(user);
            saveData();
            this.reset();
            loadUsers();
            showNotification('تم إضافة المستخدم بنجاح! 👤', 'success');
        });

        function deleteUser(id) {
            if (confirm('هل أنت متأكد من حذف هذا المستخدم؟')) {
                users = users.filter(u => u.id !== id);
                saveData();
                loadUsers();
                showNotification('تم حذف المستخدم بنجاح', 'success');
            }
        }

        // Invoices Functions
        function loadInvoices() {
            const tbody = document.getElementById('invoices-table');
            tbody.innerHTML = invoices.map(invoice => `
                <tr>
                    <td>#${invoice.id}</td>
                    <td>${invoice.customer}</td>
                    <td>${invoice.property || 'غير محدد'}</td>
                    <td>${invoice.amount} ريال</td>
                    <td>${invoice.dueDate || 'غير محدد'}</td>
                    <td><span class="badge badge-${invoice.status === 'مدفوع' ? 'success' : invoice.status === 'معلق' ? 'warning' : 'danger'}">${invoice.status}</span></td>
                    <td><button class="btn btn-danger" onclick="deleteInvoice('${invoice.id}')">حذف</button></td>
                </tr>
            `).join('');
        }

        document.getElementById('invoice-form').addEventListener('submit', function(e) {
            e.preventDefault();
            const invoice = {
                id: generateId(),
                customer: document.getElementById('invoice-customer').value,
                phone: document.getElementById('invoice-phone').value,
                property: document.getElementById('invoice-property').value,
                amount: document.getElementById('invoice-amount').value,
                dueDate: document.getElementById('invoice-due-date').value,
                status: document.getElementById('invoice-status').value,
                createdAt: new Date().toISOString()
            };
            invoices.push(invoice);
            saveData();
            this.reset();
            loadInvoices();
            showNotification('تم إضافة المستخلص بنجاح! 📄', 'success');
        });

        function deleteInvoice(id) {
            if (confirm('هل أنت متأكد من حذف هذا المستخلص؟')) {
                invoices = invoices.filter(i => i.id !== id);
                saveData();
                loadInvoices();
                showNotification('تم حذف المستخلص بنجاح', 'success');
            }
        }

        // Utility Functions
        function generateId() {
            return Date.now().toString() + Math.random().toString(36).substr(2, 9);
        }

        function showNotification(message, type = 'success') {
            const notification = document.createElement('div');
            notification.className = `alert alert-${type}`;
            notification.textContent = message;
            notification.style.position = 'fixed';
            notification.style.top = '20px';
            notification.style.right = '20px';
            notification.style.zIndex = '1000';
            notification.style.minWidth = '300px';
            notification.style.borderRadius = '8px';
            notification.style.boxShadow = '0 4px 12px rgba(0,0,0,0.15)';
            document.body.appendChild(notification);
            setTimeout(() => notification.remove(), 4000);
        }

        // Drag and Drop for File Import
        const dropZone = document.querySelector('.file-drop-zone');

        dropZone.addEventListener('dragover', function(e) {
            e.preventDefault();
            this.classList.add('dragover');
        });

        dropZone.addEventListener('dragleave', function(e) {
            e.preventDefault();
            this.classList.remove('dragover');
        });

        dropZone.addEventListener('drop', function(e) {
            e.preventDefault();
            this.classList.remove('dragover');

            const files = e.dataTransfer.files;
            if (files.length > 0) {
                const file = files[0];
                if (file.type === 'application/json') {
                    const reader = new FileReader();
                    reader.onload = function(e) {
                        try {
                            const data = JSON.parse(e.target.result);

                            if (confirm('هل أنت متأكد من استيراد البيانات؟ سيتم استبدال البيانات الحالية.')) {
                                properties = data.properties || [];
                                users = data.users || [];
                                invoices = data.invoices || [];

                                saveData();
                                updateDashboard();
                                showNotification('تم استيراد البيانات بنجاح! 📂', 'success');
                            }
                        } catch (error) {
                            showNotification('خطأ في قراءة الملف: ' + error.message, 'error');
                        }
                    };
                    reader.readAsText(file);
                } else {
                    showNotification('يرجى اختيار ملف JSON فقط', 'warning');
                }
            }
        });

        // Initialize
        document.addEventListener('DOMContentLoaded', function() {
            initializeStorage();
            loadAllData();
            startAutoSync();
            showNotification('مرحباً بك في نظام إدارة العقارات!', 'success');
        });
    </script>
</body>
</html>
