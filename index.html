
<!DOCTYPE html>
<html lang="ar">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>نظام إدارة العقارات - رافعة المدينة</title>
    <meta name="description" content="نظام شامل لإدارة العقارات والمبيعات والمستخلصات والصيانة - شركة رافعة المدينة للتطوير العقاري" />
    <meta name="author" content="Rafea Madinah Real Estate Development" />
    <meta name="keywords" content="إدارة عقارات, مبيعات, مستخلصات, صيانة, رافعة المدينة, المدينة المنورة" />
    <meta name="robots" content="index, follow" />
    <meta name="language" content="Arabic" />

    <!-- PWA Meta Tags -->
    <meta name="theme-color" content="#667eea" />
    <meta name="apple-mobile-web-app-capable" content="yes" />
    <meta name="apple-mobile-web-app-status-bar-style" content="default" />
    <meta name="apple-mobile-web-app-title" content="رافعة المدينة" />
    <meta name="mobile-web-app-capable" content="yes" />
    <meta name="application-name" content="رافعة المدينة" />

    <!-- Manifest -->
    <link rel="manifest" href="/manifest.json" />

    <!-- Icons -->
    <link rel="icon" type="image/x-icon" href="/favicon.ico" />
    <link rel="apple-touch-icon" href="/placeholder.svg" />

    <!-- Open Graph Meta Tags -->
    <meta property="og:title" content="نظام إدارة العقارات - رافعة المدينة" />
    <meta property="og:description" content="نظام شامل لإدارة العقارات والمبيعات والمستخلصات والصيانة" />
    <meta property="og:type" content="website" />
    <meta property="og:url" content="https://rafea-madinah.netlify.app" />
    <meta property="og:image" content="/placeholder.svg" />
    <meta property="og:locale" content="ar_SA" />
    <meta property="og:site_name" content="رافعة المدينة" />

    <!-- Twitter Meta Tags -->
    <meta name="twitter:card" content="summary_large_image" />
    <meta name="twitter:title" content="نظام إدارة العقارات - رافعة المدينة" />
    <meta name="twitter:description" content="نظام شامل لإدارة العقارات والمبيعات والمستخلصات والصيانة" />
    <meta name="twitter:image" content="/placeholder.svg" />

    <!-- Preconnect for Performance -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Tajawal:wght@300;400;500;700&display=swap" rel="stylesheet">

    <!-- Security Headers -->
    <meta http-equiv="X-Content-Type-Options" content="nosniff" />
    <meta http-equiv="X-Frame-Options" content="DENY" />
    <meta http-equiv="X-XSS-Protection" content="1; mode=block" />
  </head>

  <body>
    <!-- صفحة الترحيب والروابط -->
    <div id="welcome-page" style="display: block;">
      <div style="min-height: 100vh; background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); font-family: 'Tajawal', sans-serif; direction: rtl;">
        <div style="container: true; max-width: 1200px; margin: 0 auto; padding: 2rem;">
          <!-- Header -->
          <header style="text-align: center; padding: 3rem 0; color: white;">
            <h1 style="font-size: 3rem; font-weight: 700; margin-bottom: 1rem; text-shadow: 2px 2px 4px rgba(0,0,0,0.3);">
              شركة رافع للتطوير العقاري
            </h1>
            <p style="font-size: 1.5rem; font-weight: 300; opacity: 0.9;">
              فرع المدينة المنورة - نظام الإدارة المتكامل
            </p>
          </header>

          <!-- Navigation Cards -->
          <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(300px, 1fr)); gap: 2rem; margin-top: 3rem;">

            <!-- لوحة التحكم -->
            <div onclick="navigateTo('/')" style="background: white; border-radius: 15px; padding: 2rem; box-shadow: 0 10px 30px rgba(0,0,0,0.2); cursor: pointer; transition: transform 0.3s ease, box-shadow 0.3s ease;" onmouseover="this.style.transform='translateY(-5px)'; this.style.boxShadow='0 15px 40px rgba(0,0,0,0.3)'" onmouseout="this.style.transform='translateY(0)'; this.style.boxShadow='0 10px 30px rgba(0,0,0,0.2)'">
              <div style="text-align: center;">
                <div style="width: 80px; height: 80px; background: linear-gradient(135deg, #667eea, #764ba2); border-radius: 50%; margin: 0 auto 1.5rem; display: flex; align-items: center; justify-content: center;">
                  <svg width="40" height="40" fill="white" viewBox="0 0 24 24">
                    <path d="M3 13h8V3H3v10zm0 8h8v-6H3v6zm10 0h8V11h-8v10zm0-18v6h8V3h-8z"/>
                  </svg>
                </div>
                <h3 style="font-size: 1.5rem; font-weight: 600; color: #333; margin-bottom: 1rem;">لوحة التحكم</h3>
                <p style="color: #666; line-height: 1.6;">عرض شامل لجميع الإحصائيات والمؤشرات الرئيسية للشركة</p>
              </div>
            </div>

            <!-- إدارة المستخدمين -->
            <div onclick="navigateTo('/users')" style="background: white; border-radius: 15px; padding: 2rem; box-shadow: 0 10px 30px rgba(0,0,0,0.2); cursor: pointer; transition: transform 0.3s ease, box-shadow 0.3s ease;" onmouseover="this.style.transform='translateY(-5px)'; this.style.boxShadow='0 15px 40px rgba(0,0,0,0.3)'" onmouseout="this.style.transform='translateY(0)'; this.style.boxShadow='0 10px 30px rgba(0,0,0,0.2)'">
              <div style="text-align: center;">
                <div style="width: 80px; height: 80px; background: linear-gradient(135deg, #4facfe, #00f2fe); border-radius: 50%; margin: 0 auto 1.5rem; display: flex; align-items: center; justify-content: center;">
                  <svg width="40" height="40" fill="white" viewBox="0 0 24 24">
                    <path d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"/>
                  </svg>
                </div>
                <h3 style="font-size: 1.5rem; font-weight: 600; color: #333; margin-bottom: 1rem;">إدارة المستخدمين</h3>
                <p style="color: #666; line-height: 1.6;">إضافة وتعديل وإدارة حسابات المستخدمين والصلاحيات</p>
              </div>
            </div>

            <!-- إدارة العقارات -->
            <div onclick="navigateTo('/properties')" style="background: white; border-radius: 15px; padding: 2rem; box-shadow: 0 10px 30px rgba(0,0,0,0.2); cursor: pointer; transition: transform 0.3s ease, box-shadow 0.3s ease;" onmouseover="this.style.transform='translateY(-5px)'; this.style.boxShadow='0 15px 40px rgba(0,0,0,0.3)'" onmouseout="this.style.transform='translateY(0)'; this.style.boxShadow='0 10px 30px rgba(0,0,0,0.2)'">
              <div style="text-align: center;">
                <div style="width: 80px; height: 80px; background: linear-gradient(135deg, #fa709a, #fee140); border-radius: 50%; margin: 0 auto 1.5rem; display: flex; align-items: center; justify-content: center;">
                  <svg width="40" height="40" fill="white" viewBox="0 0 24 24">
                    <path d="M10.07 2.82l-.03-.03c-.59-.59-1.54-.59-2.12 0L2.69 8.02c-.78.78-.78 2.05 0 2.83L8.02 16.18c.78.78 2.05.78 2.83 0l5.33-5.33c.78-.78.78-2.05 0-2.83L10.85 2.69c-.2-.2-.42-.3-.66-.3-.24 0-.46.1-.66.3l-.46.46z"/>
                  </svg>
                </div>
                <h3 style="font-size: 1.5rem; font-weight: 600; color: #333; margin-bottom: 1rem;">إدارة العقارات</h3>
                <p style="color: #666; line-height: 1.6;">عرض وإدارة جميع العقارات والوحدات السكنية والتجارية</p>
              </div>
            </div>

            <!-- المبيعات -->
            <div onclick="navigateTo('/sales')" style="background: white; border-radius: 15px; padding: 2rem; box-shadow: 0 10px 30px rgba(0,0,0,0.2); cursor: pointer; transition: transform 0.3s ease, box-shadow 0.3s ease;" onmouseover="this.style.transform='translateY(-5px)'; this.style.boxShadow='0 15px 40px rgba(0,0,0,0.3)'" onmouseout="this.style.transform='translateY(0)'; this.style.boxShadow='0 10px 30px rgba(0,0,0,0.2)'">
              <div style="text-align: center;">
                <div style="width: 80px; height: 80px; background: linear-gradient(135deg, #a8edea, #fed6e3); border-radius: 50%; margin: 0 auto 1.5rem; display: flex; align-items: center; justify-content: center;">
                  <svg width="40" height="40" fill="white" viewBox="0 0 24 24">
                    <path d="M7 18c-1.1 0-2 .9-2 2s.9 2 2 2 2-.9 2-2-.9-2-2-2zM1 2v2h2l3.6 7.59-1.35 2.45c-.16.28-.25.61-.25.96 0 1.1.9 2 2 2h12v-2H7.42c-.14 0-.25-.11-.25-.25l.03-.12L8.1 13h7.45c.75 0 1.41-.41 1.75-1.03L21.7 4H5.21l-.94-2H1zm16 16c-1.1 0-2 .9-2 2s.9 2 2 2 2-.9 2-2-.9-2-2-2z"/>
                  </svg>
                </div>
                <h3 style="font-size: 1.5rem; font-weight: 600; color: #333; margin-bottom: 1rem;">المبيعات</h3>
                <p style="color: #666; line-height: 1.6;">متابعة عمليات البيع والعملاء والعقود</p>
              </div>
            </div>

            <!-- المشتريات -->
            <div onclick="navigateTo('/procurement')" style="background: white; border-radius: 15px; padding: 2rem; box-shadow: 0 10px 30px rgba(0,0,0,0.2); cursor: pointer; transition: transform 0.3s ease, box-shadow 0.3s ease;" onmouseover="this.style.transform='translateY(-5px)'; this.style.boxShadow='0 15px 40px rgba(0,0,0,0.3)'" onmouseout="this.style.transform='translateY(0)'; this.style.boxShadow='0 10px 30px rgba(0,0,0,0.2)'">
              <div style="text-align: center;">
                <div style="width: 80px; height: 80px; background: linear-gradient(135deg, #ffecd2, #fcb69f); border-radius: 50%; margin: 0 auto 1.5rem; display: flex; align-items: center; justify-content: center;">
                  <svg width="40" height="40" fill="white" viewBox="0 0 24 24">
                    <path d="M19 7h-3V6a4 4 0 0 0-8 0v1H5a1 1 0 0 0-1 1v11a3 3 0 0 0 3 3h10a3 3 0 0 0 3-3V8a1 1 0 0 0-1-1zM10 6a2 2 0 0 1 4 0v1h-4V6zm8 13a1 1 0 0 1-1 1H7a1 1 0 0 1-1-1V9h2v1a1 1 0 0 0 2 0V9h4v1a1 1 0 0 0 2 0V9h2v10z"/>
                  </svg>
                </div>
                <h3 style="font-size: 1.5rem; font-weight: 600; color: #333; margin-bottom: 1rem;">المشتريات</h3>
                <p style="color: #666; line-height: 1.6;">إدارة طلبات الشراء والموردين والمخزون</p>
              </div>
            </div>

            <!-- المستخلصات -->
            <div onclick="navigateTo('/invoices')" style="background: white; border-radius: 15px; padding: 2rem; box-shadow: 0 10px 30px rgba(0,0,0,0.2); cursor: pointer; transition: transform 0.3s ease, box-shadow 0.3s ease;" onmouseover="this.style.transform='translateY(-5px)'; this.style.boxShadow='0 15px 40px rgba(0,0,0,0.3)'" onmouseout="this.style.transform='translateY(0)'; this.style.boxShadow='0 10px 30px rgba(0,0,0,0.2)'">
              <div style="text-align: center;">
                <div style="width: 80px; height: 80px; background: linear-gradient(135deg, #ff9a9e, #fecfef); border-radius: 50%; margin: 0 auto 1.5rem; display: flex; align-items: center; justify-content: center;">
                  <svg width="40" height="40" fill="white" viewBox="0 0 24 24">
                    <path d="M14 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V8l-6-6z"/>
                    <path d="M14 2v6h6"/>
                    <path d="M16 13H8"/>
                    <path d="M16 17H8"/>
                    <path d="M10 9H8"/>
                  </svg>
                </div>
                <h3 style="font-size: 1.5rem; font-weight: 600; color: #333; margin-bottom: 1rem;">المستخلصات</h3>
                <p style="color: #666; line-height: 1.6;">إدارة المستخلصات المالية والفواتير</p>
              </div>
            </div>

            <!-- الصيانة -->
            <div onclick="navigateTo('/maintenance')" style="background: white; border-radius: 15px; padding: 2rem; box-shadow: 0 10px 30px rgba(0,0,0,0.2); cursor: pointer; transition: transform 0.3s ease, box-shadow 0.3s ease;" onmouseover="this.style.transform='translateY(-5px)'; this.style.boxShadow='0 15px 40px rgba(0,0,0,0.3)'" onmouseout="this.style.transform='translateY(0)'; this.style.boxShadow='0 10px 30px rgba(0,0,0,0.2)'">
              <div style="text-align: center;">
                <div style="width: 80px; height: 80px; background: linear-gradient(135deg, #a8caba, #5d4e75); border-radius: 50%; margin: 0 auto 1.5rem; display: flex; align-items: center; justify-content: center;">
                  <svg width="40" height="40" fill="white" viewBox="0 0 24 24">
                    <path d="M22.7 19l-9.1-9.1c.9-2.3.4-5-1.5-6.9-2-2-5-2.4-7.4-1.3L9 6 6 9 1.6 4.7C.4 7.1.9 10.1 2.9 12.1c1.9 1.9 4.6 2.4 6.9 1.5l9.1 9.1c.4.4 1 .4 1.4 0l2.3-2.3c.5-.4.5-1.1.1-1.4z"/>
                  </svg>
                </div>
                <h3 style="font-size: 1.5rem; font-weight: 600; color: #333; margin-bottom: 1rem;">الصيانة</h3>
                <p style="color: #666; line-height: 1.6;">متابعة طلبات الصيانة والإصلاحات</p>
              </div>
            </div>

            <!-- التقارير -->
            <div onclick="navigateTo('/reports')" style="background: white; border-radius: 15px; padding: 2rem; box-shadow: 0 10px 30px rgba(0,0,0,0.2); cursor: pointer; transition: transform 0.3s ease, box-shadow 0.3s ease;" onmouseover="this.style.transform='translateY(-5px)'; this.style.boxShadow='0 15px 40px rgba(0,0,0,0.3)'" onmouseout="this.style.transform='translateY(0)'; this.style.boxShadow='0 10px 30px rgba(0,0,0,0.2)'">
              <div style="text-align: center;">
                <div style="width: 80px; height: 80px; background: linear-gradient(135deg, #667eea, #764ba2); border-radius: 50%; margin: 0 auto 1.5rem; display: flex; align-items: center; justify-content: center;">
                  <svg width="40" height="40" fill="white" viewBox="0 0 24 24">
                    <path d="M3 3v18h18v-2H5V3H3z"/>
                    <path d="M7 12l4-4 4 4 5-5 1.5 1.5L15 15l-4-4-4 4L5.5 13.5 7 12z"/>
                  </svg>
                </div>
                <h3 style="font-size: 1.5rem; font-weight: 600; color: #333; margin-bottom: 1rem;">التقارير</h3>
                <p style="color: #666; line-height: 1.6;">تقارير مفصلة وإحصائيات شاملة</p>
              </div>
            </div>

          </div>

          <!-- Footer -->
          <footer style="text-align: center; margin-top: 4rem; padding: 2rem 0; color: white; opacity: 0.8;">
            <p style="font-size: 1rem;">© 2024 شركة رافع للتطوير العقاري - فرع المدينة المنورة</p>
            <p style="font-size: 0.9rem; margin-top: 0.5rem;">جميع الحقوق محفوظة</p>
          </footer>
        </div>
      </div>
    </div>

    <!-- التطبيق الرئيسي -->
    <div id="root" style="display: none;"></div>

    <!-- JavaScript للتنقل -->
    <script>
      function navigateTo(path) {
        // إخفاء صفحة الترحيب
        document.getElementById('welcome-page').style.display = 'none';
        // إظهار التطبيق الرئيسي
        document.getElementById('root').style.display = 'block';

        // تحديث URL
        window.history.pushState({}, '', path);

        // تحميل التطبيق إذا لم يكن محملاً
        if (!window.appLoaded) {
          loadApp();
        } else {
          // إرسال حدث تغيير المسار
          window.dispatchEvent(new PopStateEvent('popstate'));
        }
      }

      function loadApp() {
        window.appLoaded = true;
        // تحميل التطبيق
        import('/src/main.tsx');
      }

      // التعامل مع زر الرجوع في المتصفح
      window.addEventListener('popstate', function(event) {
        if (window.location.pathname === '/' && window.location.pathname.length === 1) {
          // إظهار صفحة الترحيب
          document.getElementById('welcome-page').style.display = 'block';
          document.getElementById('root').style.display = 'none';
        }
      });

      // تحقق من المسار الحالي عند تحميل الصفحة
      window.addEventListener('load', function() {
        if (window.location.pathname !== '/' || window.location.search || window.location.hash) {
          navigateTo(window.location.pathname);
        }
      });
    </script>

    <!-- IMPORTANT: DO NOT REMOVE THIS SCRIPT TAG OR THIS VERY COMMENT! -->
    <script src="https://cdn.gpteng.co/gptengineer.js" type="module"></script>
  </body>
</html>
