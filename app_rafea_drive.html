<!DOCTYPE html>
<html lang="ar">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>نظام إدارة العقارات - رافعة المدينة مع Google Drive</title>
    <link href="https://fonts.googleapis.com/css2?family=Tajawal:wght@300;400;500;700&display=swap" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        * { margin: 0; padding: 0; box-sizing: border-box; }
        body { font-family: 'Tajawal', sans-serif; direction: rtl; background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: #333; min-height: 100vh; }

        /* Header */
        .header { background: rgba(255,255,255,0.95); backdrop-filter: blur(10px); color: #333; padding: 2rem; text-align: center; box-shadow: 0 4px 20px rgba(0,0,0,0.1); }
        .header h1 { font-size: 2.5rem; margin-bottom: 0.5rem; background: linear-gradient(135deg, #667eea, #764ba2); -webkit-background-clip: text; -webkit-text-fill-color: transparent; }
        .header p { font-size: 1.1rem; opacity: 0.8; }

        /* Drive Status */
        .drive-status-bar { background: rgba(255,255,255,0.9); padding: 1.5rem; display: flex; justify-content: space-between; align-items: center; flex-wrap: wrap; gap: 1rem; }
        .status-item { display: flex; align-items: center; gap: 0.5rem; padding: 0.8rem 1.5rem; border-radius: 25px; font-size: 1rem; font-weight: 500; }
        .status-connected { background: linear-gradient(135deg, #48bb78, #38a169); color: white; }
        .status-disconnected { background: linear-gradient(135deg, #f56565, #e53e3e); color: white; }
        .status-syncing { background: linear-gradient(135deg, #ed8936, #dd6b20); color: white; }

        /* Main Container */
        .container { max-width: 1200px; margin: 0 auto; padding: 2rem; }

        /* Drive Connection Section */
        .drive-section { background: rgba(255,255,255,0.95); backdrop-filter: blur(10px); border-radius: 20px; padding: 2rem; margin-bottom: 2rem; box-shadow: 0 8px 32px rgba(0,0,0,0.1); }
        .drive-section h2 { color: #333; margin-bottom: 1rem; display: flex; align-items: center; gap: 1rem; }
        .drive-section p { color: #666; line-height: 1.6; margin-bottom: 1.5rem; }

        /* Buttons */
        .btn { padding: 1rem 2rem; border: none; border-radius: 12px; cursor: pointer; font-family: inherit; font-size: 1rem; font-weight: 500; transition: all 0.3s; display: inline-flex; align-items: center; gap: 0.8rem; text-decoration: none; }
        .btn-primary { background: linear-gradient(135deg, #667eea, #764ba2); color: white; }
        .btn-primary:hover { transform: translateY(-2px); box-shadow: 0 6px 20px rgba(102, 126, 234, 0.4); }
        .btn-success { background: linear-gradient(135deg, #48bb78, #38a169); color: white; }
        .btn-danger { background: linear-gradient(135deg, #f56565, #e53e3e); color: white; }
        .btn-secondary { background: rgba(113, 128, 150, 0.1); color: #4a5568; border: 1px solid rgba(113, 128, 150, 0.3); }

        /* File Upload Area */
        .upload-area { border: 3px dashed #667eea; border-radius: 15px; padding: 3rem; text-align: center; transition: all 0.3s; cursor: pointer; margin: 2rem 0; }
        .upload-area:hover, .upload-area.dragover { border-color: #764ba2; background: rgba(102, 126, 234, 0.05); transform: scale(1.02); }
        .upload-icon { font-size: 4rem; color: #667eea; margin-bottom: 1rem; }
        .upload-text { font-size: 1.3rem; color: #333; margin-bottom: 0.5rem; font-weight: 500; }
        .upload-hint { color: #666; font-size: 1rem; }
        .file-input { display: none; }

        /* Progress Bar */
        .progress-container { margin: 1.5rem 0; display: none; }
        .progress-bar { width: 100%; height: 12px; background: #e2e8f0; border-radius: 6px; overflow: hidden; }
        .progress-fill { height: 100%; background: linear-gradient(90deg, #667eea, #764ba2); transition: width 0.3s; border-radius: 6px; }
        .progress-text { font-size: 1rem; color: #333; margin-top: 0.8rem; text-align: center; font-weight: 500; }

        /* File List */
        .file-list { display: grid; grid-template-columns: repeat(auto-fill, minmax(280px, 1fr)); gap: 1.5rem; margin-top: 2rem; }
        .file-item { background: rgba(255,255,255,0.9); border-radius: 15px; padding: 1.5rem; box-shadow: 0 4px 15px rgba(0,0,0,0.1); transition: all 0.3s; }
        .file-item:hover { transform: translateY(-3px); box-shadow: 0 8px 25px rgba(0,0,0,0.15); }
        .file-header { display: flex; align-items: center; gap: 1rem; margin-bottom: 1rem; }
        .file-icon { width: 50px; height: 50px; border-radius: 12px; display: flex; align-items: center; justify-content: center; font-size: 1.5rem; color: white; }
        .file-info h4 { font-size: 1.1rem; color: #333; margin-bottom: 0.3rem; }
        .file-info p { font-size: 0.9rem; color: #666; }
        .file-actions { display: flex; gap: 0.8rem; margin-top: 1rem; }
        .btn-small { padding: 0.6rem 1rem; font-size: 0.9rem; }

        /* Notifications */
        .notification { position: fixed; top: 20px; right: 20px; padding: 1rem 1.5rem; border-radius: 12px; color: white; z-index: 1000; min-width: 350px; transform: translateX(400px); transition: all 0.3s; display: flex; align-items: center; gap: 0.8rem; }
        .notification.show { transform: translateX(0); }
        .notification-success { background: linear-gradient(135deg, #48bb78, #38a169); }
        .notification-error { background: linear-gradient(135deg, #f56565, #e53e3e); }
        .notification-warning { background: linear-gradient(135deg, #ed8936, #dd6b20); }
        .notification-info { background: linear-gradient(135deg, #4299e1, #3182ce); }

        /* Loading Spinner */
        .loading { display: inline-block; width: 20px; height: 20px; border: 3px solid rgba(255,255,255,.3); border-radius: 50%; border-top-color: #fff; animation: spin 1s ease-in-out infinite; }
        @keyframes spin { to { transform: rotate(360deg); } }

        /* Responsive */
        @media (max-width: 768px) {
            .header h1 { font-size: 2rem; }
            .container { padding: 1rem; }
            .drive-status-bar { flex-direction: column; align-items: stretch; }
            .file-list { grid-template-columns: 1fr; }
            .file-actions { flex-direction: column; }
        }

        /* Animations */
        @keyframes fadeIn { from { opacity: 0; transform: translateY(30px); } to { opacity: 1; transform: translateY(0); } }
        .fade-in { animation: fadeIn 0.5s ease-out; }

        /* Drive Folder Info */
        .folder-info { background: linear-gradient(135deg, #667eea, #764ba2); color: white; border-radius: 15px; padding: 1.5rem; margin-bottom: 2rem; }
        .folder-info h3 { margin-bottom: 0.5rem; }
        .folder-info p { opacity: 0.9; }
        .folder-link { color: rgba(255,255,255,0.8); text-decoration: none; font-size: 0.9rem; }
        .folder-link:hover { color: white; }
    </style>
</head>
<body>
    <!-- Header -->
    <div class="header">
        <h1><i class="fas fa-building"></i> نظام إدارة العقارات</h1>
        <p>شركة رافعة المدينة للتطوير العقاري - متصل مع Google Drive</p>
    </div>

    <!-- Drive Status Bar -->
    <div class="drive-status-bar">
        <div id="drive-status" class="status-item status-disconnected">
            <i class="fas fa-cloud"></i>
            <span>غير متصل بـ Google Drive</span>
        </div>
        <div id="sync-status" class="status-item">
            <i class="fas fa-sync"></i>
            <span>آخر مزامنة: لم يتم بعد</span>
        </div>
        <div class="status-item">
            <i class="fas fa-folder"></i>
            <span>مجلد رافعة المدينة</span>
        </div>
    </div>

    <div class="container">
        <!-- Drive Folder Information -->
        <div class="folder-info fade-in">
            <h3><i class="fab fa-google-drive"></i> مجلد Google Drive المخصص</h3>
            <p>سيتم حفظ جميع الملفات في المجلد المخصص لشركة رافعة المدينة</p>
            <a href="https://drive.google.com/drive/folders/1LpNojGRTpgR_2SELYST39F_JeOsFqGPI" target="_blank" class="folder-link">
                <i class="fas fa-external-link-alt"></i> عرض المجلد في Google Drive
            </a>
        </div>

        <!-- Drive Connection Section -->
        <div class="drive-section fade-in" id="connection-section">
            <h2>
                <i class="fab fa-google-drive"></i>
                الاتصال مع Google Drive
            </h2>
            <p>اربط التطبيق مع Google Drive للوصول إلى مجلد رافعة المدينة وإدارة الملفات من أي مكان في العالم.</p>

            <div id="connection-buttons">
                <button class="btn btn-primary" onclick="connectGoogleDrive()">
                    <i class="fab fa-google"></i>
                    ربط مع Google Drive
                </button>
                <button class="btn btn-secondary" onclick="showSetupGuide()" style="margin-right: 1rem;">
                    <i class="fas fa-question-circle"></i>
                    دليل الإعداد
                </button>
            </div>
        </div>

        <!-- File Upload Section -->
        <div class="drive-section fade-in" id="upload-section" style="display: none;">
            <h2>
                <i class="fas fa-cloud-upload-alt"></i>
                رفع الملفات إلى مجلد رافعة المدينة
            </h2>

            <div class="upload-area" onclick="triggerFileInput()" ondrop="handleDrop(event)" ondragover="handleDragOver(event)" ondragleave="handleDragLeave(event)">
                <div class="upload-icon">
                    <i class="fas fa-cloud-upload-alt"></i>
                </div>
                <div class="upload-text">اسحب الملفات هنا أو انقر للاختيار</div>
                <div class="upload-hint">يدعم جميع أنواع الملفات (PDF, Word, Excel, صور، فيديو، إلخ)</div>
                <input type="file" id="file-input" class="file-input" multiple onchange="handleFileSelect(event)">
            </div>

            <div class="progress-container" id="progress-container">
                <div class="progress-bar">
                    <div class="progress-fill" id="progress-fill" style="width: 0%"></div>
                </div>
                <div class="progress-text" id="progress-text">جاري الرفع...</div>
            </div>

            <div style="display: flex; gap: 1rem; margin-top: 1rem;">
                <button class="btn btn-success" onclick="refreshFileList()">
                    <i class="fas fa-sync"></i>
                    تحديث قائمة الملفات
                </button>
                <button class="btn btn-secondary" onclick="openDriveFolder()">
                    <i class="fas fa-external-link-alt"></i>
                    فتح المجلد في Google Drive
                </button>
            </div>
        </div>

        <!-- File List Section -->
        <div class="drive-section fade-in" id="files-section" style="display: none;">
            <h2>
                <i class="fas fa-folder-open"></i>
                ملفات مجلد رافعة المدينة
            </h2>
            <div class="file-list" id="file-list">
                <!-- Files will be loaded here -->
            </div>
        </div>
    </div>

    <!-- Google Drive API -->
    <script src="https://apis.google.com/js/api.js"></script>
    <script src="https://accounts.google.com/gsi/client"></script>

    <script>
        // Google Drive Configuration
        const CLIENT_ID = 'YOUR_CLIENT_ID'; // سيتم توضيح كيفية الحصول عليه
        const API_KEY = 'YOUR_API_KEY'; // سيتم توضيح كيفية الحصول عليه
        const DISCOVERY_DOC = 'https://www.googleapis.com/discovery/v1/apis/drive/v3/rest';
        const SCOPES = 'https://www.googleapis.com/auth/drive.file';

        // Rafea Folder ID (extracted from the shared link)
        const RAFEA_FOLDER_ID = '1LpNojGRTpgR_2SELYST39F_JeOsFqGPI';

        // Application State
        let gapi, google;
        let isGoogleDriveConnected = false;
        let tokenClient;
        let accessToken = null;
        let uploadedFiles = [];

        // Initialize Application
        document.addEventListener('DOMContentLoaded', async function() {
            showNotification('مرحباً بك في نظام إدارة العقارات!', 'info');

            // Initialize Google Drive
            if (typeof gapi !== 'undefined' && typeof google !== 'undefined') {
                await initializeGoogleDrive();
            } else {
                showNotification('تعذر تحميل Google Drive API', 'error');
            }
        });

        // Google Drive Integration
        async function initializeGoogleDrive() {
            try {
                await new Promise((resolve) => {
                    gapi.load('client', resolve);
                });

                await gapi.client.init({
                    apiKey: API_KEY,
                    discoveryDocs: [DISCOVERY_DOC],
                });

                tokenClient = google.accounts.oauth2.initTokenClient({
                    client_id: CLIENT_ID,
                    scope: SCOPES,
                    callback: handleAuthCallback,
                });

                // Check if user was previously authenticated
                const savedToken = localStorage.getItem('rafea_drive_token');
                if (savedToken) {
                    accessToken = savedToken;
                    gapi.client.setToken({access_token: accessToken});
                    await testConnection();
                }
            } catch (error) {
                console.error('Error initializing Google Drive:', error);
                showNotification('خطأ في تهيئة Google Drive: تحقق من بيانات الاعتماد', 'error');
            }
        }

        function handleAuthCallback(resp) {
            if (resp.error !== undefined) {
                showNotification('خطأ في المصادقة: ' + resp.error, 'error');
                return;
            }

            accessToken = resp.access_token;
            localStorage.setItem('rafea_drive_token', accessToken);
            gapi.client.setToken({access_token: accessToken});

            updateDriveStatus(true);
            showNotification('تم الربط مع Google Drive بنجاح!', 'success');
            loadRafeaFolderFiles();
        }

        async function connectGoogleDrive() {
            if (!CLIENT_ID || CLIENT_ID === 'YOUR_CLIENT_ID') {
                showNotification('يجب إعداد بيانات الاعتماد أولاً. راجع دليل الإعداد.', 'warning');
                showSetupGuide();
                return;
            }

            if (!tokenClient) {
                showNotification('جاري تهيئة Google Drive...', 'info');
                await initializeGoogleDrive();
            }

            tokenClient.requestAccessToken({prompt: 'consent'});
        }

        async function testConnection() {
            try {
                // Test access to the specific Rafea folder
                const response = await gapi.client.drive.files.get({
                    fileId: RAFEA_FOLDER_ID,
                    fields: 'id, name, mimeType'
                });

                updateDriveStatus(true);
                loadRafeaFolderFiles();
                return true;
            } catch (error) {
                console.error('Connection test failed:', error);
                updateDriveStatus(false);

                if (error.status === 404) {
                    showNotification('لا يمكن الوصول لمجلد رافعة المدينة. تحقق من الصلاحيات.', 'error');
                }
                return false;
            }
        }

        function updateDriveStatus(connected) {
            isGoogleDriveConnected = connected;
            const statusEl = document.getElementById('drive-status');
            const connectionSection = document.getElementById('connection-section');
            const uploadSection = document.getElementById('upload-section');
            const filesSection = document.getElementById('files-section');

            if (connected) {
                statusEl.className = 'status-item status-connected';
                statusEl.innerHTML = '<i class="fas fa-cloud"></i><span>متصل بـ Google Drive</span>';

                connectionSection.style.display = 'none';
                uploadSection.style.display = 'block';
                filesSection.style.display = 'block';

                updateSyncStatus();
            } else {
                statusEl.className = 'status-item status-disconnected';
                statusEl.innerHTML = '<i class="fas fa-cloud"></i><span>غير متصل بـ Google Drive</span>';

                connectionSection.style.display = 'block';
                uploadSection.style.display = 'none';
                filesSection.style.display = 'none';
            }
        }

        function updateSyncStatus() {
            const syncEl = document.getElementById('sync-status');
            const now = new Date().toLocaleString('ar-SA');
            syncEl.innerHTML = `<i class="fas fa-sync"></i><span>آخر مزامنة: ${now}</span>`;
        }

        // File Upload Functions
        function triggerFileInput() {
            document.getElementById('file-input').click();
        }

        function handleFileSelect(event) {
            const files = event.target.files;
            if (files.length > 0) {
                uploadFiles(files);
            }
        }

        function handleDragOver(event) {
            event.preventDefault();
            event.currentTarget.classList.add('dragover');
        }

        function handleDragLeave(event) {
            event.currentTarget.classList.remove('dragover');
        }

        function handleDrop(event) {
            event.preventDefault();
            event.currentTarget.classList.remove('dragover');
            const files = event.dataTransfer.files;
            if (files.length > 0) {
                uploadFiles(files);
            }
        }

        async function uploadFiles(files) {
            if (!isGoogleDriveConnected) {
                showNotification('يجب الربط مع Google Drive أولاً', 'warning');
                return;
            }

            const progressContainer = document.getElementById('progress-container');
            const progressFill = document.getElementById('progress-fill');
            const progressText = document.getElementById('progress-text');

            progressContainer.style.display = 'block';

            for (let i = 0; i < files.length; i++) {
                const file = files[i];
                const progress = ((i + 1) / files.length) * 100;

                progressFill.style.width = progress + '%';
                progressText.textContent = `جاري رفع ${file.name} (${i + 1}/${files.length})`;

                try {
                    await uploadFileToRafeaFolder(file);
                    showNotification(`تم رفع ${file.name} بنجاح إلى مجلد رافعة المدينة`, 'success');
                } catch (error) {
                    showNotification(`خطأ في رفع ${file.name}: ${error.message}`, 'error');
                    console.error('Upload error:', error);
                }
            }

            progressContainer.style.display = 'none';
            loadRafeaFolderFiles();
            updateSyncStatus();
        }

        async function uploadFileToRafeaFolder(file) {
            const boundary = '-------314159265358979323846';
            const delimiter = "\r\n--" + boundary + "\r\n";
            const close_delim = "\r\n--" + boundary + "--";

            const metadata = {
                'name': file.name,
                'description': `رفع من نظام إدارة العقارات - رافعة المدينة - ${new Date().toLocaleString('ar-SA')}`,
                'parents': [RAFEA_FOLDER_ID] // Upload to Rafea folder
            };

            const multipartRequestBody =
                delimiter +
                'Content-Type: application/json\r\n\r\n' +
                JSON.stringify(metadata) +
                delimiter +
                'Content-Type: ' + file.type + '\r\n\r\n';

            const reader = new FileReader();
            return new Promise((resolve, reject) => {
                reader.onload = async function(e) {
                    const fileContent = e.target.result;
                    const fullRequestBody = multipartRequestBody + fileContent + close_delim;

                    try {
                        const request = await gapi.client.request({
                            'path': 'https://www.googleapis.com/upload/drive/v3/files',
                            'method': 'POST',
                            'params': {'uploadType': 'multipart'},
                            'headers': {
                                'Content-Type': 'multipart/related; boundary="' + boundary + '"'
                            },
                            'body': fullRequestBody
                        });
                        resolve(request);
                    } catch (error) {
                        reject(error);
                    }
                };
                reader.readAsArrayBuffer(file);
            });
        }

        // File List Management
        async function loadRafeaFolderFiles() {
            if (!isGoogleDriveConnected) {
                document.getElementById('file-list').innerHTML = '<p style="text-align: center; color: #666; padding: 2rem;">يجب الربط مع Google Drive لعرض الملفات</p>';
                return;
            }

            try {
                const response = await gapi.client.drive.files.list({
                    q: `'${RAFEA_FOLDER_ID}' in parents and trashed=false`,
                    pageSize: 50,
                    fields: 'files(id, name, size, mimeType, createdTime, webViewLink, thumbnailLink)',
                    orderBy: 'createdTime desc'
                });

                const files = response.result.files;
                uploadedFiles = files;
                displayFileList(files);
            } catch (error) {
                console.error('Error loading files:', error);
                showNotification('خطأ في تحميل ملفات مجلد رافعة المدينة', 'error');
            }
        }

        function displayFileList(files) {
            const fileList = document.getElementById('file-list');

            if (files.length === 0) {
                fileList.innerHTML = '<p style="text-align: center; color: #666; padding: 2rem;">لا توجد ملفات في مجلد رافعة المدينة</p>';
                return;
            }

            fileList.innerHTML = files.map(file => {
                const fileIcon = getFileIcon(file.mimeType);
                const fileSize = formatFileSize(file.size);
                const uploadDate = new Date(file.createdTime).toLocaleDateString('ar-SA');

                return `
                    <div class="file-item fade-in">
                        <div class="file-header">
                            <div class="file-icon" style="background: ${fileIcon.color};">
                                <i class="${fileIcon.icon}"></i>
                            </div>
                            <div class="file-info">
                                <h4>${file.name}</h4>
                                <p>${fileSize} • ${uploadDate}</p>
                            </div>
                        </div>
                        <div class="file-actions">
                            <button class="btn btn-small btn-primary" onclick="openFile('${file.webViewLink}')">
                                <i class="fas fa-eye"></i> عرض
                            </button>
                            <button class="btn btn-small btn-secondary" onclick="downloadFile('${file.id}', '${file.name}')">
                                <i class="fas fa-download"></i> تحميل
                            </button>
                            <button class="btn btn-small btn-danger" onclick="deleteFile('${file.id}', '${file.name}')">
                                <i class="fas fa-trash"></i> حذف
                            </button>
                        </div>
                    </div>
                `;
            }).join('');
        }

        function getFileIcon(mimeType) {
            if (mimeType.includes('image')) {
                return { icon: 'fas fa-image', color: 'linear-gradient(135deg, #48bb78, #38a169)' };
            } else if (mimeType.includes('pdf')) {
                return { icon: 'fas fa-file-pdf', color: 'linear-gradient(135deg, #f56565, #e53e3e)' };
            } else if (mimeType.includes('word') || mimeType.includes('document')) {
                return { icon: 'fas fa-file-word', color: 'linear-gradient(135deg, #4299e1, #3182ce)' };
            } else if (mimeType.includes('excel') || mimeType.includes('spreadsheet')) {
                return { icon: 'fas fa-file-excel', color: 'linear-gradient(135deg, #48bb78, #38a169)' };
            } else if (mimeType.includes('powerpoint') || mimeType.includes('presentation')) {
                return { icon: 'fas fa-file-powerpoint', color: 'linear-gradient(135deg, #ed8936, #dd6b20)' };
            } else if (mimeType.includes('video')) {
                return { icon: 'fas fa-file-video', color: 'linear-gradient(135deg, #9f7aea, #805ad5)' };
            } else if (mimeType.includes('audio')) {
                return { icon: 'fas fa-file-audio', color: 'linear-gradient(135deg, #f56565, #e53e3e)' };
            } else if (mimeType.includes('zip') || mimeType.includes('rar')) {
                return { icon: 'fas fa-file-archive', color: 'linear-gradient(135deg, #718096, #4a5568)' };
            } else {
                return { icon: 'fas fa-file', color: 'linear-gradient(135deg, #718096, #4a5568)' };
            }
        }

        function formatFileSize(bytes) {
            if (!bytes) return '0 B';
            const k = 1024;
            const sizes = ['B', 'KB', 'MB', 'GB'];
            const i = Math.floor(Math.log(bytes) / Math.log(k));
            return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
        }

        function openFile(webViewLink) {
            window.open(webViewLink, '_blank');
        }

        async function downloadFile(fileId, fileName) {
            try {
                const response = await gapi.client.drive.files.get({
                    fileId: fileId,
                    alt: 'media'
                });

                const blob = new Blob([response.body]);
                const url = URL.createObjectURL(blob);
                const a = document.createElement('a');
                a.href = url;
                a.download = fileName;
                a.click();
                URL.revokeObjectURL(url);

                showNotification(`تم تحميل ${fileName}`, 'success');
            } catch (error) {
                console.error('Download error:', error);
                showNotification('خطأ في تحميل الملف', 'error');
            }
        }

        async function deleteFile(fileId, fileName) {
            if (!confirm(`هل أنت متأكد من حذف الملف: ${fileName}؟\n\nسيتم حذف الملف من مجلد رافعة المدينة نهائياً.`)) {
                return;
            }

            try {
                await gapi.client.drive.files.delete({
                    fileId: fileId
                });

                showNotification(`تم حذف ${fileName} من مجلد رافعة المدينة`, 'success');
                loadRafeaFolderFiles();
                updateSyncStatus();
            } catch (error) {
                console.error('Delete error:', error);
                showNotification('خطأ في حذف الملف', 'error');
            }
        }

        function refreshFileList() {
            loadRafeaFolderFiles();
            showNotification('تم تحديث قائمة الملفات', 'info');
        }

        function openDriveFolder() {
            window.open(`https://drive.google.com/drive/folders/${RAFEA_FOLDER_ID}`, '_blank');
        }

        function showSetupGuide() {
            const guideText = `
دليل إعداد Google Drive:

1. اذهب إلى: https://console.cloud.google.com
2. أنشئ مشروع جديد أو اختر مشروع موجود
3. فعل Google Drive API
4. أنشئ بيانات اعتماد (API Key و OAuth Client ID)
5. استبدل YOUR_CLIENT_ID و YOUR_API_KEY في الكود
6. أضف النطاق المناسب في إعدادات OAuth

للمساعدة التفصيلية، راجع ملف QUICK_GOOGLE_DRIVE_SETUP.md
            `;

            alert(guideText);
        }

        // Utility Functions
        function showNotification(message, type = 'success') {
            const notification = document.createElement('div');
            notification.className = `notification notification-${type}`;

            const iconMap = {
                'success': 'fas fa-check-circle',
                'error': 'fas fa-exclamation-circle',
                'warning': 'fas fa-exclamation-triangle',
                'info': 'fas fa-info-circle'
            };

            notification.innerHTML = `
                <i class="${iconMap[type]}"></i>
                <span>${message}</span>
            `;

            document.body.appendChild(notification);

            // Show notification
            setTimeout(() => notification.classList.add('show'), 100);

            // Hide notification
            setTimeout(() => {
                notification.classList.remove('show');
                setTimeout(() => notification.remove(), 300);
            }, 5000);
        }
    </script>
</body>
</html>
