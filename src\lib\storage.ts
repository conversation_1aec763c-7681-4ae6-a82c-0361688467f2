// نظام تخزين بسيط وموثوق
export class SimpleStorage {
  // حفظ البيانات
  static save(key: string, data: any): void {
    try {
      const jsonData = JSON.stringify(data);
      localStorage.setItem(key, jsonData);
      console.log(`✅ تم حفظ البيانات: ${key}`, data);
    } catch (error) {
      console.error(`❌ خطأ في حفظ البيانات: ${key}`, error);
    }
  }

  // تحميل البيانات
  static load(key: string, defaultValue: any = null): any {
    try {
      const jsonData = localStorage.getItem(key);
      if (jsonData) {
        const data = JSON.parse(jsonData);
        console.log(`✅ تم تحميل البيانات: ${key}`, data);
        return data;
      }
      console.log(`⚠️ لا توجد بيانات محفوظة: ${key}، استخدام القيم الافتراضية`);
      return defaultValue;
    } catch (error) {
      console.error(`❌ خطأ في تحميل البيانات: ${key}`, error);
      return defaultValue;
    }
  }

  // حذف البيانات
  static remove(key: string): void {
    try {
      localStorage.removeItem(key);
      console.log(`✅ تم حذف البيانات: ${key}`);
    } catch (error) {
      console.error(`❌ خطأ في حذف البيانات: ${key}`, error);
    }
  }

  // مسح جميع البيانات
  static clear(): void {
    try {
      localStorage.clear();
      console.log(`✅ تم مسح جميع البيانات`);
    } catch (error) {
      console.error(`❌ خطأ في مسح البيانات`, error);
    }
  }

  // التحقق من وجود البيانات
  static exists(key: string): boolean {
    return localStorage.getItem(key) !== null;
  }
}

// مفاتيح التخزين
export const STORAGE_KEYS = {
  USERS: 'rafea_users',
  PROPERTIES: 'rafea_properties',
  INVOICES: 'rafea_invoices',
  SALES: 'rafea_sales',
  PROCUREMENT: 'rafea_procurement',
  SUPPLIERS: 'rafea_suppliers',
  MAINTENANCE: 'rafea_maintenance',
  REPORTS: 'rafea_reports'
};

// أنواع البيانات
export interface User {
  id: number;
  name: string;
  email: string;
  role: string;
  department: string;
  status: string;
  createdAt: string;
}

export interface Property {
  id: string;
  title: string;
  type: string;
  location: string;
  area: number;
  price: string;
  bedrooms: number | null;
  bathrooms: number | null;
  status: string;
  createdAt: string;
}

// البيانات الافتراضية
export const DEFAULT_USERS: User[] = [
  {
    id: 1,
    name: "أحمد محمد",
    email: "<EMAIL>",
    role: "مدير",
    department: "المبيعات",
    status: "نشط",
    createdAt: new Date().toISOString(),
  },
  {
    id: 2,
    name: "سارة عبدالله",
    email: "<EMAIL>",
    role: "مسؤول",
    department: "المالية",
    status: "نشط",
    createdAt: new Date().toISOString(),
  },
  {
    id: 3,
    name: "خالد العمري",
    email: "<EMAIL>",
    role: "موظف",
    department: "المبيعات",
    status: "غير نشط",
    createdAt: new Date().toISOString(),
  },
];

export const DEFAULT_PROPERTIES: Property[] = [
  {
    id: "P-001",
    title: "فيلا فاخرة في حي الشاطئ",
    type: "فيلا",
    location: "المدينة المنورة - حي الشاطئ",
    area: 450,
    price: "2,500,000",
    bedrooms: 5,
    bathrooms: 6,
    status: "متاح",
    createdAt: new Date().toISOString(),
  },
  {
    id: "P-002",
    title: "شقة مفروشة في العزيزية",
    type: "شقة",
    location: "المدينة المنورة - العزيزية",
    area: 120,
    price: "850,000",
    bedrooms: 3,
    bathrooms: 2,
    status: "تم البيع",
    createdAt: new Date().toISOString(),
  },
  {
    id: "P-003",
    title: "أرض تجارية في طريق الملك عبدالله",
    type: "أرض",
    location: "المدينة المنورة - طريق الملك عبدالله",
    area: 1200,
    price: "4,000,000",
    bedrooms: null,
    bathrooms: null,
    status: "متاح",
    createdAt: new Date().toISOString(),
  },
];

// خدمات البيانات
export class DataService {
  // خدمة المستخدمين
  static getUsers(): User[] {
    return SimpleStorage.load(STORAGE_KEYS.USERS, DEFAULT_USERS);
  }

  static saveUsers(users: User[]): void {
    SimpleStorage.save(STORAGE_KEYS.USERS, users);
  }

  static addUser(user: Omit<User, 'id' | 'createdAt'>): User {
    const users = this.getUsers();
    const newId = users.length > 0 ? Math.max(...users.map(u => u.id)) + 1 : 1;
    const newUser: User = {
      ...user,
      id: newId,
      createdAt: new Date().toISOString(),
    };
    users.push(newUser);
    this.saveUsers(users);
    return newUser;
  }

  static updateUser(updatedUser: User): void {
    const users = this.getUsers();
    const index = users.findIndex(u => u.id === updatedUser.id);
    if (index !== -1) {
      users[index] = updatedUser;
      this.saveUsers(users);
    }
  }

  static deleteUser(userId: number): void {
    const users = this.getUsers();
    const filteredUsers = users.filter(u => u.id !== userId);
    this.saveUsers(filteredUsers);
  }

  // خدمة العقارات
  static getProperties(): Property[] {
    return SimpleStorage.load(STORAGE_KEYS.PROPERTIES, DEFAULT_PROPERTIES);
  }

  static saveProperties(properties: Property[]): void {
    SimpleStorage.save(STORAGE_KEYS.PROPERTIES, properties);
  }

  static addProperty(property: Omit<Property, 'createdAt'>): Property {
    const properties = this.getProperties();
    const newProperty: Property = {
      ...property,
      createdAt: new Date().toISOString(),
    };
    properties.push(newProperty);
    this.saveProperties(properties);
    return newProperty;
  }

  static updateProperty(updatedProperty: Property): void {
    const properties = this.getProperties();
    const index = properties.findIndex(p => p.id === updatedProperty.id);
    if (index !== -1) {
      properties[index] = updatedProperty;
      this.saveProperties(properties);
    }
  }

  static deleteProperty(propertyId: string): void {
    const properties = this.getProperties();
    const filteredProperties = properties.filter(p => p.id !== propertyId);
    this.saveProperties(filteredProperties);
  }

  // إنشاء معرف فريد للعقار
  static generatePropertyId(): string {
    const properties = this.getProperties();
    const maxId = properties.reduce((max, property) => {
      const idNumber = parseInt(property.id.replace('P-', ''));
      return Math.max(max, isNaN(idNumber) ? 0 : idNumber);
    }, 0);
    return `P-${String(maxId + 1).padStart(3, '0')}`;
  }

  // إنشاء نسخة احتياطية
  static createBackup(): string {
    const backup = {
      users: this.getUsers(),
      properties: this.getProperties(),
      timestamp: new Date().toISOString(),
      version: '1.0'
    };
    return JSON.stringify(backup, null, 2);
  }

  // استعادة النسخة الاحتياطية
  static restoreBackup(backupData: string): void {
    try {
      const backup = JSON.parse(backupData);
      if (backup.users) {
        this.saveUsers(backup.users);
      }
      if (backup.properties) {
        this.saveProperties(backup.properties);
      }
      console.log('✅ تم استعادة النسخة الاحتياطية بنجاح');
    } catch (error) {
      console.error('❌ خطأ في استعادة النسخة الاحتياطية:', error);
      throw new Error('فشل في استعادة النسخة الاحتياطية');
    }
  }
}
