// نظام تخزين بسيط وموثوق
export class SimpleStorage {
  // حفظ البيانات
  static save(key: string, data: any): void {
    try {
      const jsonData = JSON.stringify(data);
      localStorage.setItem(key, jsonData);
      console.log(`✅ تم حفظ البيانات: ${key}`, data);
    } catch (error) {
      console.error(`❌ خطأ في حفظ البيانات: ${key}`, error);
    }
  }

  // تحميل البيانات
  static load(key: string, defaultValue: any = null): any {
    try {
      const jsonData = localStorage.getItem(key);
      if (jsonData) {
        const data = JSON.parse(jsonData);
        console.log(`✅ تم تحميل البيانات: ${key}`, data);
        return data;
      }
      console.log(`⚠️ لا توجد بيانات محفوظة: ${key}، استخدام القيم الافتراضية`);
      return defaultValue;
    } catch (error) {
      console.error(`❌ خطأ في تحميل البيانات: ${key}`, error);
      return defaultValue;
    }
  }

  // حذف البيانات
  static remove(key: string): void {
    try {
      localStorage.removeItem(key);
      console.log(`✅ تم حذف البيانات: ${key}`);
    } catch (error) {
      console.error(`❌ خطأ في حذف البيانات: ${key}`, error);
    }
  }

  // مسح جميع البيانات
  static clear(): void {
    try {
      localStorage.clear();
      console.log(`✅ تم مسح جميع البيانات`);
    } catch (error) {
      console.error(`❌ خطأ في مسح البيانات`, error);
    }
  }

  // التحقق من وجود البيانات
  static exists(key: string): boolean {
    return localStorage.getItem(key) !== null;
  }
}

// مفاتيح التخزين
export const STORAGE_KEYS = {
  USERS: 'rafea_users',
  PROPERTIES: 'rafea_properties',
  INVOICES: 'rafea_invoices',
  SALES: 'rafea_sales',
  PROCUREMENT: 'rafea_procurement',
  SUPPLIERS: 'rafea_suppliers',
  MAINTENANCE: 'rafea_maintenance',
  REPORTS: 'rafea_reports'
};

// أنواع البيانات
export interface User {
  id: number;
  name: string;
  email: string;
  role: string;
  department: string;
  status: string;
  createdAt: string;
}

export interface Property {
  id: string;
  title: string;
  type: string;
  location: string;
  area: number;
  price: string;
  bedrooms: number | null;
  bathrooms: number | null;
  status: string;
  createdAt: string;
}

export interface Invoice {
  id: string;
  customerName: string;
  customerPhone: string;
  customerEmail: string;
  propertyId: string;
  propertyTitle: string;
  amount: string;
  status: string;
  date: string;
  dueDate: string;
  createdAt: string;
}

export interface Supplier {
  id: number;
  name: string;
  email: string;
  phone: string;
  address: string;
  category: string;
  status: string;
  createdAt: string;
}

export interface ProcurementItem {
  id: string;
  itemName: string;
  category: string;
  supplier: string;
  quantity: number;
  unitPrice: string;
  totalPrice: string;
  status: string;
  requestDate: string;
  deliveryDate: string;
  createdAt: string;
}

// البيانات الافتراضية
export const DEFAULT_USERS: User[] = [
  {
    id: 1,
    name: "أحمد محمد",
    email: "<EMAIL>",
    role: "مدير",
    department: "المبيعات",
    status: "نشط",
    createdAt: new Date().toISOString(),
  },
  {
    id: 2,
    name: "سارة عبدالله",
    email: "<EMAIL>",
    role: "مسؤول",
    department: "المالية",
    status: "نشط",
    createdAt: new Date().toISOString(),
  },
  {
    id: 3,
    name: "خالد العمري",
    email: "<EMAIL>",
    role: "موظف",
    department: "المبيعات",
    status: "غير نشط",
    createdAt: new Date().toISOString(),
  },
];

export const DEFAULT_PROPERTIES: Property[] = [
  {
    id: "P-001",
    title: "فيلا فاخرة في حي الشاطئ",
    type: "فيلا",
    location: "المدينة المنورة - حي الشاطئ",
    area: 450,
    price: "2,500,000",
    bedrooms: 5,
    bathrooms: 6,
    status: "متاح",
    createdAt: new Date().toISOString(),
  },
  {
    id: "P-002",
    title: "شقة مفروشة في العزيزية",
    type: "شقة",
    location: "المدينة المنورة - العزيزية",
    area: 120,
    price: "850,000",
    bedrooms: 3,
    bathrooms: 2,
    status: "تم البيع",
    createdAt: new Date().toISOString(),
  },
  {
    id: "P-003",
    title: "أرض تجارية في طريق الملك عبدالله",
    type: "أرض",
    location: "المدينة المنورة - طريق الملك عبدالله",
    area: 1200,
    price: "4,000,000",
    bedrooms: null,
    bathrooms: null,
    status: "متاح",
    createdAt: new Date().toISOString(),
  },
];

export const DEFAULT_INVOICES: Invoice[] = [
  {
    id: "INV-001",
    customerName: "محمد أحمد السعيد",
    customerPhone: "0501234567",
    customerEmail: "<EMAIL>",
    propertyId: "P-001",
    propertyTitle: "فيلا فاخرة في حي الشاطئ",
    amount: "2,500,000",
    status: "مدفوع",
    date: "2024-05-20",
    dueDate: "2024-06-20",
    createdAt: new Date().toISOString(),
  },
  {
    id: "INV-002",
    customerName: "فاطمة علي الزهراني",
    customerPhone: "0507654321",
    customerEmail: "<EMAIL>",
    propertyId: "P-002",
    propertyTitle: "شقة مفروشة في العزيزية",
    amount: "850,000",
    status: "معلق",
    date: "2024-05-22",
    dueDate: "2024-06-22",
    createdAt: new Date().toISOString(),
  },
  {
    id: "INV-003",
    customerName: "عبدالله محمد القحطاني",
    customerPhone: "0509876543",
    customerEmail: "<EMAIL>",
    propertyId: "P-003",
    propertyTitle: "أرض تجارية في طريق الملك عبدالله",
    amount: "4,000,000",
    status: "مرفوض",
    date: "2024-05-18",
    dueDate: "2024-06-18",
    createdAt: new Date().toISOString(),
  },
];

export const DEFAULT_SUPPLIERS: Supplier[] = [
  {
    id: 1,
    name: "شركة البناء المتطور",
    email: "<EMAIL>",
    phone: "0112345678",
    address: "المدينة المنورة - حي العوالي",
    category: "مواد البناء",
    status: "نشط",
    createdAt: new Date().toISOString(),
  },
  {
    id: 2,
    name: "مؤسسة الكهرباء الحديثة",
    email: "<EMAIL>",
    phone: "0118765432",
    address: "المدينة المنورة - حي الشرفية",
    category: "كهرباء",
    status: "نشط",
    createdAt: new Date().toISOString(),
  },
  {
    id: 3,
    name: "شركة السباكة المتخصصة",
    email: "<EMAIL>",
    phone: "0115555555",
    address: "المدينة المنورة - حي النرجس",
    category: "سباكة",
    status: "غير نشط",
    createdAt: new Date().toISOString(),
  },
];

export const DEFAULT_PROCUREMENT: ProcurementItem[] = [
  {
    id: "PR-001",
    itemName: "أسمنت بورتلاندي",
    category: "مواد البناء",
    supplier: "شركة البناء المتطور",
    quantity: 100,
    unitPrice: "25",
    totalPrice: "2,500",
    status: "تم التسليم",
    requestDate: "2024-05-15",
    deliveryDate: "2024-05-20",
    createdAt: new Date().toISOString(),
  },
  {
    id: "PR-002",
    itemName: "كابلات كهربائية",
    category: "كهرباء",
    supplier: "مؤسسة الكهرباء الحديثة",
    quantity: 50,
    unitPrice: "15",
    totalPrice: "750",
    status: "قيد التنفيذ",
    requestDate: "2024-05-18",
    deliveryDate: "2024-05-25",
    createdAt: new Date().toISOString(),
  },
  {
    id: "PR-003",
    itemName: "أنابيب PVC",
    category: "سباكة",
    supplier: "شركة السباكة المتخصصة",
    quantity: 30,
    unitPrice: "20",
    totalPrice: "600",
    status: "معلق",
    requestDate: "2024-05-20",
    deliveryDate: "2024-05-30",
    createdAt: new Date().toISOString(),
  },
];

// خدمات البيانات
export class DataService {
  // خدمة المستخدمين
  static getUsers(): User[] {
    return SimpleStorage.load(STORAGE_KEYS.USERS, DEFAULT_USERS);
  }

  static saveUsers(users: User[]): void {
    SimpleStorage.save(STORAGE_KEYS.USERS, users);
  }

  static addUser(user: Omit<User, 'id' | 'createdAt'>): User {
    const users = this.getUsers();
    const newId = users.length > 0 ? Math.max(...users.map(u => u.id)) + 1 : 1;
    const newUser: User = {
      ...user,
      id: newId,
      createdAt: new Date().toISOString(),
    };
    users.push(newUser);
    this.saveUsers(users);
    return newUser;
  }

  static updateUser(updatedUser: User): void {
    const users = this.getUsers();
    const index = users.findIndex(u => u.id === updatedUser.id);
    if (index !== -1) {
      users[index] = updatedUser;
      this.saveUsers(users);
    }
  }

  static deleteUser(userId: number): void {
    const users = this.getUsers();
    const filteredUsers = users.filter(u => u.id !== userId);
    this.saveUsers(filteredUsers);
  }

  // خدمة العقارات
  static getProperties(): Property[] {
    return SimpleStorage.load(STORAGE_KEYS.PROPERTIES, DEFAULT_PROPERTIES);
  }

  static saveProperties(properties: Property[]): void {
    SimpleStorage.save(STORAGE_KEYS.PROPERTIES, properties);
  }

  static addProperty(property: Omit<Property, 'createdAt'>): Property {
    const properties = this.getProperties();
    const newProperty: Property = {
      ...property,
      createdAt: new Date().toISOString(),
    };
    properties.push(newProperty);
    this.saveProperties(properties);
    return newProperty;
  }

  static updateProperty(updatedProperty: Property): void {
    const properties = this.getProperties();
    const index = properties.findIndex(p => p.id === updatedProperty.id);
    if (index !== -1) {
      properties[index] = updatedProperty;
      this.saveProperties(properties);
    }
  }

  static deleteProperty(propertyId: string): void {
    const properties = this.getProperties();
    const filteredProperties = properties.filter(p => p.id !== propertyId);
    this.saveProperties(filteredProperties);
  }

  // إنشاء معرف فريد للعقار
  static generatePropertyId(): string {
    const properties = this.getProperties();
    const maxId = properties.reduce((max, property) => {
      const idNumber = parseInt(property.id.replace('P-', ''));
      return Math.max(max, isNaN(idNumber) ? 0 : idNumber);
    }, 0);
    return `P-${String(maxId + 1).padStart(3, '0')}`;
  }

  // خدمة المستخلصات
  static getInvoices(): Invoice[] {
    return SimpleStorage.load(STORAGE_KEYS.INVOICES, DEFAULT_INVOICES);
  }

  static saveInvoices(invoices: Invoice[]): void {
    SimpleStorage.save(STORAGE_KEYS.INVOICES, invoices);
  }

  static addInvoice(invoice: Omit<Invoice, 'createdAt'>): Invoice {
    const invoices = this.getInvoices();
    const newInvoice: Invoice = {
      ...invoice,
      createdAt: new Date().toISOString(),
    };
    invoices.push(newInvoice);
    this.saveInvoices(invoices);
    return newInvoice;
  }

  static updateInvoice(updatedInvoice: Invoice): void {
    const invoices = this.getInvoices();
    const index = invoices.findIndex(i => i.id === updatedInvoice.id);
    if (index !== -1) {
      invoices[index] = updatedInvoice;
      this.saveInvoices(invoices);
    }
  }

  static deleteInvoice(invoiceId: string): void {
    const invoices = this.getInvoices();
    const filteredInvoices = invoices.filter(i => i.id !== invoiceId);
    this.saveInvoices(filteredInvoices);
  }

  static generateInvoiceId(): string {
    const invoices = this.getInvoices();
    const maxId = invoices.reduce((max, invoice) => {
      const idNumber = parseInt(invoice.id.replace('INV-', ''));
      return Math.max(max, isNaN(idNumber) ? 0 : idNumber);
    }, 0);
    return `INV-${String(maxId + 1).padStart(3, '0')}`;
  }

  // خدمة الموردين
  static getSuppliers(): Supplier[] {
    return SimpleStorage.load(STORAGE_KEYS.SUPPLIERS, DEFAULT_SUPPLIERS);
  }

  static saveSuppliers(suppliers: Supplier[]): void {
    SimpleStorage.save(STORAGE_KEYS.SUPPLIERS, suppliers);
  }

  static addSupplier(supplier: Omit<Supplier, 'id' | 'createdAt'>): Supplier {
    const suppliers = this.getSuppliers();
    const newId = suppliers.length > 0 ? Math.max(...suppliers.map(s => s.id)) + 1 : 1;
    const newSupplier: Supplier = {
      ...supplier,
      id: newId,
      createdAt: new Date().toISOString(),
    };
    suppliers.push(newSupplier);
    this.saveSuppliers(suppliers);
    return newSupplier;
  }

  static updateSupplier(updatedSupplier: Supplier): void {
    const suppliers = this.getSuppliers();
    const index = suppliers.findIndex(s => s.id === updatedSupplier.id);
    if (index !== -1) {
      suppliers[index] = updatedSupplier;
      this.saveSuppliers(suppliers);
    }
  }

  static deleteSupplier(supplierId: number): void {
    const suppliers = this.getSuppliers();
    const filteredSuppliers = suppliers.filter(s => s.id !== supplierId);
    this.saveSuppliers(filteredSuppliers);
  }

  // خدمة المشتريات
  static getProcurement(): ProcurementItem[] {
    return SimpleStorage.load(STORAGE_KEYS.PROCUREMENT, DEFAULT_PROCUREMENT);
  }

  static saveProcurement(procurement: ProcurementItem[]): void {
    SimpleStorage.save(STORAGE_KEYS.PROCUREMENT, procurement);
  }

  static addProcurementItem(item: Omit<ProcurementItem, 'createdAt'>): ProcurementItem {
    const procurement = this.getProcurement();
    const newItem: ProcurementItem = {
      ...item,
      createdAt: new Date().toISOString(),
    };
    procurement.push(newItem);
    this.saveProcurement(procurement);
    return newItem;
  }

  static updateProcurementItem(updatedItem: ProcurementItem): void {
    const procurement = this.getProcurement();
    const index = procurement.findIndex(p => p.id === updatedItem.id);
    if (index !== -1) {
      procurement[index] = updatedItem;
      this.saveProcurement(procurement);
    }
  }

  static deleteProcurementItem(itemId: string): void {
    const procurement = this.getProcurement();
    const filteredProcurement = procurement.filter(p => p.id !== itemId);
    this.saveProcurement(filteredProcurement);
  }

  static generateProcurementId(): string {
    const procurement = this.getProcurement();
    const maxId = procurement.reduce((max, item) => {
      const idNumber = parseInt(item.id.replace('PR-', ''));
      return Math.max(max, isNaN(idNumber) ? 0 : idNumber);
    }, 0);
    return `PR-${String(maxId + 1).padStart(3, '0')}`;
  }

  // إنشاء نسخة احتياطية
  static createBackup(): string {
    const backup = {
      users: this.getUsers(),
      properties: this.getProperties(),
      invoices: this.getInvoices(),
      suppliers: this.getSuppliers(),
      procurement: this.getProcurement(),
      timestamp: new Date().toISOString(),
      version: '2.0'
    };
    return JSON.stringify(backup, null, 2);
  }

  // استعادة النسخة الاحتياطية
  static restoreBackup(backupData: string): void {
    try {
      const backup = JSON.parse(backupData);
      if (backup.users) {
        this.saveUsers(backup.users);
      }
      if (backup.properties) {
        this.saveProperties(backup.properties);
      }
      if (backup.invoices) {
        this.saveInvoices(backup.invoices);
      }
      if (backup.suppliers) {
        this.saveSuppliers(backup.suppliers);
      }
      if (backup.procurement) {
        this.saveProcurement(backup.procurement);
      }
      console.log('✅ تم استعادة النسخة الاحتياطية بنجاح');
    } catch (error) {
      console.error('❌ خطأ في استعادة النسخة الاحتياطية:', error);
      throw new Error('فشل في استعادة النسخة الاحتياطية');
    }
  }
}
