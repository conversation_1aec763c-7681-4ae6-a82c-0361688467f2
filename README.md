# 🏢 نظام إدارة العقارات - رافعة المدينة

نظام شامل لإدارة العقارات والمستخدمين والمستخلصات والموردين والمشتريات

## 🎯 نظرة عامة

نظام إدارة العقارات هو تطبيق ويب متكامل مصمم خصيصاً لشركة رافعة المدينة للتطوير العقاري. يوفر النظام واجهة سهلة الاستخدام لإدارة جميع جوانب الأعمال العقارية.

## ✨ المميزات الرئيسية

### 🏠 إدارة العقارات
- إضافة وتعديل وحذف العقارات
- تصنيف العقارات (شقق، فلل، محلات، مكاتب، أراضي)
- تتبع حالة العقار (متاح، محجوز، مباع)
- إدارة تفاصيل العقار (المساحة، السعر، الموقع)

### 👥 إدارة المستخدمين
- إضافة وإدارة المستخدمين
- تحديد الأدوار الوظيفية
- إدارة الأقسام والصلاحيات
- تتبع حالة المستخدم (نشط/غير نشط)

### 📄 إدارة المستخلصات
- إنشاء وإدارة المستخلصات المالية
- تتبع حالة الدفع (معلق، مدفوع، مرفوض)
- ربط المستخلصات بالعقارات والعملاء
- إدارة تواريخ الاستحقاق

### 🏭 إدارة الموردين
- قاعدة بيانات شاملة للموردين
- تصنيف الموردين حسب الخدمة
- إدارة بيانات الاتصال
- تتبع حالة التعاون

### 🛒 إدارة المشتريات
- تسجيل طلبات الشراء
- حساب التكاليف الإجمالية
- تتبع حالة التسليم
- ربط المشتريات بالموردين

### ⚙️ إدارة النظام
- نسخ احتياطية للبيانات
- تصدير واستيراد البيانات
- إحصائيات شاملة
- إعدادات النظام

## 🚀 الإصدارات المتاحة

### 📱 النسخة المستقلة
- **الملف**: `app_complete.html`
- **المميزات**: يعمل مباشرة من المتصفح
- **التخزين**: localStorage (محلي)
- **الاستخدام**: فردي أو محلي

### 🌐 نسخة السيرفر
- **الملفات**: `index.php` + `api.php`
- **المميزات**: يعمل على سيرفر خارجي
- **التخزين**: ملفات JSON على السيرفر
- **الاستخدام**: متعدد المستخدمين

## 🔧 متطلبات التشغيل

### النسخة المستقلة:
- متصفح حديث (Chrome, Firefox, Safari, Edge)
- JavaScript مفعل
- لا يحتاج إنترنت للعمل

### نسخة السيرفر:
- PHP 7.4+ (يفضل 8.0+)
- Apache/Nginx مع mod_rewrite
- مساحة تخزين 50MB+
- صلاحيات كتابة الملفات

## 📥 التثبيت والاستخدام

### النسخة المستقلة:
1. حمل ملف `app_complete.html`
2. انقر مرتين لفتحه في المتصفح
3. ابدأ الاستخدام فوراً!

### نسخة السيرفر:
1. ارفع الملفات للسيرفر:
   - `index.php`
   - `api.php`
   - `.htaccess`
2. تأكد من صلاحيات الكتابة
3. افتح الموقع في المتصفح

## 🎨 واجهة المستخدم

### التصميم:
- **اللغة**: عربية من اليمين لليسار
- **الألوان**: متدرجة أزرق-بنفسجي
- **الخط**: Tajawal العربي الأنيق
- **التجاوب**: يعمل على جميع الأجهزة

### التنقل:
- شريط تنقل علوي
- صفحات منفصلة لكل قسم
- انتقال سلس بين الصفحات
- رسائل تفاعلية

## 💾 نظام التخزين

### النسخة المستقلة:
- **localStorage**: تخزين محلي في المتصفح
- **الأمان**: بيانات محلية آمنة
- **النسخ الاحتياطية**: تصدير/استيراد JSON

### نسخة السيرفر:
- **ملفات JSON**: تخزين منظم على السيرفر
- **الحماية**: مجلد محمي بـ .htaccess
- **النسخ الاحتياطية**: تلقائية ويدوية

## 🔒 الأمان والحماية

### إجراءات الأمان:
- حماية من XSS و CSRF
- تشفير البيانات
- صلاحيات محدودة للملفات
- Headers أمان شاملة

### حماية البيانات:
- منع الوصول المباشر للملفات
- تشفير الاتصالات
- نسخ احتياطية منتظمة
- سجلات الأمان

## 📊 الإحصائيات والتقارير

### لوحة المعلومات:
- إجمالي العقارات
- عدد المستخدمين
- المستخلصات المالية
- الموردين النشطين

### التقارير:
- تقارير مالية
- إحصائيات العقارات
- تقارير المشتريات
- تحليل الأداء

## 🔄 النسخ الاحتياطية

### النسخة المستقلة:
- تصدير يدوي للبيانات
- حفظ ملف JSON
- استيراد البيانات

### نسخة السيرفر:
- نسخ احتياطية تلقائية
- تصدير شامل للبيانات
- استعادة سريعة
- أرشفة منتظمة

## 🛠️ التخصيص والتطوير

### إضافة مميزات:
- سهولة إضافة حقول جديدة
- تخصيص التصميم
- إضافة تقارير مخصصة
- تكامل مع أنظمة أخرى

### التطوير:
- كود مفتوح المصدر
- تعليقات شاملة
- هيكل منظم
- سهولة الصيانة

## 📱 التوافق

### المتصفحات:
- ✅ Chrome 80+
- ✅ Firefox 75+
- ✅ Safari 13+
- ✅ Edge 80+

### الأجهزة:
- ✅ أجهزة الكمبيوتر
- ✅ الأجهزة اللوحية
- ✅ الهواتف الذكية
- ✅ جميع أحجام الشاشات

## 🆘 الدعم والمساعدة

### الوثائق:
- `STANDALONE_INSTRUCTIONS.md` - دليل النسخة المستقلة
- `SERVER_DEPLOYMENT_GUIDE.md` - دليل نشر السيرفر
- `README.md` - هذا الملف

### حل المشاكل:
- فحص وحدة التحكم للأخطاء
- التأكد من متطلبات النظام
- مراجعة ملفات السجل
- اختبار الاتصال

## 📈 خطط التطوير

### الإصدارات القادمة:
- [ ] نظام مصادقة متقدم
- [ ] تقارير PDF
- [ ] إشعارات البريد الإلكتروني
- [ ] تطبيق الهاتف المحمول
- [ ] تكامل مع أنظمة المحاسبة
- [ ] خرائط تفاعلية للعقارات

## 📄 الترخيص

هذا المشروع مطور خصيصاً لشركة رافعة المدينة للتطوير العقاري.

## 👨‍💻 المطور

تم تطوير هذا النظام بواسطة فريق التطوير المتخصص.

---

## 🎉 ابدأ الآن!

### للنسخة المستقلة:
```bash
# افتح الملف مباشرة
app_complete.html
```

### لنسخة السيرفر:
```bash
# ارفع الملفات واذهب إلى:
https://yourdomain.com
```

**🚀 استمتع بنظام إدارة عقارات متكامل وسهل الاستخدام!**
