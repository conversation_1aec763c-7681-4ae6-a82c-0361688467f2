import React, { useState } from "react";
import { Button } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { useToast } from "@/components/ui/use-toast";
import { database } from "@/lib/database";
import { Download, Upload, Database, AlertTriangle, CheckCircle } from "lucide-react";

const BackupManager: React.FC = () => {
  const { toast } = useToast();
  const [isBackupDialogOpen, setIsBackupDialogOpen] = useState(false);
  const [isRestoreDialogOpen, setIsRestoreDialogOpen] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const [selectedFile, setSelectedFile] = useState<File | null>(null);

  // إنشاء وتنزيل نسخة احتياطية
  const handleCreateBackup = async () => {
    setIsLoading(true);
    try {
      await database.downloadBackup();
      toast({
        title: "تم إنشاء النسخة الاحتياطية بنجاح",
        description: "تم تنزيل النسخة الاحتياطية على جهازك.",
      });
      setIsBackupDialogOpen(false);
    } catch (error) {
      toast({
        title: "خطأ في إنشاء النسخة الاحتياطية",
        description: "حدث خطأ أثناء إنشاء النسخة الاحتياطية. يرجى المحاولة مرة أخرى.",
        variant: "destructive",
      });
    } finally {
      setIsLoading(false);
    }
  };

  // اختيار ملف للاستعادة
  const handleFileSelect = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (file) {
      if (file.type === "application/json" || file.name.endsWith('.json')) {
        setSelectedFile(file);
      } else {
        toast({
          title: "نوع ملف غير صحيح",
          description: "يرجى اختيار ملف JSON فقط.",
          variant: "destructive",
        });
      }
    }
  };

  // استعادة النسخة الاحتياطية
  const handleRestoreBackup = async () => {
    if (!selectedFile) {
      toast({
        title: "لم يتم اختيار ملف",
        description: "يرجى اختيار ملف النسخة الاحتياطية أولاً.",
        variant: "destructive",
      });
      return;
    }

    setIsLoading(true);
    try {
      await database.uploadAndRestoreBackup(selectedFile);
      toast({
        title: "تم استعادة النسخة الاحتياطية بنجاح",
        description: "تم استعادة جميع البيانات من النسخة الاحتياطية.",
      });
      setIsRestoreDialogOpen(false);
      setSelectedFile(null);
      
      // إعادة تحميل الصفحة لتحديث البيانات
      setTimeout(() => {
        window.location.reload();
      }, 1000);
    } catch (error) {
      toast({
        title: "خطأ في استعادة النسخة الاحتياطية",
        description: "حدث خطأ أثناء استعادة النسخة الاحتياطية. يرجى التأكد من صحة الملف.",
        variant: "destructive",
      });
    } finally {
      setIsLoading(false);
    }
  };

  // عرض معلومات قاعدة البيانات
  const handleShowDatabaseInfo = async () => {
    try {
      const backupData = await database.createBackup();
      const backup = JSON.parse(backupData);
      
      let info = "معلومات قاعدة البيانات:\n\n";
      info += `تاريخ الإنشاء: ${new Date(backup.timestamp).toLocaleString('ar-SA')}\n`;
      info += `إصدار قاعدة البيانات: ${backup.version}\n\n`;
      
      const tables = ['users', 'properties', 'invoices', 'procurement', 'suppliers', 'sales', 'maintenance'];
      tables.forEach(table => {
        const count = backup[table]?.length || 0;
        info += `${getTableDisplayName(table)}: ${count} عنصر\n`;
      });

      alert(info);
    } catch (error) {
      toast({
        title: "خطأ في عرض معلومات قاعدة البيانات",
        description: "حدث خطأ أثناء جمع معلومات قاعدة البيانات.",
        variant: "destructive",
      });
    }
  };

  // الحصول على اسم الجدول للعرض
  const getTableDisplayName = (tableName: string): string => {
    const displayNames: Record<string, string> = {
      users: "المستخدمين",
      properties: "العقارات",
      invoices: "المستخلصات",
      procurement: "المشتريات",
      suppliers: "الموردين",
      sales: "المبيعات",
      maintenance: "الصيانة",
    };
    return displayNames[tableName] || tableName;
  };

  return (
    <div className="space-y-4">
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Database size={20} />
            إدارة النسخ الاحتياطية
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <p className="text-sm text-muted-foreground">
            يمكنك إنشاء نسخة احتياطية من جميع بيانات التطبيق أو استعادة نسخة احتياطية سابقة.
          </p>
          
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            {/* إنشاء نسخة احتياطية */}
            <Dialog open={isBackupDialogOpen} onOpenChange={setIsBackupDialogOpen}>
              <DialogTrigger asChild>
                <Button className="w-full gap-2" variant="default">
                  <Download size={16} />
                  إنشاء نسخة احتياطية
                </Button>
              </DialogTrigger>
              <DialogContent>
                <DialogHeader>
                  <DialogTitle>إنشاء نسخة احتياطية</DialogTitle>
                  <DialogDescription>
                    سيتم إنشاء نسخة احتياطية من جميع بيانات التطبيق وتنزيلها على جهازك.
                  </DialogDescription>
                </DialogHeader>
                <div className="flex items-center gap-2 p-4 bg-blue-50 rounded-lg">
                  <CheckCircle className="text-blue-600" size={20} />
                  <div className="text-sm">
                    <p className="font-medium">ستشمل النسخة الاحتياطية:</p>
                    <ul className="list-disc list-inside mt-1 text-muted-foreground">
                      <li>جميع المستخدمين</li>
                      <li>جميع العقارات</li>
                      <li>جميع المستخلصات</li>
                      <li>جميع المشتريات</li>
                      <li>جميع الموردين</li>
                      <li>جميع المبيعات</li>
                      <li>جميع طلبات الصيانة</li>
                    </ul>
                  </div>
                </div>
                <DialogFooter>
                  <Button 
                    onClick={handleCreateBackup} 
                    disabled={isLoading}
                    className="gap-2"
                  >
                    <Download size={16} />
                    {isLoading ? "جاري الإنشاء..." : "إنشاء وتنزيل"}
                  </Button>
                </DialogFooter>
              </DialogContent>
            </Dialog>

            {/* استعادة نسخة احتياطية */}
            <Dialog open={isRestoreDialogOpen} onOpenChange={setIsRestoreDialogOpen}>
              <DialogTrigger asChild>
                <Button className="w-full gap-2" variant="outline">
                  <Upload size={16} />
                  استعادة نسخة احتياطية
                </Button>
              </DialogTrigger>
              <DialogContent>
                <DialogHeader>
                  <DialogTitle>استعادة نسخة احتياطية</DialogTitle>
                  <DialogDescription>
                    اختر ملف النسخة الاحتياطية لاستعادة البيانات.
                  </DialogDescription>
                </DialogHeader>
                <div className="space-y-4">
                  <div className="flex items-center gap-2 p-4 bg-yellow-50 rounded-lg">
                    <AlertTriangle className="text-yellow-600" size={20} />
                    <div className="text-sm">
                      <p className="font-medium text-yellow-800">تحذير:</p>
                      <p className="text-yellow-700">
                        ستؤدي هذه العملية إلى استبدال جميع البيانات الحالية بالبيانات من النسخة الاحتياطية.
                      </p>
                    </div>
                  </div>
                  
                  <div className="space-y-2">
                    <Label htmlFor="backup-file">اختر ملف النسخة الاحتياطية</Label>
                    <Input
                      id="backup-file"
                      type="file"
                      accept=".json"
                      onChange={handleFileSelect}
                    />
                    {selectedFile && (
                      <p className="text-sm text-muted-foreground">
                        الملف المختار: {selectedFile.name}
                      </p>
                    )}
                  </div>
                </div>
                <DialogFooter>
                  <Button 
                    onClick={handleRestoreBackup} 
                    disabled={isLoading || !selectedFile}
                    variant="destructive"
                    className="gap-2"
                  >
                    <Upload size={16} />
                    {isLoading ? "جاري الاستعادة..." : "استعادة البيانات"}
                  </Button>
                </DialogFooter>
              </DialogContent>
            </Dialog>

            {/* معلومات قاعدة البيانات */}
            <Button 
              className="w-full gap-2" 
              variant="secondary"
              onClick={handleShowDatabaseInfo}
            >
              <Database size={16} />
              معلومات قاعدة البيانات
            </Button>
          </div>
        </CardContent>
      </Card>
    </div>
  );
};

export default BackupManager;
