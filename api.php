<?php
header('Content-Type: application/json; charset=utf-8');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, POST, PUT, DELETE, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type, Authorization');

// <PERSON>le preflight requests
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    http_response_code(200);
    exit();
}

// Data directory
$dataDir = __DIR__ . '/data';
if (!is_dir($dataDir)) {
    mkdir($dataDir, 0755, true);
}

// Get request data
$method = $_SERVER['REQUEST_METHOD'];
$input = json_decode(file_get_contents('php://input'), true);
$action = $_GET['action'] ?? '';
$type = $_GET['type'] ?? '';

// File paths
$files = [
    'properties' => $dataDir . '/properties.json',
    'users' => $dataDir . '/users.json',
    'invoices' => $dataDir . '/invoices.json',
    'suppliers' => $dataDir . '/suppliers.json',
    'procurement' => $dataDir . '/procurement.json'
];

// Helper functions
function readData($file) {
    if (!file_exists($file)) {
        return [];
    }
    $content = file_get_contents($file);
    return json_decode($content, true) ?: [];
}

function writeData($file, $data) {
    return file_put_contents($file, json_encode($data, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE));
}

function generateId() {
    return (string)(time() . rand(1000, 9999));
}

function sendResponse($data, $status = 200) {
    http_response_code($status);
    echo json_encode($data, JSON_UNESCAPED_UNICODE);
    exit();
}

function sendError($message, $status = 400) {
    sendResponse(['error' => $message], $status);
}

// Validate type
if (!isset($files[$type]) && $action !== 'backup' && $action !== 'restore') {
    sendError('نوع البيانات غير صحيح');
}

try {
    switch ($action) {
        case 'get':
            // Get all data of specific type
            $data = readData($files[$type]);
            sendResponse($data);
            break;

        case 'add':
            // Add new item
            if (!$input) {
                sendError('لا توجد بيانات للإضافة');
            }
            
            $data = readData($files[$type]);
            $input['id'] = generateId();
            $input['createdAt'] = date('c');
            $input['updatedAt'] = date('c');
            
            $data[] = $input;
            
            if (writeData($files[$type], $data)) {
                sendResponse(['success' => true, 'id' => $input['id'], 'message' => 'تم إضافة البيانات بنجاح']);
            } else {
                sendError('فشل في حفظ البيانات');
            }
            break;

        case 'update':
            // Update existing item
            if (!$input || !isset($input['id'])) {
                sendError('معرف العنصر مطلوب للتحديث');
            }
            
            $data = readData($files[$type]);
            $found = false;
            
            for ($i = 0; $i < count($data); $i++) {
                if ($data[$i]['id'] === $input['id']) {
                    $input['updatedAt'] = date('c');
                    $data[$i] = array_merge($data[$i], $input);
                    $found = true;
                    break;
                }
            }
            
            if (!$found) {
                sendError('العنصر غير موجود');
            }
            
            if (writeData($files[$type], $data)) {
                sendResponse(['success' => true, 'message' => 'تم تحديث البيانات بنجاح']);
            } else {
                sendError('فشل في حفظ البيانات');
            }
            break;

        case 'delete':
            // Delete item
            $id = $_GET['id'] ?? '';
            if (!$id) {
                sendError('معرف العنصر مطلوب للحذف');
            }
            
            $data = readData($files[$type]);
            $newData = array_filter($data, function($item) use ($id) {
                return $item['id'] !== $id;
            });
            
            if (count($newData) === count($data)) {
                sendError('العنصر غير موجود');
            }
            
            if (writeData($files[$type], array_values($newData))) {
                sendResponse(['success' => true, 'message' => 'تم حذف البيانات بنجاح']);
            } else {
                sendError('فشل في حذف البيانات');
            }
            break;

        case 'backup':
            // Create backup of all data
            $backup = [];
            foreach ($files as $key => $file) {
                $backup[$key] = readData($file);
            }
            
            $backupFile = $dataDir . '/backup_' . date('Y-m-d_H-i-s') . '.json';
            if (writeData($backupFile, $backup)) {
                sendResponse([
                    'success' => true, 
                    'message' => 'تم إنشاء النسخة الاحتياطية بنجاح',
                    'file' => basename($backupFile),
                    'data' => $backup
                ]);
            } else {
                sendError('فشل في إنشاء النسخة الاحتياطية');
            }
            break;

        case 'restore':
            // Restore from backup
            if (!$input) {
                sendError('بيانات الاستعادة مطلوبة');
            }
            
            $success = true;
            foreach ($files as $key => $file) {
                if (isset($input[$key])) {
                    if (!writeData($file, $input[$key])) {
                        $success = false;
                        break;
                    }
                }
            }
            
            if ($success) {
                sendResponse(['success' => true, 'message' => 'تم استعادة البيانات بنجاح']);
            } else {
                sendError('فشل في استعادة البيانات');
            }
            break;

        case 'clear':
            // Clear all data
            $success = true;
            foreach ($files as $file) {
                if (!writeData($file, [])) {
                    $success = false;
                    break;
                }
            }
            
            if ($success) {
                sendResponse(['success' => true, 'message' => 'تم مسح جميع البيانات بنجاح']);
            } else {
                sendError('فشل في مسح البيانات');
            }
            break;

        case 'stats':
            // Get statistics
            $stats = [];
            foreach ($files as $key => $file) {
                $data = readData($file);
                $stats[$key] = count($data);
            }
            sendResponse($stats);
            break;

        default:
            sendError('عملية غير مدعومة');
    }

} catch (Exception $e) {
    sendError('خطأ في الخادم: ' . $e->getMessage(), 500);
}
?>
