
import React from "react";
import StatCard from "./StatCard";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import ProjectsTable from "./ProjectsTable";
import InvoicesTable from "./InvoicesTable";
import MaintenanceRequests from "./MaintenanceRequests";
import {
  Tabs,
  Ta<PERSON>Content,
  TabsList,
  TabsTrigger,
} from "@/components/ui/tabs";
import {
  Building2,
  FileText,
  Activity,
  AlertTriangle,
  BarChart,
} from "lucide-react";
import { LineChart, Line, XAxis, YAxis, CartesianGrid, Tooltip, ResponsiveContainer } from 'recharts';

const salesData = [
  { month: 'يناير', sales: 8 },
  { month: 'فبراير', sales: 12 },
  { month: 'مارس', sales: 7 },
  { month: 'أبريل', sales: 14 },
  { month: 'مايو', sales: 18 },
];

const Dashboard: React.FC = () => {
  return (
    <div className="space-y-6">
      <div className="stats-container">
        <StatCard
          title="الوحدات المباعة"
          value="104"
          description="من أصل 214 وحدة"
          icon={<Building2 size={18} />}
          trend="up"
          trendValue="12% زيادة عن الشهر السابق"
        />
        <StatCard
          title="المشاريع الحالية"
          value="3"
          description="1 مكتمل, 1 مخطط"
          icon={<Activity size={18} />}
        />
        <StatCard
          title="مستخلصات المقاولين"
          value="840,000 ريال"
          description="4 مستخلصات، 2 بانتظار الموافقة"
          icon={<FileText size={18} />}
          trend="down"
          trendValue="5% انخفاض عن الشهر السابق"
        />
        <StatCard
          title="طلبات الصيانة"
          value="7"
          description="3 مفتوحة، 4 تم حلها"
          icon={<AlertTriangle size={18} />}
        />
      </div>

      <div className="grid grid-cols-1 xl:grid-cols-3 gap-6">
        <Card className="xl:col-span-3">
          <CardHeader>
            <CardTitle>المشاريع</CardTitle>
          </CardHeader>
          <CardContent>
            <ProjectsTable />
          </CardContent>
        </Card>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center">
              <BarChart size={18} className="ml-2" /> مبيعات الوحدات (2025)
            </CardTitle>
          </CardHeader>
          <CardContent className="h-80">
            <ResponsiveContainer width="100%" height="100%">
              <LineChart data={salesData}>
                <CartesianGrid strokeDasharray="3 3" />
                <XAxis dataKey="month" />
                <YAxis />
                <Tooltip />
                <Line type="monotone" dataKey="sales" stroke="#1e4976" strokeWidth={2} />
              </LineChart>
            </ResponsiveContainer>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle>آخر مستخلصات المقاولين</CardTitle>
          </CardHeader>
          <CardContent>
            <InvoicesTable />
          </CardContent>
        </Card>
      </div>

      <Tabs defaultValue="maintenance">
        <TabsList>
          <TabsTrigger value="maintenance">طلبات الصيانة</TabsTrigger>
          <TabsTrigger value="electricity">فواتير الكهرباء</TabsTrigger>
          <TabsTrigger value="notices">إشعارات</TabsTrigger>
        </TabsList>
        <TabsContent value="maintenance">
          <Card>
            <CardHeader>
              <CardTitle>طلبات الصيانة</CardTitle>
            </CardHeader>
            <CardContent>
              <MaintenanceRequests />
            </CardContent>
          </Card>
        </TabsContent>
        <TabsContent value="electricity">
          <Card>
            <CardHeader>
              <CardTitle>فواتير الكهرباء</CardTitle>
            </CardHeader>
            <CardContent>
              <p className="text-center p-4 text-muted-foreground">
                لا توجد فواتير كهرباء مستحقة حالياً
              </p>
            </CardContent>
          </Card>
        </TabsContent>
        <TabsContent value="notices">
          <Card>
            <CardHeader>
              <CardTitle>الإشعارات</CardTitle>
            </CardHeader>
            <CardContent>
              <p className="text-center p-4 text-muted-foreground">
                لا توجد إشعارات جديدة
              </p>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
};

export default Dashboard;
