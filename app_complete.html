<!DOCTYPE html>
<html lang="ar">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>نظام إدارة العقارات - رافعة المدينة</title>
    <link href="https://fonts.googleapis.com/css2?family=Tajawal:wght@300;400;500;700&display=swap" rel="stylesheet">
    <style>
        * { margin: 0; padding: 0; box-sizing: border-box; }
        body { font-family: 'Tajawal', sans-serif; direction: rtl; background: #f5f5f5; color: #333; }
        .header { background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; padding: 2rem; text-align: center; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }
        .header h1 { font-size: 2.5rem; margin-bottom: 0.5rem; }
        .header p { font-size: 1.2rem; opacity: 0.9; }
        .nav-tabs { background: white; padding: 1rem; box-shadow: 0 2px 5px rgba(0,0,0,0.1); display: flex; gap: 1rem; overflow-x: auto; }
        .nav-tab { padding: 0.8rem 1.5rem; background: #f8f9fa; border: none; border-radius: 8px; cursor: pointer; transition: all 0.3s; white-space: nowrap; font-family: inherit; font-size: 1rem; }
        .nav-tab.active { background: #667eea; color: white; }
        .nav-tab:hover { background: #5a67d8; color: white; }
        .content { padding: 2rem; max-width: 1200px; margin: 0 auto; }
        .page { display: none; animation: fadeIn 0.3s ease-in; }
        .page.active { display: block; }
        @keyframes fadeIn { from { opacity: 0; transform: translateY(20px); } to { opacity: 1; transform: translateY(0); } }
        .stats-grid { display: grid; grid-template-columns: repeat(auto-fit, minmax(250px, 1fr)); gap: 1.5rem; margin-bottom: 2rem; }
        .stat-card { background: white; padding: 1.5rem; border-radius: 12px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); text-align: center; }
        .stat-card h3 { color: #667eea; font-size: 2rem; margin-bottom: 0.5rem; }
        .stat-card p { color: #666; font-size: 1rem; }
        .form-container { background: white; padding: 2rem; border-radius: 12px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); margin-bottom: 2rem; }
        .form-grid { display: grid; grid-template-columns: repeat(auto-fit, minmax(250px, 1fr)); gap: 1rem; margin-bottom: 1.5rem; }
        .form-group { display: flex; flex-direction: column; }
        .form-group label { margin-bottom: 0.5rem; font-weight: 500; color: #333; }
        .form-group input, .form-group select, .form-group textarea { padding: 0.8rem; border: 2px solid #e2e8f0; border-radius: 8px; font-family: inherit; font-size: 1rem; transition: border-color 0.3s; }
        .form-group input:focus, .form-group select:focus, .form-group textarea:focus { outline: none; border-color: #667eea; }
        .btn { padding: 0.8rem 1.5rem; border: none; border-radius: 8px; cursor: pointer; font-family: inherit; font-size: 1rem; transition: all 0.3s; }
        .btn-primary { background: #667eea; color: white; }
        .btn-primary:hover { background: #5a67d8; }
        .btn-danger { background: #e53e3e; color: white; }
        .btn-danger:hover { background: #c53030; }
        .btn-secondary { background: #718096; color: white; }
        .btn-secondary:hover { background: #4a5568; }
        .table-container { background: white; border-radius: 12px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); overflow: hidden; }
        .table { width: 100%; border-collapse: collapse; }
        .table th, .table td { padding: 1rem; text-align: right; border-bottom: 1px solid #e2e8f0; }
        .table th { background: #f8f9fa; font-weight: 600; color: #4a5568; }
        .table tr:hover { background: #f8f9fa; }
        .badge { padding: 0.3rem 0.8rem; border-radius: 20px; font-size: 0.8rem; font-weight: 500; }
        .badge-success { background: #c6f6d5; color: #22543d; }
        .badge-warning { background: #fef5e7; color: #744210; }
        .badge-danger { background: #fed7d7; color: #742a2a; }
        .welcome-grid { display: grid; grid-template-columns: repeat(auto-fit, minmax(300px, 1fr)); gap: 2rem; margin-top: 2rem; }
        .welcome-card { background: white; padding: 2rem; border-radius: 15px; box-shadow: 0 5px 20px rgba(0,0,0,0.1); text-align: center; cursor: pointer; transition: transform 0.3s ease; }
        .welcome-card:hover { transform: translateY(-5px); }
        .welcome-card-icon { width: 60px; height: 60px; margin: 0 auto 1rem; border-radius: 50%; display: flex; align-items: center; justify-content: center; font-size: 1.5rem; }
        .alert { padding: 1rem; border-radius: 8px; margin-bottom: 1rem; }
        .alert-success { background: #c6f6d5; color: #22543d; border: 1px solid #9ae6b4; }
        .alert-info { background: #bee3f8; color: #2a4365; border: 1px solid #90cdf4; }
        .empty-state { text-align: center; padding: 3rem; color: #718096; }
        @media (max-width: 768px) { .header h1 { font-size: 2rem; } .nav-tabs { padding: 0.5rem; } .content { padding: 1rem; } .form-grid { grid-template-columns: 1fr; } }
    </style>
</head>
<body>
    <div class="header">
        <h1>🏢 نظام إدارة العقارات</h1>
        <p>شركة رافعة المدينة للتطوير العقاري</p>
    </div>

    <div class="nav-tabs">
        <button class="nav-tab active" onclick="showPage('dashboard')">🏠 الرئيسية</button>
        <button class="nav-tab" onclick="showPage('properties')">🏢 العقارات</button>
        <button class="nav-tab" onclick="showPage('users')">👥 المستخدمين</button>
        <button class="nav-tab" onclick="showPage('invoices')">📄 المستخلصات</button>
        <button class="nav-tab" onclick="showPage('suppliers')">🏭 الموردين</button>
        <button class="nav-tab" onclick="showPage('procurement')">🛒 المشتريات</button>
        <button class="nav-tab" onclick="showPage('settings')">⚙️ الإعدادات</button>
    </div>

    <div class="content">
        <!-- Dashboard -->
        <div id="dashboard" class="page active">
            <div class="alert alert-success">
                <strong>مرحباً بك!</strong> نظام إدارة العقارات يعمل بشكل مثالي. جميع البيانات محفوظة محلياً في متصفحك.
            </div>
            <div class="stats-grid">
                <div class="stat-card"><h3 id="total-properties">0</h3><p>إجمالي العقارات</p></div>
                <div class="stat-card"><h3 id="total-users">0</h3><p>المستخدمين</p></div>
                <div class="stat-card"><h3 id="total-invoices">0</h3><p>المستخلصات</p></div>
                <div class="stat-card"><h3 id="total-suppliers">0</h3><p>الموردين</p></div>
            </div>
            <div class="welcome-grid">
                <div class="welcome-card" onclick="showPage('properties')">
                    <div class="welcome-card-icon" style="background: linear-gradient(135deg, #667eea, #764ba2); color: white;">🏢</div>
                    <h3>إدارة العقارات</h3><p>إضافة وإدارة العقارات والوحدات السكنية والتجارية</p>
                </div>
                <div class="welcome-card" onclick="showPage('users')">
                    <div class="welcome-card-icon" style="background: linear-gradient(135deg, #4facfe, #00f2fe); color: white;">👥</div>
                    <h3>إدارة المستخدمين</h3><p>إضافة وإدارة حسابات المستخدمين والصلاحيات</p>
                </div>
                <div class="welcome-card" onclick="showPage('invoices')">
                    <div class="welcome-card-icon" style="background: linear-gradient(135deg, #fa709a, #fee140); color: white;">📄</div>
                    <h3>المستخلصات</h3><p>إدارة المستخلصات المالية والفواتير</p>
                </div>
            </div>
        </div>

        <!-- Properties -->
        <div id="properties" class="page">
            <h2>إدارة العقارات</h2>
            <div class="form-container">
                <h3>إضافة عقار جديد</h3>
                <form id="property-form">
                    <div class="form-grid">
                        <div class="form-group"><label>عنوان العقار *</label><input type="text" id="property-title" required></div>
                        <div class="form-group"><label>نوع العقار</label><select id="property-type"><option value="شقة">شقة</option><option value="فيلا">فيلا</option><option value="محل تجاري">محل تجاري</option><option value="مكتب">مكتب</option><option value="أرض">أرض</option></select></div>
                        <div class="form-group"><label>الموقع</label><input type="text" id="property-location"></div>
                        <div class="form-group"><label>المساحة (م²)</label><input type="number" id="property-area"></div>
                        <div class="form-group"><label>السعر (ريال)</label><input type="number" id="property-price"></div>
                        <div class="form-group"><label>الحالة</label><select id="property-status"><option value="متاح">متاح</option><option value="محجوز">محجوز</option><option value="مباع">مباع</option></select></div>
                    </div>
                    <div class="form-group"><label>الوصف</label><textarea id="property-description" rows="3"></textarea></div>
                    <button type="submit" class="btn btn-primary">إضافة العقار</button>
                </form>
            </div>
            <div class="table-container">
                <table class="table">
                    <thead><tr><th>العنوان</th><th>النوع</th><th>الموقع</th><th>المساحة</th><th>السعر</th><th>الحالة</th><th>الإجراءات</th></tr></thead>
                    <tbody id="properties-table"></tbody>
                </table>
            </div>
        </div>

        <!-- Users -->
        <div id="users" class="page">
            <h2>إدارة المستخدمين</h2>
            <div class="form-container">
                <h3>إضافة مستخدم جديد</h3>
                <form id="user-form">
                    <div class="form-grid">
                        <div class="form-group"><label>الاسم *</label><input type="text" id="user-name" required></div>
                        <div class="form-group"><label>البريد الإلكتروني *</label><input type="email" id="user-email" required></div>
                        <div class="form-group"><label>رقم الهاتف</label><input type="tel" id="user-phone"></div>
                        <div class="form-group"><label>الدور الوظيفي</label><select id="user-role"><option value="مدير">مدير</option><option value="موظف مبيعات">موظف مبيعات</option><option value="محاسب">محاسب</option><option value="مشرف">مشرف</option></select></div>
                        <div class="form-group"><label>القسم</label><input type="text" id="user-department"></div>
                        <div class="form-group"><label>الحالة</label><select id="user-status"><option value="نشط">نشط</option><option value="غير نشط">غير نشط</option></select></div>
                    </div>
                    <button type="submit" class="btn btn-primary">إضافة المستخدم</button>
                </form>
            </div>
            <div class="table-container">
                <table class="table">
                    <thead><tr><th>الاسم</th><th>البريد الإلكتروني</th><th>الهاتف</th><th>الدور</th><th>القسم</th><th>الحالة</th><th>الإجراءات</th></tr></thead>
                    <tbody id="users-table"></tbody>
                </table>
            </div>
        </div>

        <!-- Invoices -->
        <div id="invoices" class="page">
            <h2>إدارة المستخلصات</h2>
            <div class="form-container">
                <h3>إضافة مستخلص جديد</h3>
                <form id="invoice-form">
                    <div class="form-grid">
                        <div class="form-group"><label>اسم العميل *</label><input type="text" id="invoice-customer" required></div>
                        <div class="form-group"><label>هاتف العميل</label><input type="tel" id="invoice-phone"></div>
                        <div class="form-group"><label>العقار</label><input type="text" id="invoice-property"></div>
                        <div class="form-group"><label>المبلغ (ريال) *</label><input type="number" id="invoice-amount" required></div>
                        <div class="form-group"><label>تاريخ الاستحقاق</label><input type="date" id="invoice-due-date"></div>
                        <div class="form-group"><label>الحالة</label><select id="invoice-status"><option value="معلق">معلق</option><option value="مدفوع">مدفوع</option><option value="مرفوض">مرفوض</option></select></div>
                    </div>
                    <button type="submit" class="btn btn-primary">إضافة المستخلص</button>
                </form>
            </div>
            <div class="table-container">
                <table class="table">
                    <thead><tr><th>رقم المستخلص</th><th>العميل</th><th>العقار</th><th>المبلغ</th><th>تاريخ الاستحقاق</th><th>الحالة</th><th>الإجراءات</th></tr></thead>
                    <tbody id="invoices-table"></tbody>
                </table>
            </div>
        </div>

        <!-- Suppliers -->
        <div id="suppliers" class="page">
            <h2>إدارة الموردين</h2>
            <div class="form-container">
                <h3>إضافة مورد جديد</h3>
                <form id="supplier-form">
                    <div class="form-grid">
                        <div class="form-group"><label>اسم المورد *</label><input type="text" id="supplier-name" required></div>
                        <div class="form-group"><label>البريد الإلكتروني</label><input type="email" id="supplier-email"></div>
                        <div class="form-group"><label>رقم الهاتف</label><input type="tel" id="supplier-phone"></div>
                        <div class="form-group"><label>العنوان</label><input type="text" id="supplier-address"></div>
                        <div class="form-group"><label>الفئة</label><select id="supplier-category"><option value="مواد البناء">مواد البناء</option><option value="كهرباء">كهرباء</option><option value="سباكة">سباكة</option><option value="دهانات">دهانات</option><option value="رخام وسيراميك">رخام وسيراميك</option><option value="مقاولات">مقاولات</option></select></div>
                        <div class="form-group"><label>الحالة</label><select id="supplier-status"><option value="نشط">نشط</option><option value="غير نشط">غير نشط</option></select></div>
                    </div>
                    <button type="submit" class="btn btn-primary">إضافة المورد</button>
                </form>
            </div>
            <div class="table-container">
                <table class="table">
                    <thead><tr><th>الاسم</th><th>الفئة</th><th>الهاتف</th><th>البريد الإلكتروني</th><th>الحالة</th><th>الإجراءات</th></tr></thead>
                    <tbody id="suppliers-table"></tbody>
                </table>
            </div>
        </div>

        <!-- Procurement -->
        <div id="procurement" class="page">
            <h2>إدارة المشتريات</h2>
            <div class="form-container">
                <h3>إضافة عنصر جديد</h3>
                <form id="procurement-form">
                    <div class="form-grid">
                        <div class="form-group"><label>اسم العنصر *</label><input type="text" id="procurement-item" required></div>
                        <div class="form-group"><label>الفئة</label><select id="procurement-category"><option value="مواد البناء">مواد البناء</option><option value="كهرباء">كهرباء</option><option value="سباكة">سباكة</option><option value="دهانات">دهانات</option><option value="أدوات">أدوات</option><option value="أخرى">أخرى</option></select></div>
                        <div class="form-group"><label>المورد</label><input type="text" id="procurement-supplier"></div>
                        <div class="form-group"><label>الكمية *</label><input type="number" id="procurement-quantity" required></div>
                        <div class="form-group"><label>سعر الوحدة</label><input type="number" step="0.01" id="procurement-price"></div>
                        <div class="form-group"><label>الحالة</label><select id="procurement-status"><option value="معلق">معلق</option><option value="قيد التنفيذ">قيد التنفيذ</option><option value="تم التسليم">تم التسليم</option></select></div>
                    </div>
                    <button type="submit" class="btn btn-primary">إضافة العنصر</button>
                </form>
            </div>
            <div class="table-container">
                <table class="table">
                    <thead><tr><th>العنصر</th><th>الفئة</th><th>المورد</th><th>الكمية</th><th>السعر الإجمالي</th><th>الحالة</th><th>الإجراءات</th></tr></thead>
                    <tbody id="procurement-table"></tbody>
                </table>
            </div>
        </div>

        <!-- Settings -->
        <div id="settings" class="page">
            <h2>الإعدادات</h2>
            <div class="form-container">
                <h3>إدارة البيانات</h3>
                <div class="form-grid">
                    <div class="form-group"><label>تصدير البيانات</label><button type="button" class="btn btn-secondary" onclick="exportData()">تصدير جميع البيانات</button></div>
                    <div class="form-group"><label>استيراد البيانات</label><input type="file" id="import-file" accept=".json"><button type="button" class="btn btn-secondary" onclick="importData()">استيراد البيانات</button></div>
                    <div class="form-group"><label>مسح جميع البيانات</label><button type="button" class="btn btn-danger" onclick="clearAllData()">مسح جميع البيانات</button></div>
                </div>
            </div>
            <div class="form-container">
                <h3>معلومات النظام</h3>
                <div class="alert alert-info">
                    <strong>إصدار النظام:</strong> 2.0.0<br>
                    <strong>تاريخ آخر تحديث:</strong> <span id="last-update"></span><br>
                    <strong>عدد العقارات:</strong> <span id="settings-properties">0</span><br>
                    <strong>عدد المستخدمين:</strong> <span id="settings-users">0</span><br>
                    <strong>عدد المستخلصات:</strong> <span id="settings-invoices">0</span><br>
                    <strong>عدد الموردين:</strong> <span id="settings-suppliers">0</span><br>
                    <strong>عدد المشتريات:</strong> <span id="settings-procurement">0</span>
                </div>
            </div>
        </div>
    </div>

    <script>
        // Data Storage
        const storage = {
            get: (key) => JSON.parse(localStorage.getItem(key) || '[]'),
            set: (key, data) => localStorage.setItem(key, JSON.stringify(data)),
            generateId: () => Date.now().toString()
        };

        // Initialize data
        let properties = storage.get('properties');
        let users = storage.get('users');
        let invoices = storage.get('invoices');
        let suppliers = storage.get('suppliers');
        let procurement = storage.get('procurement');

        // Navigation
        function showPage(pageId) {
            document.querySelectorAll('.page').forEach(page => page.classList.remove('active'));
            document.querySelectorAll('.nav-tab').forEach(tab => tab.classList.remove('active'));
            document.getElementById(pageId).classList.add('active');
            event.target.classList.add('active');
            loadPageData(pageId);
        }

        function loadPageData(pageId) {
            switch(pageId) {
                case 'dashboard': loadDashboard(); break;
                case 'properties': loadProperties(); break;
                case 'users': loadUsers(); break;
                case 'invoices': loadInvoices(); break;
                case 'suppliers': loadSuppliers(); break;
                case 'procurement': loadProcurement(); break;
                case 'settings': loadSettings(); break;
            }
        }

        // Dashboard
        function loadDashboard() {
            document.getElementById('total-properties').textContent = properties.length;
            document.getElementById('total-users').textContent = users.length;
            document.getElementById('total-invoices').textContent = invoices.length;
            document.getElementById('total-suppliers').textContent = suppliers.length;
        }

        // Properties
        function loadProperties() {
            const tbody = document.getElementById('properties-table');
            tbody.innerHTML = properties.map(property => `
                <tr>
                    <td>${property.title}</td>
                    <td>${property.type}</td>
                    <td>${property.location}</td>
                    <td>${property.area} م²</td>
                    <td>${property.price} ريال</td>
                    <td><span class="badge badge-${property.status === 'متاح' ? 'success' : property.status === 'محجوز' ? 'warning' : 'danger'}">${property.status}</span></td>
                    <td><button class="btn btn-danger" onclick="deleteProperty('${property.id}')">حذف</button></td>
                </tr>
            `).join('');
        }

        document.getElementById('property-form').addEventListener('submit', function(e) {
            e.preventDefault();
            const property = {
                id: storage.generateId(),
                title: document.getElementById('property-title').value,
                type: document.getElementById('property-type').value,
                location: document.getElementById('property-location').value,
                area: document.getElementById('property-area').value,
                price: document.getElementById('property-price').value,
                status: document.getElementById('property-status').value,
                description: document.getElementById('property-description').value,
                createdAt: new Date().toISOString()
            };
            properties.push(property);
            storage.set('properties', properties);
            this.reset();
            loadProperties();
            showNotification('تم إضافة العقار بنجاح');
        });

        function deleteProperty(id) {
            if (confirm('هل أنت متأكد من حذف هذا العقار؟')) {
                properties = properties.filter(p => p.id !== id);
                storage.set('properties', properties);
                loadProperties();
                showNotification('تم حذف العقار بنجاح');
            }
        }

        // Users
        function loadUsers() {
            const tbody = document.getElementById('users-table');
            tbody.innerHTML = users.map(user => `
                <tr>
                    <td>${user.name}</td>
                    <td>${user.email}</td>
                    <td>${user.phone || 'غير محدد'}</td>
                    <td>${user.role}</td>
                    <td>${user.department}</td>
                    <td><span class="badge badge-${user.status === 'نشط' ? 'success' : 'danger'}">${user.status}</span></td>
                    <td><button class="btn btn-danger" onclick="deleteUser('${user.id}')">حذف</button></td>
                </tr>
            `).join('');
        }

        document.getElementById('user-form').addEventListener('submit', function(e) {
            e.preventDefault();
            const user = {
                id: storage.generateId(),
                name: document.getElementById('user-name').value,
                email: document.getElementById('user-email').value,
                phone: document.getElementById('user-phone').value,
                role: document.getElementById('user-role').value,
                department: document.getElementById('user-department').value,
                status: document.getElementById('user-status').value,
                createdAt: new Date().toISOString()
            };
            users.push(user);
            storage.set('users', users);
            this.reset();
            loadUsers();
            showNotification('تم إضافة المستخدم بنجاح');
        });

        function deleteUser(id) {
            if (confirm('هل أنت متأكد من حذف هذا المستخدم؟')) {
                users = users.filter(u => u.id !== id);
                storage.set('users', users);
                loadUsers();
                showNotification('تم حذف المستخدم بنجاح');
            }
        }

        // Invoices
        function loadInvoices() {
            const tbody = document.getElementById('invoices-table');
            tbody.innerHTML = invoices.map(invoice => `
                <tr>
                    <td>#${invoice.id}</td>
                    <td>${invoice.customer}</td>
                    <td>${invoice.property}</td>
                    <td>${invoice.amount} ريال</td>
                    <td>${invoice.dueDate}</td>
                    <td><span class="badge badge-${invoice.status === 'مدفوع' ? 'success' : invoice.status === 'معلق' ? 'warning' : 'danger'}">${invoice.status}</span></td>
                    <td><button class="btn btn-danger" onclick="deleteInvoice('${invoice.id}')">حذف</button></td>
                </tr>
            `).join('');
        }

        document.getElementById('invoice-form').addEventListener('submit', function(e) {
            e.preventDefault();
            const invoice = {
                id: storage.generateId(),
                customer: document.getElementById('invoice-customer').value,
                phone: document.getElementById('invoice-phone').value,
                property: document.getElementById('invoice-property').value,
                amount: document.getElementById('invoice-amount').value,
                dueDate: document.getElementById('invoice-due-date').value,
                status: document.getElementById('invoice-status').value,
                createdAt: new Date().toISOString()
            };
            invoices.push(invoice);
            storage.set('invoices', invoices);
            this.reset();
            loadInvoices();
            showNotification('تم إضافة المستخلص بنجاح');
        });

        function deleteInvoice(id) {
            if (confirm('هل أنت متأكد من حذف هذا المستخلص؟')) {
                invoices = invoices.filter(i => i.id !== id);
                storage.set('invoices', invoices);
                loadInvoices();
                showNotification('تم حذف المستخلص بنجاح');
            }
        }

        // Suppliers
        function loadSuppliers() {
            const tbody = document.getElementById('suppliers-table');
            tbody.innerHTML = suppliers.map(supplier => `
                <tr>
                    <td>${supplier.name}</td>
                    <td>${supplier.category}</td>
                    <td>${supplier.phone || 'غير محدد'}</td>
                    <td>${supplier.email || 'غير محدد'}</td>
                    <td><span class="badge badge-${supplier.status === 'نشط' ? 'success' : 'danger'}">${supplier.status}</span></td>
                    <td><button class="btn btn-danger" onclick="deleteSupplier('${supplier.id}')">حذف</button></td>
                </tr>
            `).join('');
        }

        document.getElementById('supplier-form').addEventListener('submit', function(e) {
            e.preventDefault();
            const supplier = {
                id: storage.generateId(),
                name: document.getElementById('supplier-name').value,
                email: document.getElementById('supplier-email').value,
                phone: document.getElementById('supplier-phone').value,
                address: document.getElementById('supplier-address').value,
                category: document.getElementById('supplier-category').value,
                status: document.getElementById('supplier-status').value,
                createdAt: new Date().toISOString()
            };
            suppliers.push(supplier);
            storage.set('suppliers', suppliers);
            this.reset();
            loadSuppliers();
            showNotification('تم إضافة المورد بنجاح');
        });

        function deleteSupplier(id) {
            if (confirm('هل أنت متأكد من حذف هذا المورد؟')) {
                suppliers = suppliers.filter(s => s.id !== id);
                storage.set('suppliers', suppliers);
                loadSuppliers();
                showNotification('تم حذف المورد بنجاح');
            }
        }

        // Procurement
        function loadProcurement() {
            const tbody = document.getElementById('procurement-table');
            tbody.innerHTML = procurement.map(item => {
                const total = (parseFloat(item.quantity) * parseFloat(item.price || 0)).toFixed(2);
                return `
                    <tr>
                        <td>${item.itemName}</td>
                        <td>${item.category}</td>
                        <td>${item.supplier}</td>
                        <td>${item.quantity}</td>
                        <td>${total} ريال</td>
                        <td><span class="badge badge-${item.status === 'تم التسليم' ? 'success' : item.status === 'قيد التنفيذ' ? 'warning' : 'danger'}">${item.status}</span></td>
                        <td><button class="btn btn-danger" onclick="deleteProcurement('${item.id}')">حذف</button></td>
                    </tr>
                `;
            }).join('');
        }

        document.getElementById('procurement-form').addEventListener('submit', function(e) {
            e.preventDefault();
            const item = {
                id: storage.generateId(),
                itemName: document.getElementById('procurement-item').value,
                category: document.getElementById('procurement-category').value,
                supplier: document.getElementById('procurement-supplier').value,
                quantity: document.getElementById('procurement-quantity').value,
                price: document.getElementById('procurement-price').value,
                status: document.getElementById('procurement-status').value,
                createdAt: new Date().toISOString()
            };
            procurement.push(item);
            storage.set('procurement', procurement);
            this.reset();
            loadProcurement();
            showNotification('تم إضافة العنصر بنجاح');
        });

        function deleteProcurement(id) {
            if (confirm('هل أنت متأكد من حذف هذا العنصر؟')) {
                procurement = procurement.filter(p => p.id !== id);
                storage.set('procurement', procurement);
                loadProcurement();
                showNotification('تم حذف العنصر بنجاح');
            }
        }

        // Settings
        function loadSettings() {
            document.getElementById('last-update').textContent = new Date().toLocaleDateString('ar-SA');
            document.getElementById('settings-properties').textContent = properties.length;
            document.getElementById('settings-users').textContent = users.length;
            document.getElementById('settings-invoices').textContent = invoices.length;
            document.getElementById('settings-suppliers').textContent = suppliers.length;
            document.getElementById('settings-procurement').textContent = procurement.length;
        }

        function exportData() {
            const data = { properties, users, invoices, suppliers, procurement };
            const blob = new Blob([JSON.stringify(data, null, 2)], { type: 'application/json' });
            const url = URL.createObjectURL(blob);
            const a = document.createElement('a');
            a.href = url;
            a.download = `rafea-data-${new Date().toISOString().split('T')[0]}.json`;
            a.click();
            showNotification('تم تصدير البيانات بنجاح');
        }

        function importData() {
            const file = document.getElementById('import-file').files[0];
            if (!file) return;
            const reader = new FileReader();
            reader.onload = function(e) {
                try {
                    const data = JSON.parse(e.target.result);
                    if (data.properties) { properties = data.properties; storage.set('properties', properties); }
                    if (data.users) { users = data.users; storage.set('users', users); }
                    if (data.invoices) { invoices = data.invoices; storage.set('invoices', invoices); }
                    if (data.suppliers) { suppliers = data.suppliers; storage.set('suppliers', suppliers); }
                    if (data.procurement) { procurement = data.procurement; storage.set('procurement', procurement); }
                    showNotification('تم استيراد البيانات بنجاح');
                    loadDashboard();
                } catch (error) {
                    showNotification('خطأ في استيراد البيانات', 'error');
                }
            };
            reader.readAsText(file);
        }

        function clearAllData() {
            if (confirm('هل أنت متأكد من مسح جميع البيانات؟ هذا الإجراء لا يمكن التراجع عنه!')) {
                localStorage.clear();
                properties = []; users = []; invoices = []; suppliers = []; procurement = [];
                showNotification('تم مسح جميع البيانات');
                loadDashboard();
            }
        }

        function showNotification(message, type = 'success') {
            const notification = document.createElement('div');
            notification.className = `alert alert-${type}`;
            notification.textContent = message;
            notification.style.position = 'fixed';
            notification.style.top = '20px';
            notification.style.right = '20px';
            notification.style.zIndex = '1000';
            notification.style.minWidth = '300px';
            document.body.appendChild(notification);
            setTimeout(() => notification.remove(), 3000);
        }

        // Initialize
        document.addEventListener('DOMContentLoaded', function() {
            loadDashboard();
            showNotification('مرحباً بك في نظام إدارة العقارات!');
        });
    </script>
</body>
</html>
