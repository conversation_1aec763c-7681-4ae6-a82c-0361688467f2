import { database, Property } from '@/lib/database';

export class PropertyService {
  private storeName = 'properties';

  // إضافة عقار جديد
  async addProperty(propertyData: Omit<Property, 'createdAt'>): Promise<string> {
    const property: Property = {
      ...propertyData,
      createdAt: new Date().toISOString(),
    };
    
    await database.add(this.storeName, property);
    return property.id;
  }

  // تحديث عقار
  async updateProperty(property: Property): Promise<void> {
    await database.update(this.storeName, property);
  }

  // حذف عقار
  async deleteProperty(id: string): Promise<void> {
    await database.delete(this.storeName, id);
  }

  // الحصول على عقار واحد
  async getProperty(id: string): Promise<Property | null> {
    return await database.get(this.storeName, id);
  }

  // الحصول على جميع العقارات
  async getAllProperties(): Promise<Property[]> {
    return await database.getAll(this.storeName);
  }

  // البحث عن العقارات بالعنوان
  async getPropertiesByTitle(title: string): Promise<Property[]> {
    return await database.searchByIndex(this.storeName, 'title', title);
  }

  // الحصول على العقارات حسب النوع
  async getPropertiesByType(type: string): Promise<Property[]> {
    return await database.searchByIndex(this.storeName, 'type', type);
  }

  // الحصول على العقارات حسب الحالة
  async getPropertiesByStatus(status: string): Promise<Property[]> {
    return await database.searchByIndex(this.storeName, 'status', status);
  }

  // الحصول على العقارات المتاحة
  async getAvailableProperties(): Promise<Property[]> {
    return await this.getPropertiesByStatus('متاح');
  }

  // الحصول على العقارات المباعة
  async getSoldProperties(): Promise<Property[]> {
    return await this.getPropertiesByStatus('تم البيع');
  }

  // البحث في العقارات
  async searchProperties(query: string): Promise<Property[]> {
    const allProperties = await this.getAllProperties();
    const lowerQuery = query.toLowerCase();
    
    return allProperties.filter(property => 
      property.title.toLowerCase().includes(lowerQuery) ||
      property.location.toLowerCase().includes(lowerQuery) ||
      property.type.toLowerCase().includes(lowerQuery) ||
      property.id.toLowerCase().includes(lowerQuery)
    );
  }

  // تصفية العقارات
  async filterProperties(filters: {
    type?: string;
    status?: string;
    minPrice?: number;
    maxPrice?: number;
    minArea?: number;
    maxArea?: number;
    bedrooms?: number;
    bathrooms?: number;
  }): Promise<Property[]> {
    const allProperties = await this.getAllProperties();
    
    return allProperties.filter(property => {
      if (filters.type && property.type !== filters.type) return false;
      if (filters.status && property.status !== filters.status) return false;
      
      const price = parseFloat(property.price.replace(/,/g, ''));
      if (filters.minPrice && price < filters.minPrice) return false;
      if (filters.maxPrice && price > filters.maxPrice) return false;
      
      if (filters.minArea && property.area < filters.minArea) return false;
      if (filters.maxArea && property.area > filters.maxArea) return false;
      
      if (filters.bedrooms && property.bedrooms !== filters.bedrooms) return false;
      if (filters.bathrooms && property.bathrooms !== filters.bathrooms) return false;
      
      return true;
    });
  }

  // إحصائيات العقارات
  async getPropertyStats(): Promise<{
    total: number;
    available: number;
    sold: number;
    pending: number;
    byType: Record<string, number>;
    byStatus: Record<string, number>;
    totalValue: number;
    averagePrice: number;
  }> {
    const properties = await this.getAllProperties();
    
    const stats = {
      total: properties.length,
      available: properties.filter(p => p.status === 'متاح').length,
      sold: properties.filter(p => p.status === 'تم البيع').length,
      pending: properties.filter(p => p.status === 'تحت العرض').length,
      byType: {} as Record<string, number>,
      byStatus: {} as Record<string, number>,
      totalValue: 0,
      averagePrice: 0,
    };

    // إحصائيات حسب النوع
    properties.forEach(property => {
      stats.byType[property.type] = (stats.byType[property.type] || 0) + 1;
    });

    // إحصائيات حسب الحالة
    properties.forEach(property => {
      stats.byStatus[property.status] = (stats.byStatus[property.status] || 0) + 1;
    });

    // حساب القيمة الإجمالية والمتوسط
    const totalValue = properties.reduce((sum, property) => {
      const price = parseFloat(property.price.replace(/,/g, ''));
      return sum + (isNaN(price) ? 0 : price);
    }, 0);

    stats.totalValue = totalValue;
    stats.averagePrice = properties.length > 0 ? totalValue / properties.length : 0;

    return stats;
  }

  // تهيئة البيانات الأولية
  async initializeDefaultProperties(): Promise<void> {
    const existingProperties = await this.getAllProperties();
    
    if (existingProperties.length === 0) {
      const defaultProperties: Omit<Property, 'createdAt'>[] = [
        {
          id: "P-001",
          title: "فيلا فاخرة في حي الشاطئ",
          type: "فيلا",
          location: "المدينة المنورة - حي الشاطئ",
          area: 450,
          price: "2,500,000",
          bedrooms: 5,
          bathrooms: 6,
          status: "متاح",
        },
        {
          id: "P-002",
          title: "شقة مفروشة في العزيزية",
          type: "شقة",
          location: "المدينة المنورة - العزيزية",
          area: 120,
          price: "850,000",
          bedrooms: 3,
          bathrooms: 2,
          status: "تم البيع",
        },
        {
          id: "P-003",
          title: "أرض تجارية في طريق الملك عبدالله",
          type: "أرض",
          location: "المدينة المنورة - طريق الملك عبدالله",
          area: 1200,
          price: "4,000,000",
          bedrooms: null,
          bathrooms: null,
          status: "متاح",
        },
        {
          id: "P-004",
          title: "فيلا دوبلكس في حي النخيل",
          type: "فيلا",
          location: "المدينة المنورة - حي النخيل",
          area: 380,
          price: "1,950,000",
          bedrooms: 4,
          bathrooms: 5,
          status: "تحت العرض",
        },
        {
          id: "P-005",
          title: "مكتب تجاري في برج الأعمال",
          type: "مكتب",
          location: "المدينة المنورة - شارع الأمير محمد",
          area: 85,
          price: "750,000",
          bedrooms: null,
          bathrooms: 1,
          status: "متاح",
        },
        {
          id: "P-006",
          title: "شقة في حي قباء",
          type: "شقة",
          location: "المدينة المنورة - حي قباء",
          area: 140,
          price: "920,000",
          bedrooms: 3,
          bathrooms: 3,
          status: "متاح",
        },
      ];

      for (const property of defaultProperties) {
        await this.addProperty(property);
      }
    }
  }

  // إنشاء معرف فريد للعقار
  async generatePropertyId(): Promise<string> {
    const properties = await this.getAllProperties();
    const maxId = properties.reduce((max, property) => {
      const idNumber = parseInt(property.id.replace('P-', ''));
      return Math.max(max, isNaN(idNumber) ? 0 : idNumber);
    }, 0);
    
    return `P-${String(maxId + 1).padStart(3, '0')}`;
  }
}

export const propertyService = new PropertyService();
