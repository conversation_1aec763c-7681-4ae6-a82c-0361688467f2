<!DOCTYPE html>
<html lang="ar">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>نظام إدارة العقارات - رافعة المدينة</title>
    <link href="https://fonts.googleapis.com/css2?family=Tajawal:wght@300;400;500;700&display=swap" rel="stylesheet">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Tajawal', sans-serif;
            direction: rtl;
            background: #f5f5f5;
            color: #333;
        }

        .header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 2rem;
            text-align: center;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }

        .header h1 {
            font-size: 2.5rem;
            margin-bottom: 0.5rem;
        }

        .header p {
            font-size: 1.2rem;
            opacity: 0.9;
        }

        .nav-tabs {
            background: white;
            padding: 1rem;
            box-shadow: 0 2px 5px rgba(0,0,0,0.1);
            display: flex;
            gap: 1rem;
            overflow-x: auto;
        }

        .nav-tab {
            padding: 0.8rem 1.5rem;
            background: #f8f9fa;
            border: none;
            border-radius: 8px;
            cursor: pointer;
            transition: all 0.3s;
            white-space: nowrap;
            font-family: inherit;
            font-size: 1rem;
        }

        .nav-tab.active {
            background: #667eea;
            color: white;
        }

        .nav-tab:hover {
            background: #5a67d8;
            color: white;
        }

        .content {
            padding: 2rem;
            max-width: 1200px;
            margin: 0 auto;
        }

        .page {
            display: none;
            animation: fadeIn 0.3s ease-in;
        }

        .page.active {
            display: block;
        }

        @keyframes fadeIn {
            from { opacity: 0; transform: translateY(20px); }
            to { opacity: 1; transform: translateY(0); }
        }

        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 1.5rem;
            margin-bottom: 2rem;
        }

        .stat-card {
            background: white;
            padding: 1.5rem;
            border-radius: 12px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            text-align: center;
        }

        .stat-card h3 {
            color: #667eea;
            font-size: 2rem;
            margin-bottom: 0.5rem;
        }

        .stat-card p {
            color: #666;
            font-size: 1rem;
        }

        .form-container {
            background: white;
            padding: 2rem;
            border-radius: 12px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            margin-bottom: 2rem;
        }

        .form-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 1rem;
            margin-bottom: 1.5rem;
        }

        .form-group {
            display: flex;
            flex-direction: column;
        }

        .form-group label {
            margin-bottom: 0.5rem;
            font-weight: 500;
            color: #333;
        }

        .form-group input,
        .form-group select,
        .form-group textarea {
            padding: 0.8rem;
            border: 2px solid #e2e8f0;
            border-radius: 8px;
            font-family: inherit;
            font-size: 1rem;
            transition: border-color 0.3s;
        }

        .form-group input:focus,
        .form-group select:focus,
        .form-group textarea:focus {
            outline: none;
            border-color: #667eea;
        }

        .btn {
            padding: 0.8rem 1.5rem;
            border: none;
            border-radius: 8px;
            cursor: pointer;
            font-family: inherit;
            font-size: 1rem;
            transition: all 0.3s;
        }

        .btn-primary {
            background: #667eea;
            color: white;
        }

        .btn-primary:hover {
            background: #5a67d8;
        }

        .btn-danger {
            background: #e53e3e;
            color: white;
        }

        .btn-danger:hover {
            background: #c53030;
        }

        .btn-secondary {
            background: #718096;
            color: white;
        }

        .btn-secondary:hover {
            background: #4a5568;
        }

        .table-container {
            background: white;
            border-radius: 12px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            overflow: hidden;
        }

        .table {
            width: 100%;
            border-collapse: collapse;
        }

        .table th,
        .table td {
            padding: 1rem;
            text-align: right;
            border-bottom: 1px solid #e2e8f0;
        }

        .table th {
            background: #f8f9fa;
            font-weight: 600;
            color: #4a5568;
        }

        .table tr:hover {
            background: #f8f9fa;
        }

        .badge {
            padding: 0.3rem 0.8rem;
            border-radius: 20px;
            font-size: 0.8rem;
            font-weight: 500;
        }

        .badge-success {
            background: #c6f6d5;
            color: #22543d;
        }

        .badge-warning {
            background: #fef5e7;
            color: #744210;
        }

        .badge-danger {
            background: #fed7d7;
            color: #742a2a;
        }

        .welcome-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 2rem;
            margin-top: 2rem;
        }

        .welcome-card {
            background: white;
            padding: 2rem;
            border-radius: 15px;
            box-shadow: 0 5px 20px rgba(0,0,0,0.1);
            text-align: center;
            cursor: pointer;
            transition: transform 0.3s ease;
        }

        .welcome-card:hover {
            transform: translateY(-5px);
        }

        .welcome-card-icon {
            width: 60px;
            height: 60px;
            margin: 0 auto 1rem;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 1.5rem;
        }

        .alert {
            padding: 1rem;
            border-radius: 8px;
            margin-bottom: 1rem;
        }

        .alert-success {
            background: #c6f6d5;
            color: #22543d;
            border: 1px solid #9ae6b4;
        }

        .alert-info {
            background: #bee3f8;
            color: #2a4365;
            border: 1px solid #90cdf4;
        }

        .empty-state {
            text-align: center;
            padding: 3rem;
            color: #718096;
        }

        .empty-state svg {
            width: 64px;
            height: 64px;
            margin-bottom: 1rem;
            opacity: 0.5;
        }

        @media (max-width: 768px) {
            .header h1 {
                font-size: 2rem;
            }
            
            .nav-tabs {
                padding: 0.5rem;
            }
            
            .content {
                padding: 1rem;
            }
            
            .form-grid {
                grid-template-columns: 1fr;
            }
        }
    </style>
</head>
<body>
    <!-- Header -->
    <div class="header">
        <h1>🏢 نظام إدارة العقارات</h1>
        <p>شركة رافعة المدينة للتطوير العقاري</p>
    </div>

    <!-- Navigation -->
    <div class="nav-tabs">
        <button class="nav-tab active" onclick="showPage('dashboard')">🏠 الرئيسية</button>
        <button class="nav-tab" onclick="showPage('properties')">🏢 العقارات</button>
        <button class="nav-tab" onclick="showPage('users')">👥 المستخدمين</button>
        <button class="nav-tab" onclick="showPage('invoices')">📄 المستخلصات</button>
        <button class="nav-tab" onclick="showPage('suppliers')">🏭 الموردين</button>
        <button class="nav-tab" onclick="showPage('procurement')">🛒 المشتريات</button>
        <button class="nav-tab" onclick="showPage('settings')">⚙️ الإعدادات</button>
    </div>

    <!-- Content -->
    <div class="content">
        <!-- Dashboard Page -->
        <div id="dashboard" class="page active">
            <div class="alert alert-success">
                <strong>مرحباً بك!</strong> نظام إدارة العقارات يعمل بشكل مثالي. جميع البيانات محفوظة محلياً في متصفحك.
            </div>

            <div class="stats-grid">
                <div class="stat-card">
                    <h3 id="total-properties">0</h3>
                    <p>إجمالي العقارات</p>
                </div>
                <div class="stat-card">
                    <h3 id="total-users">0</h3>
                    <p>المستخدمين</p>
                </div>
                <div class="stat-card">
                    <h3 id="total-invoices">0</h3>
                    <p>المستخلصات</p>
                </div>
                <div class="stat-card">
                    <h3 id="total-suppliers">0</h3>
                    <p>الموردين</p>
                </div>
            </div>

            <div class="welcome-grid">
                <div class="welcome-card" onclick="showPage('properties')">
                    <div class="welcome-card-icon" style="background: linear-gradient(135deg, #667eea, #764ba2); color: white;">🏢</div>
                    <h3>إدارة العقارات</h3>
                    <p>إضافة وإدارة العقارات والوحدات السكنية والتجارية</p>
                </div>
                <div class="welcome-card" onclick="showPage('users')">
                    <div class="welcome-card-icon" style="background: linear-gradient(135deg, #4facfe, #00f2fe); color: white;">👥</div>
                    <h3>إدارة المستخدمين</h3>
                    <p>إضافة وإدارة حسابات المستخدمين والصلاحيات</p>
                </div>
                <div class="welcome-card" onclick="showPage('invoices')">
                    <div class="welcome-card-icon" style="background: linear-gradient(135deg, #fa709a, #fee140); color: white;">📄</div>
                    <h3>المستخلصات</h3>
                    <p>إدارة المستخلصات المالية والفواتير</p>
                </div>
            </div>
        </div>

        <!-- Properties Page -->
        <div id="properties" class="page">
            <h2>إدارة العقارات</h2>
            
            <div class="form-container">
                <h3>إضافة عقار جديد</h3>
                <form id="property-form">
                    <div class="form-grid">
                        <div class="form-group">
                            <label>عنوان العقار *</label>
                            <input type="text" id="property-title" required>
                        </div>
                        <div class="form-group">
                            <label>نوع العقار</label>
                            <select id="property-type">
                                <option value="شقة">شقة</option>
                                <option value="فيلا">فيلا</option>
                                <option value="محل تجاري">محل تجاري</option>
                                <option value="مكتب">مكتب</option>
                                <option value="أرض">أرض</option>
                            </select>
                        </div>
                        <div class="form-group">
                            <label>الموقع</label>
                            <input type="text" id="property-location">
                        </div>
                        <div class="form-group">
                            <label>المساحة (م²)</label>
                            <input type="number" id="property-area">
                        </div>
                        <div class="form-group">
                            <label>السعر (ريال)</label>
                            <input type="number" id="property-price">
                        </div>
                        <div class="form-group">
                            <label>الحالة</label>
                            <select id="property-status">
                                <option value="متاح">متاح</option>
                                <option value="محجوز">محجوز</option>
                                <option value="مباع">مباع</option>
                            </select>
                        </div>
                    </div>
                    <div class="form-group">
                        <label>الوصف</label>
                        <textarea id="property-description" rows="3"></textarea>
                    </div>
                    <button type="submit" class="btn btn-primary">إضافة العقار</button>
                </form>
            </div>

            <div class="table-container">
                <table class="table">
                    <thead>
                        <tr>
                            <th>العنوان</th>
                            <th>النوع</th>
                            <th>الموقع</th>
                            <th>المساحة</th>
                            <th>السعر</th>
                            <th>الحالة</th>
                            <th>الإجراءات</th>
                        </tr>
                    </thead>
                    <tbody id="properties-table">
                        <!-- Properties will be loaded here -->
                    </tbody>
                </table>
            </div>
        </div>

        <!-- Users Page -->
        <div id="users" class="page">
            <h2>إدارة المستخدمين</h2>
            
            <div class="form-container">
                <h3>إضافة مستخدم جديد</h3>
                <form id="user-form">
                    <div class="form-grid">
                        <div class="form-group">
                            <label>الاسم *</label>
                            <input type="text" id="user-name" required>
                        </div>
                        <div class="form-group">
                            <label>البريد الإلكتروني *</label>
                            <input type="email" id="user-email" required>
                        </div>
                        <div class="form-group">
                            <label>رقم الهاتف</label>
                            <input type="tel" id="user-phone">
                        </div>
                        <div class="form-group">
                            <label>الدور الوظيفي</label>
                            <select id="user-role">
                                <option value="مدير">مدير</option>
                                <option value="موظف مبيعات">موظف مبيعات</option>
                                <option value="محاسب">محاسب</option>
                                <option value="مشرف">مشرف</option>
                            </select>
                        </div>
                        <div class="form-group">
                            <label>القسم</label>
                            <input type="text" id="user-department">
                        </div>
                        <div class="form-group">
                            <label>الحالة</label>
                            <select id="user-status">
                                <option value="نشط">نشط</option>
                                <option value="غير نشط">غير نشط</option>
                            </select>
                        </div>
                    </div>
                    <button type="submit" class="btn btn-primary">إضافة المستخدم</button>
                </form>
            </div>

            <div class="table-container">
                <table class="table">
                    <thead>
                        <tr>
                            <th>الاسم</th>
                            <th>البريد الإلكتروني</th>
                            <th>الهاتف</th>
                            <th>الدور</th>
                            <th>القسم</th>
                            <th>الحالة</th>
                            <th>الإجراءات</th>
                        </tr>
                    </thead>
                    <tbody id="users-table">
                        <!-- Users will be loaded here -->
                    </tbody>
                </table>
            </div>
        </div>
